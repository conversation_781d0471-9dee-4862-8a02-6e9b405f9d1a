package com.tt.cloud.controller;

import com.alibaba.fastjson.JSONObject;
import com.tt.cloud.bean.AwsCloudResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.HuaweiCloudResponse;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.bean.SsoEntity;
import com.tt.cloud.bean.TmpToken;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.UserInfo;
import com.tt.cloud.bean.VerifyTokenRequest;
import com.tt.cloud.bean.VerifyTokenResponse;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.handler.ali.AliCloudUserHandler;
import com.tt.cloud.handler.huawei.HuaweiCloudUserHandler;
import com.tt.cloud.handler.tencent.TencentCloudUserHandler;
import com.tt.cloud.service.UserServiceImpl;
import com.tt.cloud.util.CommonUtil;
import com.tt.cloud.util.EncryptUtils;
import com.tt.cloud.util.HttpUtils;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringReader;
import java.security.PrivateKey;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import java.net.URLEncoder;
import java.net.URL;
import java.net.URLConnection;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.services.securitytoken.AWSSecurityTokenServiceClient;
import com.amazonaws.services.securitytoken.model.Credentials;
import com.amazonaws.services.securitytoken.model.GetFederationTokenRequest;
import com.amazonaws.services.securitytoken.model.GetFederationTokenResult;

/**
 * 首页控制器
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2021/9/6 11:14
 */
@Slf4j
@RestController
public class SsoController {
  @Value("${tt.op.home_url}")
  private String homeUrl;
  @Value("${sso.client_secret}")
  private String clientSecret;
  @Value("${sso.validate_api}")
  private String validateApi;
  @Value("${sso.private_key}")
  private String privateKey;
  @Value("${sso.logout_api}")
  private String logOutUrl;
  @Resource
  private HttpUtils httpUtils;
  @Resource
  private CloudAccountDao cloudAccountDao;
  @Resource
  private UserServiceImpl userService;
  @Resource
  private TencentCloudUserHandler tencentCloudUserHandler;
  @Resource
  private HuaweiCloudUserHandler huaweiCloudUserHandler;
  @Resource
  private AliCloudUserHandler aliCloudUserHandler;

  /**
   * 获取aws登录连接
   *
   * @param permissionEntity permissionEntity
   * @return AwsCloudResponse
   */
  @PostMapping("/aws/login/path")
  public AwsCloudResponse queryAwsLoginPath(@RequestBody PermissionEntity permissionEntity) {
    AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
    try {
      CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
      AWSSecurityTokenServiceClient stsClient = new AWSSecurityTokenServiceClient(new AWSCredentials() {
        @Override
        public String getAWSAccessKeyId() {return cloudAccountEntity.getSecretId();}
        @Override
        public String getAWSSecretKey() {
          return EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccountEntity.getSecretKey());
        }
      });
      GetFederationTokenRequest getFederationTokenRequest = new GetFederationTokenRequest();
      getFederationTokenRequest.setDurationSeconds(1800);
      getFederationTokenRequest.setName(permissionEntity.getUserName());
      getFederationTokenRequest.setPolicyArns(permissionEntity.getPolicyArns());
      GetFederationTokenResult federationTokenResult = stsClient.getFederationToken(getFederationTokenRequest);
      Credentials federatedCredentials = federationTokenResult.getCredentials();
      String issuerURL = "https://mysignin.internal.mycompany.com/";
      String signInURL = "https://signin.aws.amazon.com/federation";
      String sessionJson = String.format(
              "{\"%1$s\":\"%2$s\",\"%3$s\":\"%4$s\",\"%5$s\":\"%6$s\"}",
              "sessionId", federatedCredentials.getAccessKeyId(),
              "sessionKey", federatedCredentials.getSecretAccessKey(),
              "sessionToken", federatedCredentials.getSessionToken());
      String getSigninTokenURL = signInURL +
              "?Action=getSigninToken" +
              "&DurationSeconds=43200" +
              "&SessionType=json&Session=" +
              URLEncoder.encode(sessionJson,"UTF-8");
      URL url = new URL(getSigninTokenURL);
      URLConnection conn = url.openConnection ();
      BufferedReader bufferReader = new BufferedReader(new
              InputStreamReader(conn.getInputStream()));
      String returnContent = bufferReader.readLine();
      String signinToken = JSONObject.parseObject(returnContent).getString("SigninToken");
      String signinTokenParameter = "&SigninToken=" + URLEncoder.encode(signinToken,"UTF-8");
      String issuerParameter = "&Issuer=" + URLEncoder.encode(issuerURL, "UTF-8");
      String destinationParameter = "&Destination=" + URLEncoder.encode(cloudAccountEntity.getDestination(),"UTF-8");
      awsCloudResponse.setLogInPath(signInURL + "?Action=login" +
              signinTokenParameter + issuerParameter + destinationParameter);
    } catch (IOException e) {
      log.error("组装aws用户登录连接异常", e);
      awsCloudResponse.getHeader().setErrorMsg("组装aws用户登录连接异常:" + e.getMessage());
    }
    return awsCloudResponse;
  }


  @GetMapping("/rest/v1/currentUser")
  public UserInfo queryCurrentUser() {
    UserInfo userInfo = UserContext.getUserInfoThreadLocal();
    userInfo.setAuthorization(StringUtils.EMPTY);
    return userInfo;
  }

  @GetMapping("/rest/v1/verify/token")
  public VerifyTokenResponse verifyToken(VerifyTokenRequest verifyTokenRequest, HttpServletResponse response) {
    VerifyTokenResponse verifyTokenResponse = new VerifyTokenResponse();
    try {
      TmpToken tmpToken = userService.queryTmpTokenByToken(verifyTokenRequest.getToken());
      if (null != tmpToken) {
        String authorization = CommonUtil.parseTmpToken(tmpToken.getToken(), clientSecret);
        userService.removeTmpTokenById(tmpToken.getId());
        UserInfo userInfo = userService.queryUserInfo(CommonUtil.verifyToken(authorization, clientSecret));
        if (null != userInfo) {
          authorization = CommonUtil.createToken(userInfo.getUserId(),6 * 60 * 60 * 1000L, clientSecret);
          CommonUtil.addCookie(response, "Authorization", authorization);
          setLogInUrl(verifyTokenRequest, verifyTokenResponse, userInfo);
        }
      }
    } catch (Exception e) {
      log.error("验证临时token异常", e);
    }
    return verifyTokenResponse;
  }

  private void setLogInUrl(VerifyTokenRequest verifyTokenRequest,
          VerifyTokenResponse verifyTokenResponse, UserInfo userInfo) {
    if (StringUtils.isNotEmpty(verifyTokenRequest.getDomainId())) {
      CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountByDomainId(
              verifyTokenRequest.getDomainId());
      if (null != cloudAccount) {
        UserEntity userEntity = new UserEntity();
        userEntity.setUserName(userInfo.getUserId());
        userEntity.setAccountId(cloudAccount.getId());
         if (Constants.HUAWEI_CLOUD.equals(cloudAccount.getCloudType())) {
           HuaweiCloudResponse huaweiCloudResponse = huaweiCloudUserHandler.queryUserExists(userEntity);
           if (Constants.SUCCESS.equals(huaweiCloudResponse.getHeader().getCode())) {
             verifyTokenResponse.setUrl(huaweiCloudResponse.getLoginUrl());
           }
         }
         if (Constants.TENCENT_CLOUD.equals(cloudAccount.getCloudType())) {
           
         }
      }
    }
  }

  @GetMapping("/rest/v1/logout")
  public String logout(HttpServletResponse response) {
    CommonUtil.addCookie(response, "Authorization", StringUtils.EMPTY);
    UserContext.clearUserInfoThreadLocal();
    return logOutUrl;
  }

  @GetMapping("/auth/sso-ticket-auth/")
  public void getTicket(SsoEntity ssoEntity, HttpServletResponse response) {
    try {
      String ticket = ssoEntity.getTicket().replaceAll(" ", "+");
      log.info("ticket={}", ticket);
      String result = httpUtils.doPost(validateApi, "{\"ticket\": \"" + ticket + "\"}", new ArrayList<>());
      log.info("登录验证结果={}", result);
      JSONObject jsonObject = JSONObject.parseObject(result);
      parseVerifyResult(jsonObject, response);
      response.sendRedirect(homeUrl);
    } catch (Exception e) {
      log.info("单点登录异常", e);
    }
  }

  private void parseVerifyResult(JSONObject jsonObject, HttpServletResponse response)
          throws Exception {
    Object data = jsonObject.get("data");
    Object signature = jsonObject.get("signature");
    if (null != data && null != signature) {
      String ciphertext = JSONObject.parseObject(data.toString()).getString("ciphertext");
      Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA1AndMGF1Padding", "BC");
      cipher.init(Cipher.DECRYPT_MODE, getPrivateKey(privateKey));
      byte[] deBytes = cipher.doFinal(Base64.decodeBase64(ciphertext));
      List<String> params = Arrays.asList(new String(deBytes).split(":"));
      log.info("params={}", params);
      if (params.size() == 5) {
        String token = CommonUtil.createToken(params.get(3),6 * 60 * 60 * 1000L, clientSecret);
        CommonUtil.addCookie(response, "Authorization", token);
      }
    }
  }

  private static PrivateKey getPrivateKey(String privateKey) throws Exception {
    Reader privateKeyReader = new StringReader(privateKey);
    PEMParser privatePemParser = new PEMParser(privateKeyReader);
    Object privateObject = privatePemParser.readObject();
    if (privateObject instanceof PEMKeyPair) {
      PEMKeyPair pemKeyPair = (PEMKeyPair) privateObject;
      JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");
      return converter.getPrivateKey(pemKeyPair.getPrivateKeyInfo());
    }
    return null;
  }
}
