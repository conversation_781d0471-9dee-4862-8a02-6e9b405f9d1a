<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--上面2行的是约束依赖，固定照抄就好-->
<!--下面的才是要自己编写的地方-->
<!--写mapper的配置文件第一步就是要写<mapper></mapper>标签-->
<!--<mapper></mapper>标签里包含着各个CURD操作的SQL语句-->
<mapper namespace="com.tt.cloud.dao.LBDao">

  <insert id="insertCloudLoadBalance" useGeneratedKeys="true">
    insert into tt_cloud_lb_info(loadbalancer_name, loadBalance_type, trackid,
                                 projectid, net_type, bandwidth_size, vpc_id, subnet_id,
                                 l7_flavor_id, l4_flavor_id, security_groups, az, listener_list)
    values (#{loadbalancer_name}, #{loadBalance_type}, #{trackId}, #{projectId},
            #{net_type}, #{bandwidth_size}, #{vpc_id}, #{subnet_id}, #{l7_flavor_id},
            #{l4_flavor_id}, #{security_groups}, #{az}, #{listener_list})
  </insert>

</mapper>
