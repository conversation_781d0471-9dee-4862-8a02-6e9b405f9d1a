package com.tt.cloud.sso.saml;

import com.tt.cloud.bean.v2.Result;
import com.tt.cloud.sso.metadata.MetadataBean;
import com.tt.cloud.sso.metadata.MetadataConstants;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.app.VelocityEngine;
import org.opensaml.common.binding.BasicSAMLMessageContext;
import org.opensaml.saml2.binding.encoding.HTTPPostSimpleSignEncoder;
import org.opensaml.saml2.core.Assertion;
import org.opensaml.saml2.core.AuthnRequest;
import org.opensaml.saml2.core.Response;
import org.opensaml.saml2.metadata.SingleSignOnService;
import org.opensaml.ws.transport.http.HttpServletResponseAdapter;
import org.opensaml.xml.security.credential.Credential;

@Slf4j
public class SAMLResponseResolution {
    private static final MetadataBean idpMetadata = SAMLResponse.getIdpMetadata();

    public void send(AuthnRequest authnRequest, HttpServletResponse httpServletResponse, HttpServletRequest request, Response response)
            throws Exception {
        try {
            VelocityEngine velocityEngine = new VelocityEngine();
            velocityEngine.setProperty("ISO-8859-1", "UTF-8");
            velocityEngine.setProperty("output.encoding", "UTF-8");
            velocityEngine.setProperty("resource.loader", "classpath");
            velocityEngine.setProperty("classpath.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
            velocityEngine.setProperty("runtime.log.logsystem.class", "org.apache.velocity.runtime.log.NullLogChute");
            velocityEngine.init();
            HTTPPostSimpleSignEncoder httpPostSimpleSignEncoder = new HTTPPostSimpleSignEncoder(velocityEngine,
                    "/templates/saml2-post-simplesign-binding.vm", false);
            HttpServletResponseAdapter outTransport = new HttpServletResponseAdapter(httpServletResponse, false);
            BasicSAMLMessageContext<?, Response, ?> messageContext = new BasicSAMLMessageContext<>();
            messageContext.setOutboundMessageTransport(outTransport);
            messageContext.setOutboundSAMLMessage(response);
            messageContext.setOutboundMessageIssuer(idpMetadata.getEntityId());
            SingleSignOnService endpoint = SAMLResponse.buildSAMLObject(SingleSignOnService.class);
            endpoint.setLocation(authnRequest.getAssertionConsumerServiceURL());
            endpoint.setBinding(MetadataConstants.HTTP_POST_BINDING);
            messageContext.setPeerEntityEndpoint(endpoint);
            messageContext.setRelayState(request.getParameter(AuthnRequestParameterConst.RELAY_STATE));
            httpPostSimpleSignEncoder.encode(messageContext);
        } catch (Exception e) {
            log.error("发送认证信息失败", e);
            throw new Exception("发送认证信息失败");
        }
    }

    public void signAssertion(Response response) throws Exception {
        try {
            Assertion assertion = response.getAssertions().get(0);
            Credential credential = idpMetadata.getSigningCredList().get(0);
            SignatureMethod.signObject(assertion, credential);
        } catch (Exception e) {
            log.error("签名失败!", e);
            throw new Exception("签名失败");
        }
    }

    public Response createSamlResponse(AuthnRequest authnRequest, String cloud, Result result) {
        Assertion assertion = SAMLResponse.buildAssertion(authnRequest, cloud, result);
        return SAMLResponse.buildResponse(authnRequest, assertion);
    }
















}
