<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--上面2行的是约束依赖，固定照抄就好-->
<!--下面的才是要自己编写的地方-->
<!--写mapper的配置文件第一步就是要写<mapper></mapper>标签-->
<!--<mapper></mapper>标签里包含着各个CURD操作的SQL语句-->
<mapper namespace="com.tt.cloud.dao.ResourceChangeDao">

  <insert id="insertResourceChange">
    insert into tt_cloud_resource_change_log(changeid, cloudid, `before`, after, resourcetype, resourceid, resourcename,
                                             changetime, creatorid, changetype, source, trackid)
    VALUES (#{changeId}, #{cloudId}, #{before}, #{after}, #{resourceType}, #{resourceId}, #{resourceName}, #{changeTime},
            #{creatorId}, #{changeType}, #{source}, #{trackId})
  </insert>

</mapper>
