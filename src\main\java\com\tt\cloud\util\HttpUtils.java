package com.tt.cloud.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HttpUtils {
  @Resource
  private CloseableHttpClient httpClient;

  @Resource private RequestConfig config;

  /**
   * 带参数的post请求
   *
   * @param url url
   * @param jsonString jsonString
   * @return Striarams jsonParams
   * @throws Exception Exception
   */
  public String doPost(String url, String jsonString, List<Header> headers) throws Exception {
    try {
      // 声明httpPost请求
      HttpPost httpPost = new HttpPost(url);
      // 加入配置信息
      httpPost.setConfig(config);
      if (StringUtils.isNotEmpty(jsonString)) {
        httpPost.addHeader("Content-type", "application/json");
        httpPost.setHeader("Accept", "application/json");
        httpPost.setEntity(new StringEntity(jsonString, ContentType.create("application/json")));
      }
      for (Header header : headers) {
        httpPost.addHeader(header);
      }
      getStringBuilder(httpPost.getEntity());
      // 发起请求
      CloseableHttpResponse response = this.httpClient.execute(httpPost);
      // 判断状态码是否为200
      if (response.getStatusLine().getStatusCode() == 200) {
        // 返回响应体的内容
        return EntityUtils.toString(response.getEntity(), "UTF-8");
      } else {
        return getStringBuilder(response.getEntity()).toString();
      }
    } catch (Exception e) {
      throw new Exception("POST请求发送失败", e);
    }
  }

  /**
   * 无参GET请求
   *
   * @param url url
   * @return String
   */
  public String doGet(String url) throws Exception {
    // 创建get请求
    HttpGet httpGet = new HttpGet(url);
    // 设置配置信息
    httpGet.setConfig(config);
    // 发送请求
    try {
      CloseableHttpResponse response = this.httpClient.execute(httpGet);
      // 判断状态码是否为200
      if (response.getStatusLine().getStatusCode() == 200) {
        // 返回响应体的内容
        return EntityUtils.toString(response.getEntity(), "UTF-8");
      }
    } catch (IOException e) {
      throw new Exception("GET请求发送失败", e);
    }
    return null;
  }

  public String doGet(String url, List<Header> headers) throws Exception {
    try {
      // 创建get请求
      HttpGet httpGet = new HttpGet(url);
      // 设置配置信息
      httpGet.setConfig(config);
      for (Header header : headers) {
        httpGet.addHeader(header);
      }
      // 发送请求
      CloseableHttpResponse response = this.httpClient.execute(httpGet);
      // 判断状态码是否为200
      if (response.getStatusLine().getStatusCode() == 200) {
        // 返回响应体的内容
        return EntityUtils.toString(response.getEntity(), "UTF-8");
      }
    } catch (IOException e) {
      throw new Exception("GET请求发送失败", e);
    }
    return null;
  }

  private StringBuilder getStringBuilder(HttpEntity entity) throws IOException {
    BufferedReader in = new BufferedReader(new InputStreamReader(entity.getContent()));
    StringBuilder sb = new StringBuilder();
    String line;
    String NL = System.getProperty("line.separator");
    while ((line = in.readLine()) != null) {
      sb.append(line).append(NL);
    }
    in.close();
    return sb;
  }



}
