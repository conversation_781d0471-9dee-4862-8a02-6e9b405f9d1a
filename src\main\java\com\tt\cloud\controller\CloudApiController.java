package com.tt.cloud.controller;

import com.tt.cloud.bean.AliCloudResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.CloudAddGroupInfo;
import com.tt.cloud.bean.CloudUserAuthRequest;
import com.tt.cloud.bean.CloudUserAuthResponse;
import com.tt.cloud.bean.CloudUserAuthUser;
import com.tt.cloud.bean.GroupEntity;
import com.tt.cloud.bean.HuaweiCloudResponse;
import com.tt.cloud.bean.TencentCloudResponse;
import com.tt.cloud.bean.UserAuthApplyLog;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.UserGroupInfo;
import com.tt.cloud.bean.UserGroupResponse;
import com.tt.cloud.bean.VolacEngineCloudResponse;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.exception.ServiceRuntimeException;
import com.tt.cloud.handler.ali.AliCloudUserGroupHandler;
import com.tt.cloud.handler.ali.AliCloudUserHandler;
import com.tt.cloud.handler.huawei.HuaweiCloudUserGroupHandler;
import com.tt.cloud.handler.huawei.HuaweiCloudUserHandler;
import com.tt.cloud.handler.tencent.TencentCloudUserGroupHandler;
import com.tt.cloud.handler.tencent.TencentCloudUserHandler;
import com.tt.cloud.handler.volcengine.VolcEngineCloudUserHandler;
import com.tt.cloud.handler.ks.KSCloudUserHandler;
import com.tt.cloud.service.UserAuthApplyLogServiceImpl;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.K;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/10 16:35
 */
@Slf4j
@RestController
@RequestMapping("/rest/v1")
public class CloudApiController {
    private static final List<String> CLOUD_LIST = Arrays.asList("huawei","ali","tencent","aws","volcengine");
    @Resource
    private HuaweiCloudUserGroupHandler huaweiCloudUserGroupHandler;
    @Resource
    private TencentCloudUserGroupHandler tencentCloudUserGroupHandler;
    @Resource
    private AliCloudUserGroupHandler aliCloudUserGroupHandler;
    @Resource
    private HuaweiCloudUserHandler huaweiCloudUserHandler;
    @Resource
    private TencentCloudUserHandler tencentCloudUserHandler;
    @Resource
    private AliCloudUserHandler aliCloudUserHandler;
    @Resource
    private UserAuthApplyLogServiceImpl userAuthApplyLogService;
    @Resource CloudAccountDao cloudAccountDao;
    @Resource
    private VolcEngineCloudUserHandler volcEngineCloudUserHandler;
    @Resource
    private KSCloudUserHandler ksCloudUserHandler;

    /**
     * 查询用户组列表
     *
     * @return Object
     */
    @GetMapping("/cloud/group/list")
    public UserGroupResponse queryCloudGroupList() {
        UserGroupResponse userGroupResponse = new UserGroupResponse();
        try {
            List<UserGroupInfo> huaweiGroupList = huaweiCloudUserGroupHandler.queryAllGroupList();
            List<UserGroupInfo> tencentGroupList = tencentCloudUserGroupHandler.queryAllGroupList();
            List<UserGroupInfo> aliGroupList = aliCloudUserGroupHandler.queryAllGroupList();
            // List<UserGroupInfo> volcGroupList = volcEngineCloudUserHandler.queryAllGroupList();
            if (!huaweiGroupList.isEmpty()
                    && !tencentGroupList.isEmpty()
                    && !aliGroupList.isEmpty()) {
                huaweiGroupList = huaweiGroupList.stream().filter(item->
                        tencentGroupList.stream().anyMatch(
                                item1 -> item.getGroupName().equals(item1.getGroupName())
                                        && item.getGroupId().equals(item1.getGroupId()))).collect(
                        Collectors.toList());
                huaweiGroupList = huaweiGroupList.stream().filter(item->
                        aliGroupList.stream().anyMatch(
                                item1 -> item.getGroupName().equals(item1.getGroupName())
                                        && item.getGroupId().equals(item1.getGroupId()))).collect(Collectors.toList());
                // huaweiGroupList = huaweiGroupList.stream().filter(item->
                //         volcGroupList.stream().anyMatch(
                //                 item1 -> item.getGroupName().equals(item1.getGroupName())
                //                         && item.getGroupId().equals(item1.getGroupId()))).collect(Collectors.toList());
                userGroupResponse.setGroupList(huaweiGroupList);
            }
        } catch (Exception e) {
            log.info("查询用户组列表异常", e);
            userGroupResponse.getHeader().setErrorMsg("查询用户组列表异常：" + e.getMessage());
        }
        return userGroupResponse;
    }

    /**
     * 为用户增加权限
     *
     * @param cloudUserAuthRequest  cloudUserAuthRequest
     * @return CloudUserAuthResponse
     */
    @PostMapping("/cloud/user2group")
    public CloudUserAuthResponse addUser2Group(@RequestBody CloudUserAuthRequest cloudUserAuthRequest) {
        CloudUserAuthResponse cloudUserAuthResponse = new CloudUserAuthResponse();
        Date nowTime = new Date();
        try {
            log.info("cloudUserAuthRequest:{}", cloudUserAuthRequest);
            verifyParam(cloudUserAuthRequest);
            cloudUserAuthRequest.getUserInfo().setUserName(cloudUserAuthRequest.getUserInfo().getEmail().split("@")[0]);
            List<CloudAccountEntity> accountEntityList = cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
            for (CloudAddGroupInfo cloudAddGroupInfo : cloudUserAuthRequest.getDataList()) {
                if (CLOUD_LIST.contains(cloudAddGroupInfo.getCloudId().toLowerCase(Locale.ROOT))) {
                    List<CloudAccountEntity> accountList =
                            accountEntityList.stream()
                                    .filter(item -> StringUtils.isNotEmpty(item.getMemo())
                                            && item.getMemo().equals(cloudAddGroupInfo.getCouldName().toLowerCase(Locale.ROOT)))
                                    .collect(Collectors.toList());
                    for (CloudAccountEntity cloudAccount : accountList) {
                        UserEntity userInfo = queryUserInfo(cloudUserAuthRequest.getUserInfo().getUserName(), cloudAddGroupInfo.getCloudId().toLowerCase(
                                Locale.ROOT), cloudAccount.getId());
                        log.info("判断用户是否存在：{},{}", userInfo, cloudAccount);
                        if (null == userInfo) {
                            log.info("用户不存在，创建用户，并绑定用户组");
                            addUserAndBindGroup(cloudUserAuthRequest.getUserInfo(), cloudAddGroupInfo.getGroupList(), cloudAddGroupInfo.getCloudId().toLowerCase(
                                    Locale.ROOT), cloudAccount.getId());
                        } else {
                            log.info("用户存在，绑定用户组");
                            bindUser2Group(userInfo, cloudAddGroupInfo.getGroupList(), cloudAddGroupInfo.getCloudId().toLowerCase(
                                    Locale.ROOT), cloudAccount.getId());
                        }
                    }
                }
            }
            addUserAuthLog(cloudUserAuthRequest, nowTime, Constants.SUCCESS, "授权成功");
        } catch (Exception e) {
            log.error("为用户增加分组权限异常", e);
            cloudUserAuthResponse.getHeader().setErrorMsg("为用户增加分组权限异常：" + e.getMessage());
            if (e instanceof ServiceRuntimeException) {
                cloudUserAuthResponse.getHeader().setErrorMsg(((ServiceRuntimeException) e).getErrorMsg());
            }
            addUserAuthLog(cloudUserAuthRequest, nowTime, Constants.ERROR, e.getMessage());
        }
        return cloudUserAuthResponse;
    }

    private void addUserAuthLog(CloudUserAuthRequest cloudUserAuthRequest,
            Date nowTime, String resultCode, String resultMessage) {
        try {
            for (CloudAddGroupInfo cloudAddGroupInfo : cloudUserAuthRequest.getDataList()) {
                UserAuthApplyLog userAuthApplyLog = new UserAuthApplyLog();
                userAuthApplyLog.setUserId(cloudUserAuthRequest.getUserInfo().getUserName());
                userAuthApplyLog.setCloudId(cloudAddGroupInfo.getCloudId().toLowerCase(Locale.ROOT));
                userAuthApplyLog.setGroupList(String.join(",", cloudAddGroupInfo.getGroupList()));
                userAuthApplyLog.setCreateTime(nowTime);
                userAuthApplyLog.setResultCode(resultCode);
                userAuthApplyLog.setResultMsg(resultMessage);
                userAuthApplyLogService.addUserAuthApplyLog(userAuthApplyLog);
            }
        } catch (Exception e) {
            log.error("新增用户授权日志异常", e);
        }
    }

    private void verifyParam(CloudUserAuthRequest cloudUserAuthRequest) {
        if (null == cloudUserAuthRequest.getDataList() || cloudUserAuthRequest.getDataList().isEmpty()) {
            log.error("云厂商类型为空");
            throw new ServiceRuntimeException("云厂商类型为空");
        }
        if (null == cloudUserAuthRequest.getUserInfo()) {
            log.error("用户信息为空");
            throw new ServiceRuntimeException("用户信息为空");
        }
        if (null == cloudUserAuthRequest.getUserInfo().getUserName()) {
            log.error("用户名称为空");
            throw new ServiceRuntimeException("用户名称为空");
        }
    }

    private void bindUser2Group(UserEntity userInfo, List<String> groupList, String cloud, String accountId) {
        if (Constants.HUAWEI_CLOUD.equals(cloud)) {
            List<String> groupIdList = queryHuaweiGroupIdList(groupList, userInfo.getUserId(), Constants.EDIT, accountId);
            GroupEntity groupEntity = new GroupEntity();
            groupEntity.setUserId(userInfo.getUserId());
            groupEntity.setGroupIdList(groupIdList);
            groupEntity.setAccountId(accountId);
            HuaweiCloudResponse huaweiCloudResponse1 = huaweiCloudUserGroupHandler.keystoneAddUserToGroup(groupEntity);
            if (!Constants.SUCCESS.equals(huaweiCloudResponse1.getHeader().getCode())) {
                throw new ServiceRuntimeException(huaweiCloudResponse1.getHeader().getMsg());
            }
        } else if (Constants.TENCENT_CLOUD.equals(cloud)) {
            List<String> groupIdList = queryTencentGroupIdList(groupList, userInfo.getUserId(), Constants.EDIT, accountId);
            if (!groupIdList.isEmpty()) {
                GroupEntity groupEntity = new GroupEntity();
                groupEntity.setUserId(userInfo.getUserId());
                groupEntity.setGroupIdList(groupIdList);
                groupEntity.setAccountId(accountId);
                TencentCloudResponse tencentCloudResponse1 = tencentCloudUserGroupHandler.addUserToGroup(groupEntity);
                if (!Constants.SUCCESS.equals(tencentCloudResponse1.getHeader().getCode())) {
                    throw new ServiceRuntimeException(tencentCloudResponse1.getHeader().getMsg());
                }
            }
        } else if (Constants.ALI_CLOUD.equals(cloud)) {
            List<String> groupNameList = queryAliGroupNameList(groupList, userInfo.getUserName(), Constants.EDIT, accountId);
            if (!groupNameList.isEmpty()) {
                GroupEntity groupEntity = new GroupEntity();
                groupEntity.setUserName(userInfo.getUserName());
                groupEntity.setGroupNameList(groupNameList);
                groupEntity.setAccountId(accountId);
                AliCloudResponse aliCloudResponse1 = aliCloudUserGroupHandler.addUserToGroup(groupEntity);
                if (!Constants.SUCCESS.equals(aliCloudResponse1.getHeader().getCode())) {
                    throw new ServiceRuntimeException(aliCloudResponse1.getHeader().getMsg());
                }
            }
        } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloud)) {
            List<String> groupNameList = queryVolcGroupNameList(groupList, userInfo.getUserName(), Constants.EDIT, accountId);
            if (!groupNameList.isEmpty()) {
                GroupEntity groupEntity = new GroupEntity();
                groupEntity.setUserName(userInfo.getUserName());
                groupEntity.setGroupNameList(groupNameList);
                groupEntity.setAccountId(accountId);
                VolacEngineCloudResponse volcEngineCloudResponse1 = volcEngineCloudUserHandler.addUserToGroup(groupEntity);
                if (!Constants.SUCCESS.equals(volcEngineCloudResponse1.getHeader().getCode())) {
                    throw new ServiceRuntimeException(volcEngineCloudResponse1.getHeader().getMsg());
                }
            }
        }else if (Constants.KS_CLOUD.equals(cloud)) {
            List<String> groupNameList = queryKSGroupNameList(groupList, userInfo.getUserName(), Constants.EDIT, accountId);
            if (!groupNameList.isEmpty()) {
                GroupEntity groupEntity = new GroupEntity();
                groupEntity.setUserName(userInfo.getUserName());
                groupEntity.setGroupNameList(groupNameList);
                groupEntity.setAccountId(accountId);
                VolacEngineCloudResponse volcEngineCloudResponse1 = ksCloudUserHandler.addUserToGroup(groupEntity);
                if (!Constants.SUCCESS.equals(volcEngineCloudResponse1.getHeader().getCode())) {
                    throw new ServiceRuntimeException(volcEngineCloudResponse1.getHeader().getMsg());
                }
            }
        }
    }

    private List<String> queryAliGroupNameList(List<String> groupList, String userName, String type, String accountId) {
        log.info("查询阿里云用户组：{}", groupList);
        GroupEntity group = new GroupEntity();
        group.setAccountId(accountId);
        AliCloudResponse aliCloudResponse = aliCloudUserGroupHandler.queryGroupList(group);
        if (!Constants.SUCCESS.equals(aliCloudResponse.getHeader().getCode())) {
            throw new ServiceRuntimeException(aliCloudResponse.getHeader().getMsg());
        }
        List<String> groupNameList = aliCloudResponse.getGroupList().stream()
                .map(GroupEntity::getGroupName)
                .filter(groupList::contains).collect(Collectors.toList());
        if (!groupNameList.isEmpty() && !Constants.ADD.equals(type)) {
            GroupEntity groupEntity = new GroupEntity();
            groupEntity.setUserName(userName);
            groupEntity.setAccountId(accountId);
            AliCloudResponse aliCloudResponse1
                    = aliCloudUserGroupHandler.queryListGroupsForUser(groupEntity);
            if (!aliCloudResponse1.getGroupList().isEmpty()) {
                groupNameList = groupNameList.stream()
                        .filter(item-> !aliCloudResponse1.getGroupList()
                                .stream().map(GroupEntity::getGroupName).collect(
                                        Collectors.toList()).contains(item)).collect(Collectors.toList());
            }
        }
        return groupNameList;
    }

    private void addUserAndBindGroup(CloudUserAuthUser userInfo, List<String> groupList, String cloud, String accountId) {
        UserEntity userEntity = getUserEntity(userInfo);
        userEntity.setAccountId(accountId);
        if (Constants.HUAWEI_CLOUD.equals(cloud)) {
            List<String> groupIdList = queryHuaweiGroupIdList(groupList, userEntity.getUserId(), Constants.ADD, accountId);
            userEntity.setGroupIdList(groupIdList);
            HuaweiCloudResponse huaweiCloudResponse1 = huaweiCloudUserHandler.createUser(userEntity);
            if (!Constants.SUCCESS.equals(huaweiCloudResponse1.getHeader().getCode())) {
                throw new ServiceRuntimeException(huaweiCloudResponse1.getHeader().getMsg());
            }
        } else if (Constants.TENCENT_CLOUD.equals(cloud)) {
            List<String> groupIdList = queryTencentGroupIdList(groupList, userEntity.getUserId(), Constants.ADD, accountId);
            userEntity.setGroupIdList(groupIdList);
            TencentCloudResponse tencentCloudResponse1 = tencentCloudUserHandler.addUser(userEntity);
            if (!Constants.SUCCESS.equals(tencentCloudResponse1.getHeader().getCode())) {
                throw new ServiceRuntimeException(tencentCloudResponse1.getHeader().getMsg());
            }
        } else if (Constants.ALI_CLOUD.equals(cloud)) {
            List<String> groupNameList = queryAliGroupNameList(groupList, userEntity.getUserName(), Constants.ADD, accountId);
            userEntity.setGroupNameList(groupNameList);
            AliCloudResponse aliCloudResponse1 = aliCloudUserHandler.createUser(userEntity);
            if (!Constants.SUCCESS.equals(aliCloudResponse1.getHeader().getCode())) {
                throw new ServiceRuntimeException(aliCloudResponse1.getHeader().getMsg());
            }
        } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloud)) {
            List<String> groupNameList = queryVolcGroupNameList(groupList, userEntity.getUserName(), Constants.ADD, accountId);
            userEntity.setGroupNameList(groupNameList);
            VolacEngineCloudResponse vcCloudResponse1 = volcEngineCloudUserHandler.createUser(userEntity);
            if (!Constants.SUCCESS.equals(vcCloudResponse1.getHeader().getCode())) {
                throw new ServiceRuntimeException(vcCloudResponse1.getHeader().getMsg());
            }
        }else if (Constants.KS_CLOUD.equals(cloud)) {
            List<String> groupNameList = queryKSGroupNameList(groupList, userEntity.getUserName(), Constants.ADD, accountId);
            userEntity.setGroupNameList(groupNameList);
            VolacEngineCloudResponse vcCloudResponse1 = ksCloudUserHandler.createUser(userEntity);
            if (!Constants.SUCCESS.equals(vcCloudResponse1.getHeader().getCode())) {
                throw new ServiceRuntimeException(vcCloudResponse1.getHeader().getMsg());
            }
        }
    }

    private List<String> queryTencentGroupIdList(List<String> groupList, String userId, String type, String accountId) {
        log.info("查询腾讯云用户组：{}", groupList);
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setAccountId(accountId);
        TencentCloudResponse tencentCloudResponse = tencentCloudUserGroupHandler.queryListGroups(groupEntity);
        if (!Constants.SUCCESS.equals(tencentCloudResponse.getHeader().getCode())) {
            throw new ServiceRuntimeException(tencentCloudResponse.getHeader().getMsg());
        }
        List<String> groupIdList = tencentCloudResponse.getGroupList().stream()
                .filter(item -> groupList.contains(item.getGroupName()))
                .map(GroupEntity::getGroupId).collect(Collectors.toList());
        if (!groupIdList.isEmpty() && !Constants.ADD.equals(type)) {
            GroupEntity group = new GroupEntity();
            group.setUserId(userId);
            group.setAccountId(accountId);
            TencentCloudResponse tencentCloudResponse1 = tencentCloudUserGroupHandler.queryListGroupsForUser(group);
            if (null != tencentCloudResponse1.getGroupList() && !tencentCloudResponse1.getGroupList().isEmpty()) {
                groupIdList = groupIdList.stream()
                        .filter(item -> !tencentCloudResponse1.getGroupList().stream()
                                .map(GroupEntity::getGroupId)
                                .collect(Collectors.toList())
                                .contains(item)).collect(Collectors.toList());
            }
        }
        return groupIdList;
    }

    private List<String> queryHuaweiGroupIdList(List<String> groupList, String userId, String type, String accountId) {
        log.info("查询华为云用户组：{}", groupList);

        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setAccountId(accountId);

        HuaweiCloudResponse huaweiCloudResponse =
                huaweiCloudUserGroupHandler.keystoneListGroups(groupEntity);
        if (!Constants.SUCCESS.equals(huaweiCloudResponse.getHeader().getCode())) {
            throw new ServiceRuntimeException(huaweiCloudResponse.getHeader().getMsg());
        }
        List<String> groupIdList = huaweiCloudResponse.getGroupList()
                .stream()
                .filter(item -> groupList.contains(item.getGroupName()))
                .map(GroupEntity::getGroupId).collect(Collectors.toList());
        if (!groupIdList.isEmpty() && !Constants.ADD.equals(type)) {
            GroupEntity group = new GroupEntity();
            group.setUserId(userId);
            group.setAccountId(accountId);
            HuaweiCloudResponse huaweiCloudResponse1 = huaweiCloudUserGroupHandler.keystoneListGroupsForUser(group);
            if (null != huaweiCloudResponse1.getGroupList() && !huaweiCloudResponse1.getGroupList().isEmpty()) {
                groupIdList = groupIdList.stream().filter(item-> !huaweiCloudResponse1.getGroupList()
                        .stream().map(GroupEntity::getGroupId).collect(
                                Collectors.toList()).contains(item)).collect(Collectors.toList());
            }
        }
        return groupIdList;
    }


    private List<String> queryVolcGroupNameList(List<String> groupList, String userName, String type, String accountId) {
        log.info("查询🌋云用户组：{}", groupList);
        GroupEntity group = new GroupEntity();
        group.setAccountId(accountId);
        VolacEngineCloudResponse aliCloudResponse = volcEngineCloudUserHandler.queryGroupList(group);
        if (!Constants.SUCCESS.equals(aliCloudResponse.getHeader().getCode())) {
            throw new ServiceRuntimeException(aliCloudResponse.getHeader().getMsg());
        }
        List<String> groupNameList = aliCloudResponse.getGroupList().stream()
                .map(GroupEntity::getGroupName)
                .filter(groupList::contains).collect(Collectors.toList());
        if (!groupNameList.isEmpty() && !Constants.ADD.equals(type)) {
            GroupEntity groupEntity = new GroupEntity();
            groupEntity.setUserName(userName);
            groupEntity.setAccountId(accountId);
            AliCloudResponse aliCloudResponse1
                    = volcEngineCloudUserHandler.queryListGroupsForUser(groupEntity);
            if (!aliCloudResponse1.getGroupList().isEmpty()) {
                groupNameList = groupNameList.stream()
                        .filter(item-> !aliCloudResponse1.getGroupList()
                                .stream().map(GroupEntity::getGroupName).collect(
                                        Collectors.toList()).contains(item)).collect(Collectors.toList());
            }
        }
        return groupNameList;
    }

    private List<String> queryKSGroupNameList(List<String> groupList, String userName, String type, String accountId) {
        log.info("查询🌋云用户组：{}", groupList);
        GroupEntity group = new GroupEntity();
        group.setAccountId(accountId);
        VolacEngineCloudResponse aliCloudResponse = ksCloudUserHandler.queryGroupList(group);
        if (!Constants.SUCCESS.equals(aliCloudResponse.getHeader().getCode())) {
            throw new ServiceRuntimeException(aliCloudResponse.getHeader().getMsg());
        }
        List<String> groupNameList = aliCloudResponse.getGroupList().stream()
                .map(GroupEntity::getGroupName)
                .filter(groupList::contains).collect(Collectors.toList());
        if (!groupNameList.isEmpty() && !Constants.ADD.equals(type)) {
            GroupEntity groupEntity = new GroupEntity();
            groupEntity.setUserName(userName);
            groupEntity.setAccountId(accountId);
            AliCloudResponse aliCloudResponse1
                    = ksCloudUserHandler.queryListGroupsForUser(groupEntity);
            if (!aliCloudResponse1.getGroupList().isEmpty()) {
                groupNameList = groupNameList.stream()
                        .filter(item-> !aliCloudResponse1.getGroupList()
                                .stream().map(GroupEntity::getGroupName).collect(
                                        Collectors.toList()).contains(item)).collect(Collectors.toList());
            }
        }
        return groupNameList;
    }

    private UserEntity getUserEntity(CloudUserAuthUser userInfo) {
        UserEntity userEntity = new UserEntity();
        userEntity.setUserName(userInfo.getUserName());
        userEntity.setEmail(userInfo.getEmail());
        userEntity.setPhone(userInfo.getPhone());
        return userEntity;
    }

    private UserEntity queryUserInfo(String userName, String cloud, String accountId) {
        UserEntity userEntity = new UserEntity();
        userEntity.setUserName(userName);
        userEntity.setAccountId(accountId);
        if (Constants.HUAWEI_CLOUD.equals(cloud)) {
            HuaweiCloudResponse huaweiCloudResponse = huaweiCloudUserHandler.queryUserExists(userEntity);
            if (!Constants.SUCCESS.equals(huaweiCloudResponse.getHeader().getCode())) {
                throw new ServiceRuntimeException(huaweiCloudResponse.getHeader().getMsg());
            }
            return huaweiCloudResponse.getUserEntity();
        } else if (Constants.TENCENT_CLOUD.equals(cloud)) {
            TencentCloudResponse tencentCloudResponse = tencentCloudUserHandler.queryUserExists(userEntity);
            if (!Constants.SUCCESS.equals(tencentCloudResponse.getHeader().getCode())) {
                throw new ServiceRuntimeException(tencentCloudResponse.getHeader().getMsg());
            }
            return tencentCloudResponse.getUserEntity();
        } else if (Constants.ALI_CLOUD.equals(cloud)) {
            AliCloudResponse aliCloudResponse = aliCloudUserHandler.queryUserExists(userEntity);
            if (!Constants.SUCCESS.equals(aliCloudResponse.getHeader().getCode())) {
                throw new ServiceRuntimeException(aliCloudResponse.getHeader().getMsg());
            }
            return aliCloudResponse.getUserEntity();
        }else if (Constants.VOLC_ENGINE_CLOUD.equals(cloud)) {
            VolacEngineCloudResponse volcEngineCloudResponse = volcEngineCloudUserHandler.queryUserExists(userEntity);
            if (!Constants.SUCCESS.equals(volcEngineCloudResponse.getHeader().getCode())) {
                throw new ServiceRuntimeException(volcEngineCloudResponse.getHeader().getMsg());
            }
            return volcEngineCloudResponse.getUserEntity();
        }else if (Constants.KS_CLOUD.equals(cloud)) {
            VolacEngineCloudResponse volcEngineCloudResponse = ksCloudUserHandler.queryUserExists(userEntity);
            if (!Constants.SUCCESS.equals(volcEngineCloudResponse.getHeader().getCode())) {
                throw new ServiceRuntimeException(volcEngineCloudResponse.getHeader().getMsg());
            }
            return volcEngineCloudResponse.getUserEntity();
        }
        return null;
    }

}
