package com.tt.cloud.bean;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/23 18:00
 */
@Getter
@Setter
@ToString
public class AwsCloudResponse {
    private ResponseHeader header = new ResponseHeader();
    private String userId;
    private UserEntity userEntity;
    private List<GroupEntity> groupList = new ArrayList<>();
    private List<UserEntity> userList;
    private GroupEntity groupEntity;
    private List<PermissionEntity> permissionList;
    private List<PermanentAccessKeyEntity> accessKeyList;
    private PermanentAccessKeyEntity accessKeyEntity;
    private String policyVersion;
    private String logInPath;
    private List<AwsLoadBalancer> awsLoadBalancerList;
}
