package com.tt.cloud.dao;

import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.UserAuthEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/26 15:09
 */
public interface CloudAccountDao {

    void addCloudAccountInfo(CloudAccountEntity cloudAccountEntity);

    CloudAccountEntity queryCloudAccountInfo(CloudAccountRequest cloudAccountRequest);

    List<CloudAccountEntity> queryCloudAccountList(CloudAccountRequest cloudAccountRequest);

    void editCloudAccountInfo(CloudAccountEntity cloudAccount);

    void deleteCloudAccountInfo(CloudAccountRequest cloudAccountRequest);

    CloudAccountEntity queryCloudAccountInfoById(@Param("id") String id);

    void updateCloudAccountConnectStatus(CloudAccountEntity cloudAccountInfo);

    List<String> queryUserAuthNameList(UserAuthEntity userAuthEntity);

    List<UserAuthEntity> queryUserAuthList(UserAuthEntity userAuthEntity);

    UserAuthEntity queryUserAuthInfoByName(UserAuthEntity userAuthInfo);

    List<CloudAccountEntity> queryResourceManageAccountList(CloudAccountRequest cloudAccountRequest);

    List<UserAuthEntity> queryUserAllAuthList(UserAuthEntity userAuthEntity);

    CloudAccountEntity queryCloudAccountInfoByCloudId(@Param("cloudId") String cloudId);

    CloudAccountEntity queryCloudAccountByDomainId(@Param("domainId") String domainId);
}
