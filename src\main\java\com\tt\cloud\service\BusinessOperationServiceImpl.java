package com.tt.cloud.service;

import com.tt.cloud.bean.BusinessOperationRequest;
import com.tt.cloud.bean.BusinessOperationResponse;
import com.tt.cloud.dao.BusinessOperationDao;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2021/12/22 14:26
 */
@Slf4j
@Component
public class BusinessOperationServiceImpl {
    @Resource
    private BusinessOperationDao businessOperationDao;

    public void addBusinessOperationInfo(BusinessOperationRequest request) {
        BusinessOperationResponse response = new BusinessOperationResponse();
        try {
            businessOperationDao.addBusinessOperationInfo(request.getBusinessOperationLog());
        } catch (Exception e) {
            log.error("新增业务操作日志异常", e);
            response.getHeader().setErrorMsg("新增业务操作日志异常");
        }
    }
}
