package com.tt.cloud.bean;

import com.huaweicloud.sdk.cce.v3.model.Cluster;
import com.huaweicloud.sdk.cce.v3.model.ClusterSpec;
import com.huaweicloud.sdk.eip.v2.model.PublicipShowResp;
import com.huaweicloud.sdk.elb.v3.model.CertificateInfo;
import com.huaweicloud.sdk.elb.v3.model.Flavor;
import com.huaweicloud.sdk.elb.v3.model.IpGroup;
import com.huaweicloud.sdk.elb.v3.model.LoadBalancer;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import com.huaweicloud.sdk.iam.v3.model.CreateUserResult;
import com.huaweicloud.sdk.iam.v3.model.ProjectResult;
import com.huaweicloud.sdk.iam.v3.model.RoleResult;
import com.huaweicloud.sdk.vpc.v2.model.Subnet;
import com.huaweicloud.sdk.vpc.v2.model.Vpc;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 13:52
 */
@Getter
@Setter
@ToString
public class HuaweiCloudResponse {
    private ResponseHeader header = new ResponseHeader();
    private List<UserEntity> userList = new ArrayList<>();
    private List<GroupEntity> groupList = new ArrayList<>();
    private CreateUserResult userResult;
    private GroupEntity groupEntity;
    private UserEntity userEntity;
    private List<PermissionEntity> permissionList;
    private LoginProtectEntity loginProtect;
    private PermanentAccessKeyEntity permanentAccessKeyEntity;
    private List<PermanentAccessKeyEntity> permanentAccessKeyList;
    private List<RoleResult> roleResultList;
    private List<ProjectResult> projectResultList;
    private String loginUrl;
    private int totalCount;
    private List<LoadBalancer> loadBalancers;
    private List<AvailabilityZoneEntity> availabilityZoneList;
    private List<CertificateInfo> certificateInfoList;
    private List<Vpc> vpcList;
    private List<Subnet> subnets;
    private List<PublicipShowResp> publicIps;
    private List<AuthProjectResult> projects;
    private List<IpGroup> ipGroups;
    private List<RegionEntity> regionList;
    private List<ProjectEntity> projectList;
    private List<Flavor> flavors;
    private String trackId;

    private List<EcsFlavorEntity> flavorList;
    private List<String> cpu_core_memory_list;
    private List<Cluster> clusterList;
    private ClusterSpec clusterSpec;
    private String cloudType;
    private List<AvailabilityZoneEntity> azList;
}
