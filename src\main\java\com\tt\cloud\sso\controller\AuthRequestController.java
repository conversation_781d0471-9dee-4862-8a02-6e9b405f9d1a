package com.tt.cloud.sso.controller;

import com.alibaba.fastjson.JSON;
import com.tt.cloud.bean.*;
import com.tt.cloud.bean.v2.CloudSSOLoginLog;
import com.tt.cloud.bean.v2.Result;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.dao.CloudSSODao;
import com.tt.cloud.handler.ali.AliCloudUserHandler;
import com.tt.cloud.handler.tencent.TencentCloudUserHandler;
import com.tt.cloud.handler.volcengine.VolcEngineCloudUserHandler;
import com.tt.cloud.handler.ks.KSCloudUserHandler;
import com.tt.cloud.sso.metadata.MetadataBean;
import com.tt.cloud.sso.saml.AuthnRequestResolution;
import com.tt.cloud.sso.saml.SAMLResponseResolution;
import com.tt.cloud.util.HttpUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opensaml.saml2.core.AuthnRequest;
import org.opensaml.saml2.core.Response;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("rest/v1")
public class AuthRequestController {
    @Resource
    private AliCloudUserHandler aliCloudUserHandler;
    @Resource
    private TencentCloudUserHandler tencentCloudUserHandler;
    @Resource
    private VolcEngineCloudUserHandler volcEngineCloudUserHandler;
    @Resource
    private KSCloudUserHandler ksCloudUserHandler;
    @Resource
    private HttpUtils httpUtils;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private CloudSSODao cloudSSODao;

    @RequestMapping(value = "/idp/azure", method = RequestMethod.POST)
    public void idpAzure(@RequestBody Map<String, Object> params) {
        log.info("params：{}", JSON.toJSONString(params));
        if (null != params) {
            for (Entry<String, Object> entry : params.entrySet()) {
                log.info("key-value: {}-{}", entry.getKey(), entry.getValue());
            }
        }
    }

    @RequestMapping(value = "/idp/login", method = RequestMethod.GET)
    public void redirect(HttpServletRequest request, HttpServletResponse response) throws Exception {
        CloudSSOLoginLog cloudSSOLoginLog = new CloudSSOLoginLog();
        cloudSSOLoginLog.setUserId(UserContext.getCurrentUserId());
        try {
            for (Entry<String, String[]> map : request.getParameterMap().entrySet()) {
                log.info("解析SAMLRequest:{} - {}", map.getKey(), StringUtils.join(map.getValue()));
            }
            AuthnRequest authnRequest = new AuthnRequestResolution().resolve(request);
            CloudAccountEntity cloudAccount = getAccountEntity(request, JSON.toJSONString(authnRequest));
            log.info("resolve authnrequest successfully!:{}", JSON.toJSONString(authnRequest));
            Result result = getResult(cloudAccount, cloudSSOLoginLog);
            log.info("get SP metadata successfully!");
            SAMLResponseResolution samlResponseResolution = new SAMLResponseResolution();
            Response samlResponse = samlResponseResolution.createSamlResponse(authnRequest, cloudAccount.getCloudType(), result);
            log.info("resolve saml response successfully!");
            samlResponseResolution.signAssertion(samlResponse);
            log.info("sign saml response successfully!");
            samlResponseResolution.send(authnRequest, response, request, samlResponse);
            log.info("send saml response successfully!");
            cloudSSOLoginLog.setStatus(Constants.SUCCESS);
            cloudSSOLoginLog.setResult("success");
        } catch (Exception e) {
            log.error("[error Message] decode SAMLRequest error !" + e.getMessage());
            response.getWriter().println(e.getMessage());
            cloudSSOLoginLog.setStatus(Constants.FAIL);
            cloudSSOLoginLog.setResult(e.getMessage());
        }
        insertSSOLog(cloudSSOLoginLog);
    }

    private CloudAccountEntity getAccountEntity(HttpServletRequest request, String authnRequestStr) {
        String domainId = request.getParameter("domainId");
        CloudAccountEntity cloudAccount = null;
        if (StringUtils.isNotEmpty(domainId)) {
            cloudAccount = cloudAccountDao.queryCloudAccountByDomainId(domainId);
        }
        if (null == cloudAccount) {
            List<CloudAccountEntity> accountEntityList = cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
            for (CloudAccountEntity cloudAccountEntity : accountEntityList) {
                if (authnRequestStr.contains(cloudAccountEntity.getDomainId())) {
                    return cloudAccountEntity;
                }
            }
        }
        return cloudAccount;
    }

    private void insertSSOLog(CloudSSOLoginLog cloudSSOLoginLog) {
        try {
            cloudSSOLoginLog.setLoginTime(new Date());
            cloudSSODao.insertCloudSSOLoginLog(cloudSSOLoginLog);
        } catch (Exception e) {
            log.error("[error Message] insert CloudSSOLoginLog error !" + e.getMessage());
        }
    }

    private Result getResult(CloudAccountEntity cloudAccount, CloudSSOLoginLog cloudSSOLoginLog) throws Exception {
        cloudSSOLoginLog.setAccountId(cloudAccount.getId());
        log.info("cloud:{}", cloudAccount.getName());
        String aliLoginName = StringUtils.EMPTY;
        String tencentUserName = StringUtils.EMPTY;
        String volcEngineUserName = StringUtils.EMPTY;
        String ksUserName = StringUtils.EMPTY;
        MetadataBean spMetadata = new MetadataBean();
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                spMetadata = getHuaweiMetadataBean();
                log.info("用户：{} 华为云SSO", UserContext.getCurrentUserId());
                break;
            case Constants.ALI_CLOUD:
                spMetadata = getAliMetadataBean(cloudAccount);
                aliLoginName = aliCloudUserHandler.queryUserLoginName(cloudAccount.getDomainId());
                log.info("用户：{} 阿里云用户名：{}", UserContext.getCurrentUserId(), aliLoginName);
                break;
            case Constants.TENCENT_CLOUD:
                tencentUserName = getTencentUserName(cloudAccount);
                log.info("tencentUserName:{}", tencentUserName);
                spMetadata = getTencentMetadataBean(cloudAccount);
                break;
            case Constants.VOLC_ENGINE_CLOUD:
                log.info("用户：{} 火山引擎SSO", UserContext.getCurrentUserId());
                UserEntity userEntity = new UserEntity();
                userEntity.setUserName(UserContext.getCurrentUserId());
                userEntity.setAccountId(cloudAccount.getId());
                VolacEngineCloudResponse volacEngineCloudResponse = volcEngineCloudUserHandler.queryUserExists(userEntity);
                volcEngineUserName = volacEngineCloudResponse.getUserEntity().getUserName();

                spMetadata = getVolcEngineMetadataBean(cloudAccount);
                break;
            case Constants.KS_CLOUD:
                log.info("用户：{} ⛰️云SSO", UserContext.getCurrentUserId());
                 UserEntity userEntityKs = new UserEntity();
                userEntityKs.setUserName(UserContext.getCurrentUserId());
                userEntityKs.setAccountId(cloudAccount.getId());
                ksUserName = ksCloudUserHandler.queryUserExists(userEntityKs).getUserEntity().getUserName()+"@52tt.com";
                spMetadata = getKSMetadataBean(cloudAccount);
                log.info("用户：{} ⛰️云用户名：{}", UserContext.getCurrentUserId(), ksUserName);
                break;
            default:
                log.info("用户：{} 未知云SSO", UserContext.getCurrentUserId());
                break;
        }
        return new Result(spMetadata, aliLoginName, cloudAccount, tencentUserName, volcEngineUserName,ksUserName);
    }

    private String getTencentUserName(CloudAccountEntity cloudAccount) {
        String tencentUserName;
        UserEntity userEntity = new UserEntity();
        userEntity.setUserName(UserContext.getCurrentUserId());
        userEntity.setAccountId(cloudAccount.getId());
        TencentCloudResponse tencentCloudResponse = tencentCloudUserHandler.queryUserExists(userEntity);
        tencentUserName = tencentCloudResponse.getUserEntity().getUserName();
        return tencentUserName;
    }


    private MetadataBean getVolcEngineMetadataBean(CloudAccountEntity cloudAccountEntity)
            throws Exception {
        MetadataBean spMetadata;
        String metadataContent = httpUtils.doGet(
                Constants.VOLC_ENGINE_SP_URL + cloudAccountEntity.getDomainId());
        spMetadata = new MetadataBean();
        spMetadata.initSPMetadata(metadataContent);
        return spMetadata;
    }

      private MetadataBean getKSMetadataBean(CloudAccountEntity cloudAccountEntity)
            throws Exception {
        MetadataBean spMetadata;
        String metadataContent = httpUtils.doGet(String.format(Constants.KS_YUN_SP_URL, cloudAccountEntity.getDomainId())+ cloudAccountEntity.getDomainId());
        spMetadata = new MetadataBean();
        spMetadata.initSPMetadata(metadataContent);
        return spMetadata;
    }

    private MetadataBean getAliMetadataBean(CloudAccountEntity cloudAccountEntity)
            throws Exception {
        MetadataBean spMetadata;
        String metadataContent = httpUtils.doGet(
                (cloudAccountEntity.isInternational() ? Constants.ALI_CLOUD_INTEL_SP_URL : Constants.ALI_CLOUD_SP_URL) + cloudAccountEntity.getDomainId());
        spMetadata = new MetadataBean();
        spMetadata.initSPMetadata(metadataContent);
        return spMetadata;
    }

    private MetadataBean getTencentMetadataBean(CloudAccountEntity cloudAccountEntity)
            throws Exception {
        String metadataContent;
        if (cloudAccountEntity.isInternational()) {
            metadataContent = "<EntityDescriptor xmlns=\"urn:oasis:names:tc:SAML:2.0:metadata\" xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\" xmlns:saml=\"urn:oasis:names:tc:SAML:2.0:AssertionConsumerService\" entityID=\"https://www.tencentcloud.com/"+ cloudAccountEntity.getDomainId() +"/saml/sso\" validUntil=\"2060-12-01T23:59:59Z\">\n" +
                    "<SPSSODescriptor WantAssertionsSigned=\"true\" protocolSupportEnumeration=\"urn:oasis:names:tc:SAML:2.0:protocol\">\n" +
                    "<KeyDescriptor xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\" use=\"signing\">\n" +
                    "<ds:KeyInfo xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\">\n" +
                    "<ds:X509Data>\n" +
                    "<ds:X509Certificate> MIIDDTCCAnYCCQDsOxizbGiilDANBgkqhkiG9w0BAQsFADCByjELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCWd1YW5nZG9uZzERMA8GA1UEBwwIc2hlbnpoZW4xNjA0BgNVBAoMLVRlbmNlbnQgVGVjaG5vbG9neSAoU2hlbnpoZW4pIENvbXBhbnkgTGltaXRlZDEWMBQGA1UECwwNVGVuY2VudCBDbG91ZDEaMBgGA1UEAwwRY2xvdWQudGVuY2VudC5jb20xKDAmBgkqhkiG9w0BCQEWGWNsb3VkX25vcmVwbHlAdGVuY2VudC5jb20wHhcNMjEwOTE3MTQxNjIzWhcNMzEwOTE1MTQxNjIzWjCByjELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCWd1YW5nZG9uZzERMA8GA1UEBwwIc2hlbnpoZW4xNjA0BgNVBAoMLVRlbmNlbnQgVGVjaG5vbG9neSAoU2hlbnpoZW4pIENvbXBhbnkgTGltaXRlZDEWMBQGA1UECwwNVGVuY2VudCBDbG91ZDEaMBgGA1UEAwwRY2xvdWQudGVuY2VudC5jb20xKDAmBgkqhkiG9w0BCQEWGWNsb3VkX25vcmVwbHlAdGVuY2VudC5jb20wgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAPROBeNvgtwFJbiAO14Ff8SNltu3l05HwuVeU34mvACASMgHA3ZM8i1hF1wlZg9LkICNKCkulWBPQAmEuoIe4ODQM/+Ht5VBuFdBkyBVs/ACS9Ja4E6fBZd73vCDbNteehvqk5yJkyeSPzql8cXt5x5LPzhQNPfu8uEYe/ESKOQdAgMBAAEwDQYJKoZIhvcNAQELBQADgYEA4DgCO5yxEGunFGeveymXG8JXsyx1UOxwlJmVXZRcPDazv1EDtnVyVVhyr52L2PRXeH/JjVtbZ13ddHVSDeG57wZu8bK0juYdKLhlHezb3Urb14uSkOR0DZEPHex53MqL7o36Qcim+CdlKOEpZ4qTFSjEQiCVpVzfXh6zthT+NE0= </ds:X509Certificate>\n" +
                    "</ds:X509Data>\n" +
                    "</ds:KeyInfo>\n" +
                    "</KeyDescriptor>\n" +
                    "<NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:transient</NameIDFormat>\n" +
                    "<NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:entity</NameIDFormat>\n" +
                    "<NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</NameIDFormat>\n" +
                    "<NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified</NameIDFormat>\n" +
                    "<NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:WindowsDomainQualifiedName</NameIDFormat>\n" +
                    "<AssertionConsumerService Binding=\"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST\" Location=\"https://www.tencentcloud.com/saml/sso\" index=\"0\" isDefault=\"true\"/>\n" +
                    "</SPSSODescriptor>\n" +
                    "<Organization>\n" +
                    "<OrganizationName xml:lang=\"en\">Tencent Cloud Services, Inc.</OrganizationName>\n" +
                    "<OrganizationDisplayName xml:lang=\"en\">Tencent Cloud</OrganizationDisplayName>\n" +
                    "<OrganizationURL xml:lang=\"en\">https://www.tencentcloud.com</OrganizationURL>\n" +
                    "</Organization>\n" +
                    "</EntityDescriptor>";
        } else {
            String endpoint = Constants.TENCENT_CLOUD_SP_URL + cloudAccountEntity.getDomainId();
            log.info("endpoint:{}", endpoint);
            metadataContent = httpUtils.doGet(endpoint);
        }
        MetadataBean spMetadata = new MetadataBean();
        spMetadata.initSPMetadata(metadataContent);
        return spMetadata;
    }


    private MetadataBean getHuaweiMetadataBean() throws Exception {
        MetadataBean spMetadata;
        String metadataContent = httpUtils.doGet(Constants.HUAWEI_CLOUD_SP_URL);
        spMetadata = new MetadataBean();
        spMetadata.initSPMetadata(metadataContent);
        return spMetadata;
    }
}
