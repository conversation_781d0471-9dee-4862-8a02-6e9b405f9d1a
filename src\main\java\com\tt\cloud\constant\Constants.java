package com.tt.cloud.constant;

import com.tt.cloud.bean.v2.CloudFlavorInfo;

import java.util.*;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/18 15:19
 */
public class Constants {
    public static final String HUAWEI_CLOUD = "huawei";
    public static final String TENCENT_CLOUD = "tencent";
    public static final String ALI_CLOUD = "ali";
    public static final String AWS_CLOUD = "aws";
    public static final String VOLC_ENGINE_CLOUD = "volcengine";
    public static final String KS_CLOUD = "ksyun";

    public static final String SUCCESS = "1";
    public static final String FAIL = "0";
    public static final String EDIT = "edit";
    public static final String ADD = "add";
    public static final String BELONG_TYPE_USER = "user";
    public static final String BELONG_TYPE_GROUP = "group";
    public static final String ERROR = "0";
    public static final String HUAWEI_CLOUD_SP_URL = "https://auth.huaweicloud.com/authui/saml/metadata.xml";
    public static final String TENCENT_CLOUD_SP_URL = "https://cloud.tencent.com/saml/SpMetadata.xml?tenantID=";
    public static final String TENCENT_NATION_CLOUD_SP_URL = "https://www.tencentcloud.com/saml/SpMetadata.xml?tenantID=";
    public static final String ALI_CLOUD_SP_URL = "https://signin.aliyun.com/saml/SpMetadata.xml?tenantID=";
    public static final String ALI_CLOUD_INTEL_SP_URL = "https://signin.alibabacloud.com/saml/SpMetadata.xml?tenantID=";
    public static final String VOLC_ENGINE_SP_URL = "https://signin.volcengine.com/saml_user/SpMetadata.xml?tenantID=";
    public static final String KS_YUN_SP_URL = "https://signin.ksyun.com/saml/%s/SpMetadata.xml?tenantID=";

    public static final String DATA_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final Map<String, String> VOLC_ENGINE_REGION_MAP = new HashMap<String, String>() {{
        put("cn-beijing", "华北2(北京)");
        put("cn-shanghai", "华东2(上海)");
        put("cn-guangzhou", "华南1(广州)");
        put("ap-southeast-1", "亚太东南(柔佛)");
    }};

    public static final List<CloudFlavorInfo> VOLE_ENGINE_CLB_FLAVOR_LIST = Arrays.asList(
            new CloudFlavorInfo("small_1", "小型I"),
            new CloudFlavorInfo("small_2", "小型II"),
            new CloudFlavorInfo("medium_1", "中型I"),
            new CloudFlavorInfo("medium_2", "中型II"),
            new CloudFlavorInfo("large_1", "大型I"),
            new CloudFlavorInfo("large_2", "大型II")
    );

    public static final List<CloudFlavorInfo> ALI_CLB_FLAVOR_LIST = Arrays.asList(
            new CloudFlavorInfo("slb.s1.small", "简约型I(slb.s1.small)"),
            new CloudFlavorInfo("slb.s2.small", "标准型I(slb.s2.small)"),
            new CloudFlavorInfo("slb.s2.medium", "标准型II(slb.s2.medium)"),
            new CloudFlavorInfo("slb.s3.small", "高阶型I(slb.s3.small)"),
            new CloudFlavorInfo("slb.s3.medium", "高阶型II(slb.s3.medium)"),
            new CloudFlavorInfo("slb.s3.large", "超强型I(slb.s3.large)")
    );

    public static final List<CloudFlavorInfo> ALI_ALB_FLAVOR_LIST = Arrays.asList(
            new CloudFlavorInfo("Basic", "基础版"),
            new CloudFlavorInfo("Standard", "标准版"),
            new CloudFlavorInfo("StandardWithWaf", "WAF 增强版")
    );

}
