package com.tt.cloud.bean.v2;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/1/19 9:45
 */
@Getter
@Setter
@ToString
public class LBCreateRequest {
    private String cloud_id;
    private String region_id;
    private String project_id;
    private String lb_name;
    private String lb_type;
    private String net_type;
    private List<String> zone_ids = new ArrayList<>();
    private String zone_id;
    private String l7_flavor_id;
    private String l4_flavor_id;
    private int bandwidth_size;
    private String vpc_id;
    private String subnet_id;
    private String resource_type;
    private String cluster_id;
    private String istio_net;
    private String email;
    private List<LBItem> list = new ArrayList<>();
}
