package com.tt.cloud.service;

import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.CloudAccountResponse;
import com.tt.cloud.dao.CloudAccountDao;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/26 15:09
 */
@Slf4j
@Component
public class CloudAccountServiceImpl {
    @Resource
    private CloudAccountDao cloudAccountDao;

    /**
     * 查询云商账号列表
     *
     * @param cloudAccountRequest cloudAccountRequest
     * @return CloudAccountResponse
     */
    public CloudAccountResponse queryCloudAccountList(CloudAccountRequest cloudAccountRequest) {
        CloudAccountResponse cloudAccountResponse = new CloudAccountResponse();
        try {
            List<CloudAccountEntity> accountEntityList = cloudAccountDao.queryCloudAccountList(cloudAccountRequest);
            for (CloudAccountEntity cloudAccountEntity : accountEntityList) {
                cloudAccountEntity.setSecretKey(StringUtils.EMPTY);
            }
            cloudAccountResponse.setCloudAccountList(accountEntityList);
        } catch (Exception e) {
            log.error("查询云商账号列表异常", e);
            cloudAccountResponse.getHeader().setErrorMsg("查询云商账号列表异常：" + e.getMessage());
        }
        return cloudAccountResponse;
    }

}
