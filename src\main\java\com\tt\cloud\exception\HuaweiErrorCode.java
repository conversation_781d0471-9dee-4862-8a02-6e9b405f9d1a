package com.tt.cloud.exception;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/25 14:00
 */
@Getter
public enum HuaweiErrorCode {
    MISSING_PARAMETER("1100", "缺失必选参数, 请检查请求参数。"),
    USERNAME_VALIDATOR_ERROR("1101", "用户名校验失败,请检查用户名。"),
    EMAIL_VALIDATOR_ERROR("1101", "邮箱校验失败,请检查邮箱。"),
    PASSWORD_VALIDATOR_ERROR("1103", "密码校验失败,请检查密码。"),
    PHONE_VALIDATOR_ERROR("1104", "手机号校验失败,请检查手机号。"),
    PHONE_AREA_CODE_VALIDATOR_ERROR("1106", "国家码、手机号必须同时存在,请检查国家码和手机号是否同时存在。"),
    ADMIN_DENIED_DELETE_VALIDATOR_ERROR("1107", "帐号管理员不能被删除,不允许此操作。"),
    PASSWORD_SAME_VALIDATOR_ERROR("1108", "新密码不能与原密码相同,请修改新密码。"),
    USERNAME_EXISTS_VALIDATOR_ERROR("1109", "用户名已存在,请修改用户名。"),
    EMAIL_EXISTS_VALIDATOR_ERROR("1110", "邮箱已存在,请修改邮箱。"),
    PHONE_EXISTS_VALIDATOR_ERROR("1111", "手机号已存在,请修改手机号。"),
    USER_LIMIT_VALIDATOR_ERROR("1115", "IAM用户数量达到最大限制,请修改用户配额或联系技术支持。"),
    USER_MEMO_VALIDATOR_ERROR("1117", "用户描述校验失败,请修改用户描述。"),
    PASSWORD_WEAK_VALIDATOR_ERROR("1118", "密码是弱密码,重新选择密码。"),
    REQUEST_MISSION_VALIDATOR_ERROR("IAM.0007", "请求参数校验失败,请检查请求参数。"),
    SUBJECT_TOKEN_VALIDATOR_ERROR("IAM.0009", "请求中的X-Subject-Token校验失败,请检查请求参数。"),
    REQUEST_BODY_VALIDATOR_ERROR("IAM.0011", "请求体校验失败,请检查请求体。");

    private final String errorCode;
    private final String errorMessage;

    HuaweiErrorCode(String errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public static String getErrorMsg(String errorCode) {
        for (HuaweiErrorCode value : HuaweiErrorCode.values()) {
            if (errorCode.contains(value.getErrorCode())) {
                return value.getErrorMessage();
            }
        }
        return StringUtils.EMPTY;
    }

}
