package com.tt.cloud.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/10 16:06
 */
@Getter
@Setter
@ToString
public class UserGroupInfo {
    private String groupId;
    private String groupName;
    private String remark;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserGroupInfo that = (UserGroupInfo) o;
        return Objects.equals(groupName, that.groupName) &&
                Objects.equals(groupId, that.groupId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(groupName, groupId);
    }

}
