package com.tt.cloud.bean;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/8/25 14:33
 */
@Getter
@Setter
@ToString
public class TerraformEntity {
    private String trackId;
    private String cloud;
    private String name;
    private String type;
    private String content;
    private String planResult;
    private String executeResult;
    private String stateResult;
    private String status;
    private Date createTime;
    private Date endTime;
    private String creatorId;
    private String creatorName;
    private String accountId;
    private String accountName;
    private String region;
    private String rollbackResult;
    private String projectId;
    private boolean hasManageAuth;
    private String clusterId;
    private String action;
    private String mainTrackId;
    private Date lastModifyTime;
    private String lastModifyUserId;
    private String lastModifyUserName;
    private String tags;
    private String labels;
    private String taints;
    private String resource_id;

    private String source;
}
