<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--上面2行的是约束依赖，固定照抄就好-->
<!--下面的才是要自己编写的地方-->
<!--写mapper的配置文件第一步就是要写<mapper></mapper>标签-->
<!--<mapper></mapper>标签里包含着各个CURD操作的SQL语句-->
<mapper namespace="com.tt.cloud.dao.CloudAccountDao">

  <insert id="addCloudAccountInfo" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
    insert into
      tt_cloud_account(name, cloudtype, secretid, secretkey, endpoint, createtime, creatorid, memo,domainid,destination)
    VALUES (
             #{name}, #{cloudType}, #{secretId}, #{secretKey}, #{endpoint}, now(), #{creatorId}, #{memo}, #{domainId}, #{destination}
           )
  </insert>

  <update id="editCloudAccountInfo">
    update tt_cloud_account set name = #{name}, secretid = #{secretId},
                                secretkey = #{secretKey}, domainid = #{domainId},
                                memo = #{memo},destination =#{destination}, resourceManage = #{resourceManage}
    where id = #{id}
  </update>

  <update id="updateCloudAccountConnectStatus">
    update  tt_cloud_account set connectstatus = #{connectStatus}, domainid = #{domainId} where id = #{id}
  </update>

  <delete id="deleteCloudAccountInfo">
    delete from tt_cloud_account where id = #{id}
  </delete>

  <select id="queryCloudAccountInfo" resultType="com.tt.cloud.bean.CloudAccountEntity">
    select a.id, a.name, a.cloudtype, a.secretid, a.secretkey, a.endpoint, a.createtime,
           a.creatorid, a.memo, a.domainid, a.connectstatus, a.destination, a.resourcemanage, a.isinternational
    from tt_cloud_account a where 1=1
    <if test="name != null and name != ''">
      and a.name =#{name}
    </if>
    <if test="id != null and id != ''">
      and a.id =#{id}
    </if>
  </select>

  <select id="queryCloudAccountList" resultType="com.tt.cloud.bean.CloudAccountEntity">
    select a.id, a.name, a.cloudtype, a.secretid, a.secretkey, a.endpoint, a.createtime,
           a.creatorid, a.memo, v.username as creatorName, a.domainid, a.connectstatus, a.destination, a.resourcemanage, a.isinternational
    from tt_cloud_account a left join tt_op_user v on a.creatorid = v.userid and v.state = 1 order by a.id
  </select>

  <select id="queryCloudAccountInfoById" resultType="com.tt.cloud.bean.CloudAccountEntity">
    select a.id, a.name, a.cloudtype, a.secretid, a.secretkey,
           a.endpoint, a.createtime, a.creatorid, a.memo, a.domainid, a.connectstatus, a.destination, a.resourcemanage, a.isinternational
    from tt_cloud_account a where a.id = #{id}
  </select>

  <select id="queryUserAuthNameList" resultType="java.lang.String">
    select auth from tt_cloud_user_auth where 1=1
    <if test=" accountId != null and accountId != '' ">
      and accountid = #{accountId}
    </if>
    <if test=" uid != null and uid != '' ">
      and uid = #{uid}
    </if>
    <if test=" uin != null and uin != '' ">
      and uin = #{uin}
    </if>
    <if test=" uname != null and uname != '' ">
      and (uname = #{uname} or uname = concat(#{uname}, '@52tt.com'))
    </if>
  </select>

  <select id="queryUserAuthList" resultType="com.tt.cloud.bean.UserAuthEntity">
    select id, auth, authid, uid, uin, uname, accountid from tt_cloud_user_auth where accountid = #{accountId}
    <if test=" uid != null and uid != '' ">
      and uid = #{uid}
    </if>
    <if test=" uin != null and uin != '' ">
      and uin = #{uin}
    </if>
    <if test=" accountId != null and accountId != '' ">
      and accountid = #{accountId}
    </if>
    <if test=" uname != null and uname != '' ">
      and (uname = #{uname} or uname = concat(#{uname}, '@52tt.com'))
    </if>
  </select>

  <select id="queryUserAuthInfoByName" resultType="com.tt.cloud.bean.UserAuthEntity">
    select id, auth, authid, uid, uin, uname, accountid from tt_cloud_user_auth where accountid = #{accountId}
    and (uname = #{uname} or uname = concat(#{uname}, '@52tt.com')) limit 1
  </select>

  <select id="queryResourceManageAccountList"
    resultType="com.tt.cloud.bean.CloudAccountEntity">
    select id, name, cloudtype, endpoint, domainid, destination, connectstatus, createtime, creatorid, memo, resourcemanage from tt_cloud_account
    where resourcemanage = 1
  </select>

  <select id="queryUserAllAuthList" resultType="com.tt.cloud.bean.UserAuthEntity">
    select id, auth, authid, uid, uin, uname, accountid from tt_cloud_user_auth where
    auth IN ('Tenant Administrator', 'AdministratorAccess')
    and (uname = #{uname} or uname = concat(#{uname}, '@52tt.com'))
  </select>

  <select id="queryCloudAccountInfoByCloudId"
          resultType="com.tt.cloud.bean.CloudAccountEntity">
    select id, name, cloudtype, secretid, secretkey, endpoint, domainid, resourcemanage,
           isinternational, connectstatus, createtime, creatorid, memo, destination from tt_cloud_account where domainid = #{cloudId} and resourcemanage = 1
  </select>

  <select id="queryCloudAccountByDomainId"
          resultType="com.tt.cloud.bean.CloudAccountEntity">
    select id, name, cloudtype, secretid, secretkey, endpoint, domainid, resourcemanage,
           isinternational, connectstatus, createtime, creatorid, memo, destination from tt_cloud_account where domainid = #{domainId}
  </select>

</mapper>
