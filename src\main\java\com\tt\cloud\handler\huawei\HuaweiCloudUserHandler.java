package com.tt.cloud.handler.huawei;

import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.*;
import com.tt.cloud.bean.*;
import com.tt.cloud.config.HuaweiCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import com.alibaba.fastjson.JSON;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 华为云IAM用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 13:44
 */
@Slf4j
@Component
public class HuaweiCloudUserHandler {
    @Resource
    private HuaweiCloudHttpConfig huaweiCloudHttpConfig;
    @Resource
    private HuaweiCloudUserGroupHandler huaweiCloudUserGroupHandler;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 新增用户
     *
     * @param userEntity userEntity
     */
    public HuaweiCloudResponse createUser(UserEntity userEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            CreateUserOption userOption = getCreateUserOption(userEntity);
            log.info("新增华为云用户：{}", userOption);
            IamClient iamClient = huaweiCloudHttpConfig.getHuaweiIamClient(userEntity.getAccountId());
            CreateUserResponse createUserResponse = iamClient.createUser(new CreateUserRequest()
                            .withBody(new CreateUserRequestBody().withUser(userOption)));
            bindUserGroup(userEntity, createUserResponse);
            log.info("新增华为云用户成功{}", createUserResponse);
            updateLoginProtect(iamClient, createUserResponse, userEntity);
            userEntity.setPassword(userOption.getPassword());
            userEntity.setUserId(createUserResponse.getUser().getId());
            huaweiCloudResponse.setUserEntity(userEntity);
            sendNotice(userEntity);
        } catch (Exception e) {
            log.error("新增华为云用户异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "新增华为云用户异常");
        }
        return huaweiCloudResponse;
    }

    private void bindUserGroup(UserEntity userEntity, CreateUserResponse createUserResponse) {
        if (null != userEntity.getGroupIdList() && !userEntity.getGroupIdList().isEmpty()) {
            GroupEntity groupEntity = new GroupEntity();
            groupEntity.setGroupIdList(userEntity.getGroupIdList());
            groupEntity.setUserId(createUserResponse.getUser().getId());
            groupEntity.setAccountId(userEntity.getAccountId());
            huaweiCloudUserGroupHandler.keystoneAddUserToGroup(groupEntity);
        }
    }

    private CreateUserOption getCreateUserOption(UserEntity userEntity) {
        CreateUserOption userOption = new CreateUserOption();
        userOption.setName(userEntity.getUserName());
        userOption.setEmail(userEntity.getEmail());
        if (StringUtils.isNotEmpty(userEntity.getPhone())) {
            userOption.setAreacode("0086");
            userOption.setPhone(userEntity.getPhone());
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
        userOption.setDescription(userEntity.getRemark());
        userOption.setPwdStatus(false);
        userOption.setEnabled(true);
        if (userEntity.isApiUser()) {
            userOption.setAccessMode("programmatic");
        } else {
            userOption.setAccessMode("console");
        }
        userOption.setXuserType("TenantIdp");
        userOption.setXuserId(userEntity.getEmail().split("@")[0]);
        userOption.setPassword(Utils.getPassword());
        userOption.setDomainId(cloudAccount.getDomainId());
        return userOption;
    }

    private void updateLoginProtect(IamClient iamClient, CreateUserResponse createUserResponse, UserEntity userEntity) {
        try {
            if (!userEntity.isApiUser()) {
                UpdateLoginProtectRequest updateLoginProtectRequest = new UpdateLoginProtectRequest();
                updateLoginProtectRequest.setUserId(createUserResponse.getUser().getId());
                UpdateLoginProjectReq updateLoginProjectReq = new UpdateLoginProjectReq();
                UpdateLoginProject loginProject = new UpdateLoginProject();
                loginProject.setEnabled(true);
                loginProject.setVerificationMethod("sms");
                updateLoginProjectReq.setLoginProtect(loginProject);
                updateLoginProtectRequest.setBody(updateLoginProjectReq);
                iamClient.updateLoginProtect(updateLoginProtectRequest);
            }
        } catch (Exception e) {
            log.info("华为云为用户增加手机登录验证失败:{} {}", e, createUserResponse.getUser().getEmail());
        }
    }

    private void sendNotice(UserEntity userEntity) throws Exception {
        if (!userEntity.isApiUser()) {
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
            Map<String, Object> messageParam = new HashMap<>();
            messageParam.put("cloud", cloudAccount.getMemo() + "/" + cloudAccount.getName() + "/" + cloudAccount.getDomainId());
            messageParam.put("username", userEntity.getUserName());
            messageParam.put("password", userEntity.getPassword());
            messageParam.put("url", "*由于开启云商单点登录，关闭密码登录方式*\\n*请通过[运维平台](https://yw-sso.ttyuyin.com/index/dashboard)->"
                    + "[云泽平台](https://yw-cloud.ttyuyin.com/tt-cloud/cloud/center)进行登录*");
            String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(userEntity.getEmail(), content);
        }
    }

    /**
     * 查詢用戶是否存在
     *
     * @param userEntity userEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse queryUserExists(UserEntity userEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            if (StringUtils.isNotEmpty(userEntity.getAccountId())) {
                UserAuthEntity userAuthInfo = new UserAuthEntity();
                userAuthInfo.setAccountId(userEntity.getAccountId());
                userAuthInfo.setUname(userEntity.getUserName());
                UserAuthEntity userAuthEntity = cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
                CloudAccountRequest cloudAccountRequest = new CloudAccountRequest();
                cloudAccountRequest.setId(userEntity.getAccountId());
                CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfo(cloudAccountRequest);
                if (null != userAuthEntity) {
                    log.info("查询华为云用户存在:{}", userAuthEntity.getUname());
                    UserEntity userEntity1 = new UserEntity();
                    userEntity1.setUserId(userAuthEntity.getUid());
                    userEntity1.setUserName(userAuthEntity.getUname());
                    huaweiCloudResponse.setUserEntity(userEntity1);
                } else {
                    huaweiCloudResponse.setUserEntity(queryUser(userEntity));
                }
                huaweiCloudResponse.setLoginUrl("https://auth.huaweicloud.com/authui/federation/websso?domain_id="
                        + cloudAccount.getDomainId() + "&idp=qw-sso&protocol=saml");
                return huaweiCloudResponse;
            } else {
                List<CloudAccountEntity> accountEntityList =  cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
                accountEntityList = accountEntityList.stream()
                        .filter(item -> Constants.HUAWEI_CLOUD.equals(item.getCloudType()) && item.isConnectStatus())
                        .collect(Collectors.toList());
                for (CloudAccountEntity cloudAccountEntity : accountEntityList) {
                    KeystoneListUsersRequest keystoneListUsersRequest = new KeystoneListUsersRequest();
                    keystoneListUsersRequest.setName(userEntity.getUserName());
                    keystoneListUsersRequest.setEnabled(true);
                    KeystoneListUsersResponse keystoneListUsersResponse =
                            huaweiCloudHttpConfig.getHuaweiIamClient(cloudAccountEntity.getId())
                                    .keystoneListUsers(keystoneListUsersRequest);
                    if (null == keystoneListUsersResponse.getUsers() || keystoneListUsersResponse.getUsers().isEmpty()) {
                        log.info("查询华为云用户为空，用户不存在");
                        continue;
                    }
                    if (getHuaweiCloudResponse(userEntity, huaweiCloudResponse, keystoneListUsersResponse, cloudAccountEntity)) {
                        return huaweiCloudResponse;
                    }
                }
            }
        } catch (Exception e) {
            log.info("查询用户是否存在异常", e);
            huaweiCloudResponse.getHeader().setErrorMsg("查询用户是否存在异常：" + e.getMessage());
        }
        return huaweiCloudResponse;
    }

    private UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        KeystoneListUsersRequest keystoneListUsersRequest = new KeystoneListUsersRequest();
        keystoneListUsersRequest.setName(userEntity.getUserName());
        keystoneListUsersRequest.setEnabled(true);
        KeystoneListUsersResponse keystoneListUsersResponse =
                huaweiCloudHttpConfig.getHuaweiIamClient(userEntity.getAccountId())
                        .keystoneListUsers(keystoneListUsersRequest);
        if (null == keystoneListUsersResponse.getUsers() || keystoneListUsersResponse.getUsers().isEmpty()) {
            log.info("查询华为云用户为空，用户不存在");
            if (!userEntity.getUserName().contains("@52tt.com")) {
                userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                return queryUser(userEntity);
            }
        }
        for (KeystoneListUsersResult user : keystoneListUsersResponse.getUsers()) {
            if (user.getName().equals(userEntity.getUserName())) {
                userInfo = new UserEntity();
                userInfo.setUserId(user.getId());
                userInfo.setUserName(user.getName());
            }
        }
        return userInfo;
    }

    private boolean getHuaweiCloudResponse(UserEntity userEntity,
            HuaweiCloudResponse huaweiCloudResponse,
            KeystoneListUsersResponse keystoneListUsersResponse, CloudAccountEntity cloudAccountEntity) {
        for (KeystoneListUsersResult user : keystoneListUsersResponse.getUsers()) {
            if (user.getName().equals(userEntity.getUserName())) {
                log.info("查询华为云用户存在:{}", user.getName());
                UserEntity userEntity1 = new UserEntity();
                userEntity1.setUserId(user.getId());
                userEntity1.setUserName(user.getName());
                huaweiCloudResponse.setUserEntity(userEntity1); // domain_id
                huaweiCloudResponse.setLoginUrl(
                        "https://auth.huaweicloud.com/authui/federation/websso?domain_id=" + cloudAccountEntity.getDomainId() +"&idp=qw-sso&protocol=saml");
                return true;
            }
        }
        return false;
    }

    public CloudAllUserResponse queryUserListByAccount(CloudAccountEntity cloudAccount, String nextMarker) {
        CloudAllUserResponse userListRst = new CloudAllUserResponse();
        try {
            log.info("华为云子用户详情列表");

            // userList 先从redis里获取，如果没有，再调用接口获取，然后存入redis
            // redis key: huawei-all-user-list
            // redis value: userList
            // redis 过期时间 20 分钟
            List<UserEntity> userList = new ArrayList<>();
            String redisKey = "huawei-all-user-list:" + cloudAccount.getId();

            try {
                // 先从 Redis 获取用户列表
                String cachedUserListJson = stringRedisTemplate.opsForValue().get(redisKey);
                if (cachedUserListJson != null) {
                    log.info("从Redis缓存获取到华为云用户列表，key: {}", redisKey);
                    // 将JSON字符串转换为用户列表
                    userList = JSON.parseArray(cachedUserListJson, UserEntity.class);
                } else {
                    log.info("Redis缓存中未找到华为云用户列表，开始调用API获取，key: {}", redisKey);
                    // Redis中没有数据，调用华为云API获取
                    KeystoneListUsersRequest request = new KeystoneListUsersRequest();
                    request.setDomainId(cloudAccount.getDomainId());
                    KeystoneListUsersResponse response = huaweiCloudHttpConfig.getHuaweiIamClient(cloudAccount.getId()).keystoneListUsers(request);

                    if (response != null && response.getUsers() != null && !response.getUsers().isEmpty()) {
                        for (KeystoneListUsersResult user : response.getUsers()) {
                            UserEntity userEntity = new UserEntity();
                            userEntity.setUserId(safeGetString(user.getId()));
                            userEntity.setUserIn(safeGetString(user.getId())); // 华为云没有UIN概念，使用UserId
                            userEntity.setUserName(safeGetString(user.getName()));
                            userEntity.setRemark(safeGetString(user.getDescription()));
                            userEntity.setAccountId(cloudAccount.getDomainId());
                            userList.add(userEntity);
                        }

                        // 将用户列表存入Redis，过期时间20分钟
                        String userListJson = JSON.toJSONString(userList);
                        stringRedisTemplate.opsForValue().set(redisKey, userListJson, 20, TimeUnit.MINUTES);
                        log.info("已将华为云用户列表存入Redis缓存，key: {}, 用户数量: {}", redisKey, userList.size());
                    }
                }
            } catch (Exception e) {
                log.error("Redis操作异常，直接调用API获取用户列表: {}", e.getMessage(), e);
            }

            // nextMarker 有字符串转为int，如果不是数字型或空，则默认为0
            int pageIndex = 0;
            if (nextMarker != null && !nextMarker.isEmpty()) {
                try {
                    pageIndex = Integer.parseInt(nextMarker);
                    if (pageIndex < 0) {
                        pageIndex = 0; // 负数也默认为0
                    }
                } catch (NumberFormatException e) {
                    log.warn("nextMarker格式错误，无法转换为整数: {}, 使用默认值0", nextMarker);
                    pageIndex = 0;
                }
            }

            // 处理分页逻辑：将nextMarker转为int，并从userList中切片
            int size = 20;
            int startIndex = pageIndex * size;
            int endIndex = (pageIndex + 1) * size;
            List<UserEntity> pagedUserList = userList;
            if (startIndex >= userList.size()) {
                return userListRst;
            }
            if (endIndex > userList.size()) {
                endIndex = userList.size();
                userListRst.setNextMarker(Strings.EMPTY);
            } else {
                userListRst.setNextMarker(String.valueOf(pageIndex + 1));
            }
            pagedUserList = userList.subList(startIndex, endIndex);

            // 对分页后的用户列表进行详细信息补充
            attachUserInfos(pagedUserList, cloudAccount.getId());

            // 设置返回结果
            userListRst.setUserList(pagedUserList);

        } catch (Exception e) {
            log.error("查询华为云子用户详情列表异常", e);
            throw new RuntimeException("查询华为云子用户详情列表异常" + e.getMessage());
        }
        return userListRst;
    }

    /**
     * 安全获取字符串值，如果为null则返回空字符串
     */
    private String safeGetString(String value) {
        return value != null ? value : "";
    }

    /**
     * 补充用户详细信息
     */
    private void attachUserInfos(List<UserEntity> userList, String accountId) {
        if (userList == null || userList.isEmpty()) {
            return;
        }
        try {
            IamClient iamClient = huaweiCloudHttpConfig.getHuaweiIamClient(accountId);

            // 获取用户信息
            for (int i = 0; i < userList.size(); i++) {
                UserEntity user = userList.get(i);
                user.setPolicyList(new ArrayList<>());

                // 获取最后登录时间和登录IP  使用SDK里的  ShowUser 方法获取数据
                try {
                    ShowUserRequest showUserRequest = new ShowUserRequest();
                    showUserRequest.setUserId(user.getUserId());
                    ShowUserResponse showUserResponse = iamClient.showUser(showUserRequest);

                    if (showUserResponse != null && showUserResponse.getUser() != null) {
                        ShowUserResult userInfo = showUserResponse.getUser();
                        user.setEmail(safeGetString(userInfo.getEmail()));
                        user.setPhone(safeGetString(userInfo.getPhone()));
                        user.setLastLoginTime(safeGetString(userInfo.getLastLoginTime())); 
                        user.setCreateTime(safeGetString(userInfo.getCreateTime()));
                        user.setLastLoginIp(""); 
                        user.setConsoleLogin(safeGetString(userInfo.getAccessMode()).equals("programmatic") ? 0L : 1L);
                    }
                } catch (Exception e) {
                    log.error("获取用户详细信息异常: {}", e.getMessage());
                }

                // 1. 获取用户组列表
                try {
                    KeystoneListGroupsForUserRequest request = new KeystoneListGroupsForUserRequest();
                    request.setUserId(user.getUserId());
                    KeystoneListGroupsForUserResponse response = iamClient.keystoneListGroupsForUser(request);

                    if (response != null && response.getGroups() != null && !response.getGroups().isEmpty()) {
                        List<UserGroupInfo> groupList = new ArrayList<>();
                        for (KeystoneGroupResult group : response.getGroups()) {
                            UserGroupInfo userGroupInfo = new UserGroupInfo();
                            userGroupInfo.setGroupId(safeGetString(group.getId()));
                            userGroupInfo.setGroupName(safeGetString(group.getName()));
                            userGroupInfo.setRemark(safeGetString(group.getDescription()));
                            groupList.add(userGroupInfo);


                            // 2.1 获取用户组权限列表
                            try {
                                KeystoneListAllProjectPermissionsForGroupRequest listPermissionsRequest = new KeystoneListAllProjectPermissionsForGroupRequest();
                                listPermissionsRequest.setGroupId(group.getId());
                                listPermissionsRequest.setDomainId(user.getAccountId());
                                KeystoneListAllProjectPermissionsForGroupResponse listPermissionsResponse =
                                        iamClient.keystoneListAllProjectPermissionsForGroup(listPermissionsRequest);

                                if (listPermissionsResponse != null && listPermissionsResponse.getRoles() != null
                                    && !listPermissionsResponse.getRoles().isEmpty()) {
                                    List<PermissionEntity> policyList = new ArrayList<>();
                                    for (RoleResult role : listPermissionsResponse.getRoles()) {
                                        PermissionEntity permission = new PermissionEntity();
                                        permission.setPolicyName(safeGetString(role.getName()));
                                        permission.setPolicyType(safeGetString(role.getType()));
                                        permission.setDisplay_name(safeGetString(role.getDisplayName()));
                                        // permission.setCreateMode(null); // 华为云可能不支持此字段
                                        permission.setAddTime(safeGetString(role.getCreatedTime())); // 华为云RoleResult不返回附加时间
                                        permission.setRemark(safeGetString(role.getDescription()));
                                        permission.setBelongId(group.getId());
                                        permission.setBelongName(group.getName());
                                        permission.setBelongType(Constants.BELONG_TYPE_GROUP);
                                        policyList.add(permission);
                                    }
                                    user.getPolicyList().addAll(policyList);
                                }
                            } catch (Exception e) {
                                log.error("获取用户组权限信息异常: {}", e.getMessage());
                            }
                        }
                        user.setGroupList(groupList);
                    }
                } catch (Exception e) {
                    log.error("获取用户组信息异常: {}", e.getMessage());
                }

                // 2. 获取用户权限列表（华为云通过用户组获取权限）               

                // 3. 获取用户密钥ID列表 SDK ListPermanentAccessKeys 方法
                try {
                    ListPermanentAccessKeysRequest listAccessKeysRequest = new ListPermanentAccessKeysRequest();
                    listAccessKeysRequest.setUserId(user.getUserId());
                    ListPermanentAccessKeysResponse listAccessKeysResponse = iamClient.listPermanentAccessKeys(listAccessKeysRequest);

                    if (listAccessKeysResponse != null && listAccessKeysResponse.getCredentials() != null
                        && !listAccessKeysResponse.getCredentials().isEmpty()) {
                        List<PermanentAccessKeyEntity> accessKeyList = new ArrayList<>();
                        for (Credentials credential : listAccessKeysResponse.getCredentials()) {
                            PermanentAccessKeyEntity accessKeyEntity = new PermanentAccessKeyEntity();
                            accessKeyEntity.setAccess(safeGetString(credential.getAccess()));
                            accessKeyEntity.setStatus(safeGetString(credential.getStatus()));
                            accessKeyEntity.setCreateTime(safeGetString(credential.getCreateTime()));
                            accessKeyEntity.setUserId(user.getUserId());
                            accessKeyEntity.setUserName(user.getUserName());

                            // 使用 ShowPermanentAccessKey 方法获取最后使用时间
                            try {
                                ShowPermanentAccessKeyRequest showAccessKeyRequest = new ShowPermanentAccessKeyRequest();
                                showAccessKeyRequest.setAccessKey(credential.getAccess());
                                ShowPermanentAccessKeyResponse showAccessKeyResponse = iamClient.showPermanentAccessKey(showAccessKeyRequest);
                                if (showAccessKeyResponse != null && showAccessKeyResponse.getCredential() != null) {
                                    ShowCredential detailCredential = showAccessKeyResponse.getCredential();
                                    // 设置详细信息
                                    accessKeyEntity.setDescription(safeGetString(detailCredential.getDescription()));
                                    accessKeyEntity.setLastUsedDate(safeGetString(detailCredential.getLastUseTime()));
                                }
                            } catch (Exception e) {
                                log.error("获取访问密钥详细信息异常: {}", e.getMessage());
                            }
                            accessKeyList.add(accessKeyEntity);
                        }
                        user.setAccessKeyList(accessKeyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户密钥信息异常: {}", e.getMessage());
                    user.setAccessKeyList(new ArrayList<>());
                }

                userList.set(i, user);
            }
        } catch (Exception e) {
            log.error("华为云完善用户详细信息异常", e);
        }
    }

}
