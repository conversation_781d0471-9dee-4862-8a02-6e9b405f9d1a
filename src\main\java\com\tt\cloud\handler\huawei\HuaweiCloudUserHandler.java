package com.tt.cloud.handler.huawei;

import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.CreateUserOption;
import com.huaweicloud.sdk.iam.v3.model.CreateUserRequest;
import com.huaweicloud.sdk.iam.v3.model.CreateUserRequestBody;
import com.huaweicloud.sdk.iam.v3.model.CreateUserResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListUsersRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListUsersResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListUsersResult;
import com.huaweicloud.sdk.iam.v3.model.UpdateLoginProject;
import com.huaweicloud.sdk.iam.v3.model.UpdateLoginProjectReq;
import com.huaweicloud.sdk.iam.v3.model.UpdateLoginProtectRequest;
import com.tt.cloud.bean.*;
import com.tt.cloud.config.HuaweiCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 华为云IAM用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 13:44
 */
@Slf4j
@Component
public class HuaweiCloudUserHandler {
    @Resource
    private HuaweiCloudHttpConfig huaweiCloudHttpConfig;
    @Resource
    private HuaweiCloudUserGroupHandler huaweiCloudUserGroupHandler;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 新增用户
     *
     * @param userEntity userEntity
     */
    public HuaweiCloudResponse createUser(UserEntity userEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            CreateUserOption userOption = getCreateUserOption(userEntity);
            log.info("新增华为云用户：{}", userOption);
            IamClient iamClient = huaweiCloudHttpConfig.getHuaweiIamClient(userEntity.getAccountId());
            CreateUserResponse createUserResponse = iamClient.createUser(new CreateUserRequest()
                            .withBody(new CreateUserRequestBody().withUser(userOption)));
            bindUserGroup(userEntity, createUserResponse);
            log.info("新增华为云用户成功{}", createUserResponse);
            updateLoginProtect(iamClient, createUserResponse, userEntity);
            userEntity.setPassword(userOption.getPassword());
            userEntity.setUserId(createUserResponse.getUser().getId());
            huaweiCloudResponse.setUserEntity(userEntity);
            sendNotice(userEntity);
        } catch (Exception e) {
            log.error("新增华为云用户异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "新增华为云用户异常");
        }
        return huaweiCloudResponse;
    }

    private void bindUserGroup(UserEntity userEntity, CreateUserResponse createUserResponse) {
        if (null != userEntity.getGroupIdList() && !userEntity.getGroupIdList().isEmpty()) {
            GroupEntity groupEntity = new GroupEntity();
            groupEntity.setGroupIdList(userEntity.getGroupIdList());
            groupEntity.setUserId(createUserResponse.getUser().getId());
            groupEntity.setAccountId(userEntity.getAccountId());
            huaweiCloudUserGroupHandler.keystoneAddUserToGroup(groupEntity);
        }
    }

    private CreateUserOption getCreateUserOption(UserEntity userEntity) {
        CreateUserOption userOption = new CreateUserOption();
        userOption.setName(userEntity.getUserName());
        userOption.setEmail(userEntity.getEmail());
        if (StringUtils.isNotEmpty(userEntity.getPhone())) {
            userOption.setAreacode("0086");
            userOption.setPhone(userEntity.getPhone());
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
        userOption.setDescription(userEntity.getRemark());
        userOption.setPwdStatus(false);
        userOption.setEnabled(true);
        if (userEntity.isApiUser()) {
            userOption.setAccessMode("programmatic");
        } else {
            userOption.setAccessMode("console");
        }
        userOption.setXuserType("TenantIdp");
        userOption.setXuserId(userEntity.getEmail().split("@")[0]);
        userOption.setPassword(Utils.getPassword());
        userOption.setDomainId(cloudAccount.getDomainId());
        return userOption;
    }

    private void updateLoginProtect(IamClient iamClient, CreateUserResponse createUserResponse, UserEntity userEntity) {
        try {
            if (!userEntity.isApiUser()) {
                UpdateLoginProtectRequest updateLoginProtectRequest = new UpdateLoginProtectRequest();
                updateLoginProtectRequest.setUserId(createUserResponse.getUser().getId());
                UpdateLoginProjectReq updateLoginProjectReq = new UpdateLoginProjectReq();
                UpdateLoginProject loginProject = new UpdateLoginProject();
                loginProject.setEnabled(true);
                loginProject.setVerificationMethod("sms");
                updateLoginProjectReq.setLoginProtect(loginProject);
                updateLoginProtectRequest.setBody(updateLoginProjectReq);
                iamClient.updateLoginProtect(updateLoginProtectRequest);
            }
        } catch (Exception e) {
            log.info("华为云为用户增加手机登录验证失败:{} {}", e, createUserResponse.getUser().getEmail());
        }
    }

    private void sendNotice(UserEntity userEntity) throws Exception {
        if (!userEntity.isApiUser()) {
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
            Map<String, Object> messageParam = new HashMap<>();
            messageParam.put("cloud", cloudAccount.getMemo() + "/" + cloudAccount.getName() + "/" + cloudAccount.getDomainId());
            messageParam.put("username", userEntity.getUserName());
            messageParam.put("password", userEntity.getPassword());
            messageParam.put("url", "*由于开启云商单点登录，关闭密码登录方式*\\n*请通过[运维平台](https://yw-sso.ttyuyin.com/index/dashboard)->"
                    + "[云泽平台](https://yw-cloud.ttyuyin.com/tt-cloud/cloud/center)进行登录*");
            String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(userEntity.getEmail(), content);
        }
    }

    /**
     * 查詢用戶是否存在
     *
     * @param userEntity userEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse queryUserExists(UserEntity userEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            if (StringUtils.isNotEmpty(userEntity.getAccountId())) {
                UserAuthEntity userAuthInfo = new UserAuthEntity();
                userAuthInfo.setAccountId(userEntity.getAccountId());
                userAuthInfo.setUname(userEntity.getUserName());
                UserAuthEntity userAuthEntity = cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
                CloudAccountRequest cloudAccountRequest = new CloudAccountRequest();
                cloudAccountRequest.setId(userEntity.getAccountId());
                CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfo(cloudAccountRequest);
                if (null != userAuthEntity) {
                    log.info("查询华为云用户存在:{}", userAuthEntity.getUname());
                    UserEntity userEntity1 = new UserEntity();
                    userEntity1.setUserId(userAuthEntity.getUid());
                    userEntity1.setUserName(userAuthEntity.getUname());
                    huaweiCloudResponse.setUserEntity(userEntity1);
                } else {
                    huaweiCloudResponse.setUserEntity(queryUser(userEntity));
                }
                huaweiCloudResponse.setLoginUrl("https://auth.huaweicloud.com/authui/federation/websso?domain_id="
                        + cloudAccount.getDomainId() + "&idp=qw-sso&protocol=saml");
                return huaweiCloudResponse;
            } else {
                List<CloudAccountEntity> accountEntityList =  cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
                accountEntityList = accountEntityList.stream()
                        .filter(item -> Constants.HUAWEI_CLOUD.equals(item.getCloudType()) && item.isConnectStatus())
                        .collect(Collectors.toList());
                for (CloudAccountEntity cloudAccountEntity : accountEntityList) {
                    KeystoneListUsersRequest keystoneListUsersRequest = new KeystoneListUsersRequest();
                    keystoneListUsersRequest.setName(userEntity.getUserName());
                    keystoneListUsersRequest.setEnabled(true);
                    KeystoneListUsersResponse keystoneListUsersResponse =
                            huaweiCloudHttpConfig.getHuaweiIamClient(cloudAccountEntity.getId())
                                    .keystoneListUsers(keystoneListUsersRequest);
                    if (null == keystoneListUsersResponse.getUsers() || keystoneListUsersResponse.getUsers().isEmpty()) {
                        log.info("查询华为云用户为空，用户不存在");
                        continue;
                    }
                    if (getHuaweiCloudResponse(userEntity, huaweiCloudResponse, keystoneListUsersResponse, cloudAccountEntity)) {
                        return huaweiCloudResponse;
                    }
                }
            }
        } catch (Exception e) {
            log.info("查询用户是否存在异常", e);
            huaweiCloudResponse.getHeader().setErrorMsg("查询用户是否存在异常：" + e.getMessage());
        }
        return huaweiCloudResponse;
    }

    private UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        KeystoneListUsersRequest keystoneListUsersRequest = new KeystoneListUsersRequest();
        keystoneListUsersRequest.setName(userEntity.getUserName());
        keystoneListUsersRequest.setEnabled(true);
        KeystoneListUsersResponse keystoneListUsersResponse =
                huaweiCloudHttpConfig.getHuaweiIamClient(userEntity.getAccountId())
                        .keystoneListUsers(keystoneListUsersRequest);
        if (null == keystoneListUsersResponse.getUsers() || keystoneListUsersResponse.getUsers().isEmpty()) {
            log.info("查询华为云用户为空，用户不存在");
            if (!userEntity.getUserName().contains("@52tt.com")) {
                userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                return queryUser(userEntity);
            }
        }
        for (KeystoneListUsersResult user : keystoneListUsersResponse.getUsers()) {
            if (user.getName().equals(userEntity.getUserName())) {
                userInfo = new UserEntity();
                userInfo.setUserId(user.getId());
                userInfo.setUserName(user.getName());
            }
        }
        return userInfo;
    }

    private boolean getHuaweiCloudResponse(UserEntity userEntity,
            HuaweiCloudResponse huaweiCloudResponse,
            KeystoneListUsersResponse keystoneListUsersResponse, CloudAccountEntity cloudAccountEntity) {
        for (KeystoneListUsersResult user : keystoneListUsersResponse.getUsers()) {
            if (user.getName().equals(userEntity.getUserName())) {
                log.info("查询华为云用户存在:{}", user.getName());
                UserEntity userEntity1 = new UserEntity();
                userEntity1.setUserId(user.getId());
                userEntity1.setUserName(user.getName());
                huaweiCloudResponse.setUserEntity(userEntity1); // domain_id
                huaweiCloudResponse.setLoginUrl(
                        "https://auth.huaweicloud.com/authui/federation/websso?domain_id=" + cloudAccountEntity.getDomainId() +"&idp=qw-sso&protocol=saml");
                return true;
            }
        }
        return false;
    }

    public CloudAllUserResponse queryUserListByAccount(CloudAccountEntity cloudAccount, String nextMarker) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'queryUserListByAccount'");
    }

}
