package com.tt.cloud.thread;

import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.v2.LBItem;
import com.tt.cloud.bean.v2.NetTypeEnum;
import com.tt.cloud.bean.v2.ResourceTypeEnum;
import com.tt.cloud.config.VolcEngineCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import com.volcengine.ApiException;
import com.volcengine.iam20210801.model.ListProjectsRequest;
import com.volcengine.iam20210801.model.ListProjectsResponse;
import com.volcengine.iam20210801.model.ProjectForListProjectsOutput;
import com.volcengine.vke.model.ItemForListClustersOutput;
import com.volcengine.vpc.model.SubnetForDescribeSubnetsOutput;
import com.volcengine.vpc.model.VpcForDescribeVpcsOutput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/2/28 11:19
 */
@Slf4j
public class VolcEngineLBCreateNoticeThread implements Runnable {
    private final LBItem lbItem;
    private final CloudAccountEntity cloudAccountEntity;
    private final FlyBookService flyBookService;
    private final ApplicationContext applicationContext;
    private final VolcEngineCloudHttpConfig volcEngineCloudHttpConfig;

    public VolcEngineLBCreateNoticeThread(LBItem lbItem, CloudAccountEntity cloudAccountEntity,
                                          FlyBookService flyBookService, ApplicationContext applicationContext,
                                          VolcEngineCloudHttpConfig volcEngineCloudHttpConfig) {
        this.lbItem = lbItem;
        this.cloudAccountEntity = cloudAccountEntity;
        this.flyBookService = flyBookService;
        this.applicationContext = applicationContext;
        this.volcEngineCloudHttpConfig = volcEngineCloudHttpConfig;
    }

    @Override
    public void run() {
        try {
            String content = Utils.readTxtFile("/fly_book_template/create_volcengine_cloud_lb.txt");
            Map<String, Object> messageParam = new HashMap<>();
            fillTitle(messageParam);
            messageParam.put("cloud", cloudAccountEntity.getDomainId() + "/" + cloudAccountEntity.getName());
            String region = Constants.VOLC_ENGINE_REGION_MAP.get(lbItem.getRegion_id());
            messageParam.put("region", StringUtils.isNotEmpty(region) ? region : lbItem.getRegion_id());
            fillProject(messageParam);
            messageParam.put("lb_name", lbItem.getLb_name());
            messageParam.put("type", "负载均衡");
            if (ResourceTypeEnum.HOST_LB.getResourceType().equals(lbItem.getResource_type())) {
                messageParam.put("cluster_istio", StringUtils.EMPTY);
            } else {
                fillCluster(messageParam);
            }
            dealLBType(messageParam);
            dealNetType(messageParam);
            fill_VPC_Subnet(messageParam);
            messageParam.put("lb_id", StringUtils.isNotEmpty(lbItem.getLb_id()) ? lbItem.getLb_id() : "-");
            messageParam.put("createTime", new SimpleDateFormat(Constants.DATA_FORMAT).format(new Date()));
            messageParam.put("username", lbItem.getUsername());
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(lbItem.getEmail(), content);
        } catch (Exception e) {
            log.error("发送火山云LB创建成功消息异常", e);
        }
    }

    private void fillTitle(Map<String, Object> messageParam) {
        if (Constants.SUCCESS.equals(lbItem.getResultCode())) {
            messageParam.put("color", "green");
            messageParam.put("result", "成功");
            messageParam.put("error", StringUtils.EMPTY);
        } else {
            messageParam.put("color", "red");
            messageParam.put("result", "失败");
            String error = ",\n"
                    + "{\n"
                    + "\"tag\": \"div\",\n"
                    + "\"fields\": [\n"
                    + "{\n"
                    + "\"is_short\": true,\n"
                    + "\"text\": {\n"
                    + "\"tag\": \"lark_md\",\n"
                    + "\"content\": \"**\uD83D\uDDF3错误信息：**\\n" + lbItem.getResultMsg() + "\"\n"
                    + "}\n"
                    + "}\n"
                    + "]\n"
                    + "}";
            messageParam.put("error", error);
        }
    }

    private void fillCluster(Map<String, Object> messageParam) throws ApiException {
        messageParam.put("cluster_istio", "{\n"
                + "      \"tag\": \"div\",\n"
                + "      \"fields\": [\n"
                + "        {\n"
                + "          \"is_short\": true,\n"
                + "          \"text\": {\n"
                + "            \"tag\": \"lark_md\",\n"
                + "            \"content\": \"**\uD83D\uDDF3集群名称：**\\n" + getCluster_name() + "\"\n"
                + "          }\n"
                + "        },\n"
                + "        {\n"
                + "          \"is_short\": true,\n"
                + "          \"text\": {\n"
                + "            \"tag\": \"lark_md\",\n"
                + "            \"content\": \"**\uD83D\uDCDD是否istio网关：**\\n" + ("1".equals(lbItem.getIstio_net()) ? "是": "否") + "\"\n"
                + "          }\n"
                + "        }\n"
                + "      ]\n"
                + "    },");
    }

    private String getCluster_name() throws ApiException {
        String cluster_name = StringUtils.EMPTY;
        com.volcengine.vke.model.ListClustersResponse listClustersResponse =
                volcEngineCloudHttpConfig.getVolcEngineVkeApi(cloudAccountEntity.getId(), lbItem.getRegion_id())
                        .listClusters(new com.volcengine.vke.model.ListClustersRequest());

        Optional<ItemForListClustersOutput> clusterOptional = listClustersResponse.getItems().stream()
                .filter(cluster -> cluster.getId().equals(lbItem.getCluster_id())).findFirst();
        if (clusterOptional.isPresent()) {
            cluster_name = clusterOptional.get().getName();
        }
        return cluster_name;
    }

    private void fill_VPC_Subnet(Map<String, Object> messageParam) throws ApiException {
        com.volcengine.vpc.model.DescribeVpcsRequest describeVpcsRequest = new com.volcengine.vpc.model.DescribeVpcsRequest();
        describeVpcsRequest.setPageSize(100);
        com.volcengine.vpc.model.DescribeVpcsResponse describeVpcsResponse =
                volcEngineCloudHttpConfig.getVolcEngineVpcApi(cloudAccountEntity.getId(), lbItem.getRegion_id()).describeVpcs(describeVpcsRequest);
        if (null != describeVpcsResponse.getVpcs()) {
            List<VpcForDescribeVpcsOutput> vpcForDescribeVpcsOutputs = describeVpcsResponse.getVpcs().stream()
                    .filter(vpcForDescribeVpcsOutput -> "Available".equals(vpcForDescribeVpcsOutput.getStatus())).collect(Collectors.toList());

            Optional<VpcForDescribeVpcsOutput> vpcOptional = vpcForDescribeVpcsOutputs.stream().filter(vpc -> vpc.getVpcId().equals(lbItem.getVpc_id())).findFirst();
            vpcOptional.ifPresent(
                    vpc -> messageParam.put("vpc", vpc.getVpcName() + "【" + vpc.getCidrBlock() + "】"));
        }

        com.volcengine.vpc.model.DescribeSubnetsRequest describeSubnetsRequest = new com.volcengine.vpc.model.DescribeSubnetsRequest();
        describeSubnetsRequest.setPageSize(100);
        describeSubnetsRequest.setVpcId(lbItem.getVpc_id());
        com.volcengine.vpc.model.DescribeSubnetsResponse describeSubnetsResponse =
                volcEngineCloudHttpConfig.getVolcEngineVpcApi(cloudAccountEntity.getId(), lbItem.getRegion_id())
                        .describeSubnets(describeSubnetsRequest);
        if (null != describeSubnetsResponse.getSubnets()) {
            if ("clb".equals(lbItem.getLb_type())) {
                Optional<SubnetForDescribeSubnetsOutput> subnetOptional = describeSubnetsResponse.getSubnets().stream()
                        .filter(subnet -> subnet.getSubnetId().equals(lbItem.getSubnet_id())).findFirst();
                subnetOptional.ifPresent(subnet -> messageParam.put("subnet",
                        subnet.getSubnetName() + "【" + subnet.getCidrBlock() + "】"));
            } else {
                List<SubnetForDescribeSubnetsOutput> subnetsOutputs = describeSubnetsResponse.getSubnets().stream()
                        .filter(subnet -> lbItem.getSubnet_ids().contains(subnet.getSubnetId())).collect(Collectors.toList());
                StringBuilder result = new StringBuilder();
                for (SubnetForDescribeSubnetsOutput subnetsOutput : subnetsOutputs) {
                    result.append(subnetsOutput.getSubnetName()).append("【").append(subnetsOutput.getCidrBlock()).append("】，");
                }
                messageParam.put("subnet", result);
            }
        }


    }

    private void fillProject(Map<String, Object> messageParam) throws ApiException {
        ListProjectsRequest listProjectsRequest = new ListProjectsRequest();
        listProjectsRequest.setLimit(100);
        ListProjectsResponse listProjectsResponse =
                volcEngineCloudHttpConfig.getVolcEngineIam20210801Api(cloudAccountEntity.getId(), lbItem.getRegion_id())
                        .listProjects(listProjectsRequest);
        Optional<ProjectForListProjectsOutput> projectOptional = listProjectsResponse.getProjects()
                .stream().filter(project -> project.getProjectName().equals(lbItem.getProject_id())).findFirst();
        projectOptional.ifPresent(
                projectsOutput -> messageParam.put("project", projectsOutput.getDisplayName()));
    }

    private void dealLBType(Map<String, Object> messageParam) {
        if ("clb".equals(lbItem.getLb_type())) {
            messageParam.put("lb_type", "负载均衡(CLB)");
            String zone_flavor = "\n"
                    + "    {\n"
                    + "      \"tag\": \"div\",\n"
                    + "      \"fields\": [\n"
                    + "        {\n"
                    + "          \"is_short\": true,\n"
                    + "          \"text\": {\n"
                    + "            \"tag\": \"lark_md\",\n"
                    + "            \"content\": \"**\uD83D\uDCDD规格：**\\n" + lbItem.getFlavor_id() + "\"\n"
                    + "          }\n"
                    + "        }\n"
                    + "      ]\n"
                    + "    },";
            messageParam.put("zone_flavor", zone_flavor);
        } else {
            messageParam.put("lb_type", "应用型负载均衡(ALB)");
            messageParam.put("zone_flavor", StringUtils.EMPTY);
        }
    }

    private void dealNetType(Map<String, Object> messageParam) {
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            messageParam.put("net_type", "公网");
            String band_width = ",\n"
                    + "        {\n"
                    + "          \"is_short\": true,\n"
                    + "          \"text\": {\n"
                    + "            \"tag\": \"lark_md\",\n"
                    + "            \"content\": \"**\uD83D\uDCDD网络带宽：**\\n" + lbItem.getBandwidth_size() + "\"\n"
                    + "          }\n"
                    + "        }";
            messageParam.put("band_width", band_width);
        } else {
            messageParam.put("net_type", "内网");
            messageParam.put("band_width", StringUtils.EMPTY);
        }
    }
}
