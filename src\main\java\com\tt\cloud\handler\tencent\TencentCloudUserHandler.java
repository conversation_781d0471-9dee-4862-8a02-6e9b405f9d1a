package com.tt.cloud.handler.tencent;

import com.tencentcloudapi.cam.v20190116.models.AccessKey;
import com.tencentcloudapi.cam.v20190116.models.AddUserRequest;
import com.tencentcloudapi.cam.v20190116.models.AddUserResponse;
import com.tencentcloudapi.cam.v20190116.models.AttachPolicyInfo;
import com.tencentcloudapi.cam.v20190116.models.GetSecurityLastUsedRequest;
import com.tencentcloudapi.cam.v20190116.models.GetSecurityLastUsedResponse;
import com.tencentcloudapi.cam.v20190116.models.GetUserRequest;
import com.tencentcloudapi.cam.v20190116.models.GetUserResponse;
import com.tencentcloudapi.cam.v20190116.models.GroupInfo;
import com.tencentcloudapi.cam.v20190116.models.ListAccessKeysRequest;
import com.tencentcloudapi.cam.v20190116.models.ListAccessKeysResponse;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserAllPoliciesRequest;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserAllPoliciesResponse;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserPoliciesRequest;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserPoliciesResponse;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsForUserRequest;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsForUserResponse;
import com.tencentcloudapi.cam.v20190116.models.ListUsersRequest;
import com.tencentcloudapi.cam.v20190116.models.ListUsersResponse;
import com.tencentcloudapi.cam.v20190116.models.LoginActionMfaFlag;
import com.tencentcloudapi.cam.v20190116.models.SetMfaFlagRequest;
import com.tencentcloudapi.cam.v20190116.models.SubAccountInfo;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.GroupEntity;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.bean.PermanentAccessKeyEntity;
import com.tt.cloud.bean.TencentCloudResponse;
import com.tt.cloud.bean.UserAuthEntity;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.UserGroupInfo;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;

import lombok.var;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 腾讯云子用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/18 9:31
 */
@Slf4j
@Component
public class TencentCloudUserHandler {
    @Resource
    private TencentCloudHttpConfig tencentCloudHttpConfig;
    @Resource
    private TencentCloudUserGroupHandler tencentCloudUserGroupHandler;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private ApplicationContext applicationContext;


    /**
     * 新增腾讯云子用户
     *
     * @param userEntity userEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse addUser(UserEntity userEntity) {
        log.info("查询新增子用户:{} 是否在腾讯云存在", userEntity.getUserName());
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            log.info("新增腾讯云子用户：{}", userEntity.getUserName());
            AddUserRequest request = new AddUserRequest();
            request.setName(userEntity.getUserName());
            request.setRemark(userEntity.getRemark());
            if (userEntity.isApiUser()) {
                request.setConsoleLogin(0L);
                request.setUseApi(1L);
            } else {
                request.setConsoleLogin(1L);
                request.setUseApi(0L);
            }
            request.setNeedResetPassword(0L);
            request.setPhoneNum(userEntity.getPhone());
            request.setEmail(userEntity.getEmail());
            AddUserResponse addUserResponse = tencentCloudHttpConfig.getTencentCamClient(userEntity.getAccountId()).AddUser(request);
            if (null != userEntity.getGroupIdList() && !userEntity.getGroupIdList().isEmpty()) {
                log.info("腾讯云将用户添加到用户组");
                for (String groupId : userEntity.getGroupIdList()) {
                    GroupEntity groupEntity = new GroupEntity();
                    groupEntity.setGroupId(groupId);
                    groupEntity.setAccountId(userEntity.getAccountId());
                    groupEntity.setUserId(String.valueOf(addUserResponse.getUid()));
                    tencentCloudUserGroupHandler.addUserToGroup(groupEntity);
                }
            }
            log.info("新增腾讯云子用户:{}成功", addUserResponse);
            setMfaFlag(userEntity, addUserResponse);
            CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
            userEntity.setPassword(addUserResponse.getPassword());
            userEntity.setUserId(String.valueOf(addUserResponse.getUid()));
            tencentCloudResponse.setUserEntity(userEntity);
            sendNotice(userEntity, cloudAccountEntity);
        } catch (Exception e) {
            log.error("新增腾讯云子用户异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("新增腾讯云子用户异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    private void setMfaFlag(UserEntity userEntity, AddUserResponse addUserResponse) {
        try {
            if (!userEntity.isApiUser()) {
                SetMfaFlagRequest setMfaFlagRequest = new SetMfaFlagRequest();
                LoginActionMfaFlag loginActionMfaFlag = new LoginActionMfaFlag();
                loginActionMfaFlag.setPhone(1L);
                setMfaFlagRequest.setLoginFlag(loginActionMfaFlag);
                setMfaFlagRequest.setActionFlag(loginActionMfaFlag);
                setMfaFlagRequest.setOpUin(addUserResponse.getUin());
                tencentCloudHttpConfig.getTencentCamClient(userEntity.getAccountId()).SetMfaFlag(setMfaFlagRequest);
            }
        } catch (Exception e) {
            log.error("为腾讯云新增用户设置登录及高位操作手机验证失败:{} {}", e, userEntity.getUserName());
        }
    }

    private void sendNotice(UserEntity userEntity, CloudAccountEntity cloudAccountEntity)
            throws Exception {
        if (!userEntity.isApiUser()) {
            Map<String, Object> messageParam = new HashMap<>();
            messageParam.put("cloud", cloudAccountEntity.getMemo() + "/" + cloudAccountEntity.getName() + "/" + cloudAccountEntity.getDomainId());
            messageParam.put("username", userEntity.getUserName());
            messageParam.put("password", userEntity.getPassword());
            messageParam.put("url", "*由于开启云商单点登录，关闭密码登录方式*\\n*请通过[运维平台](https://yw-sso.ttyuyin.com/index/dashboard)->"
                    + "[云泽平台](https://yw-cloud.ttyuyin.com/tt-cloud/cloud/center)进行登录*");
            String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(userEntity.getEmail(), content);
        }
    }

    /**
     * 查詢用戶是否存在
     *
     * @param userEntity userEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse queryUserExists(UserEntity userEntity) {
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            UserAuthEntity userAuthInfo = new UserAuthEntity();
            userAuthInfo.setAccountId(userEntity.getAccountId());
            userAuthInfo.setUname(userEntity.getUserName());
            UserAuthEntity userAuthEntity = cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
            if (null != userAuthEntity) {
                UserEntity userInfo = new UserEntity();
                userInfo.setUserId(userAuthEntity.getUid());
                userInfo.setUserIn(userAuthEntity.getUin());
                userInfo.setUserName(userAuthEntity.getUname());
                tencentCloudResponse.setUserEntity(userInfo);

                CloudAccountRequest cloudAccountRequest = new CloudAccountRequest();
                cloudAccountRequest.setId(userAuthEntity.getAccountId());
                CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfo(cloudAccountRequest);
                tencentCloudResponse.setLoginUrl("https://cloud.tencent.com/login/subAccount/"
                        + cloudAccount.getDomainId() + "?type=subAccount&username=" + userInfo.getUserName());
                if (cloudAccount.isInternational()) {
                    tencentCloudResponse.setLoginUrl("https://www.tencentcloud.com/account/login/subAccount/"
                            + cloudAccount.getDomainId() + "?type=subAccount&username=" + userInfo.getUserName());
                }
            } else {
                tencentCloudResponse.setUserEntity(queryUser(userEntity));
            }
        } catch (Exception e) {
            log.error("查询腾讯云用户是否存在异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("查询腾讯云用户是否存在异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    private UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        try {
            GetUserRequest request = new GetUserRequest();
            request.setName(userEntity.getUserName());
            GetUserResponse response =
                    tencentCloudHttpConfig.getTencentCamClient(userEntity.getAccountId()).GetUser(request);
            if (null != response) {
                userInfo = new UserEntity();
                userInfo.setUserId(String.valueOf(response.getUid()));
                userInfo.setUserIn(String.valueOf(response.getUin()));
                userInfo.setUserName(response.getName());
            }
            } catch (TencentCloudSDKException e) {
                if ("the uid is not exit in receiverInfo".equals(e.getMessage())) {
                    if (!userEntity.getUserName().contains("@52tt.com")) {
                        userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                        return queryUser(userEntity);
                    }
                }
            }
            return userInfo;
        }


        
    public Object queryUserListByAccount(CloudAccountEntity cloudAccount) {
        List<UserEntity> userList = new ArrayList<>();
        try {
            log.info("腾讯云子用户详情列表");
            ListUsersRequest request = new ListUsersRequest();
            ListUsersResponse response = tencentCloudHttpConfig.getTencentCamClient(cloudAccount.getId()).ListUsers(request);

            if (response != null && response.getData() != null && response.getData().length > 0) {
                for (SubAccountInfo subAccount : response.getData()) {
                    UserEntity user = new UserEntity();
                    user.setUserId(String.valueOf(subAccount.getUid()));
                    user.setUserIn(String.valueOf(subAccount.getUin()));
                    user.setUserName(subAccount.getName());
                    user.setCreateTime(subAccount.getCreateTime());
                    user.setRemark(subAccount.getRemark());
                    user.setPhone(subAccount.getPhoneNum());
                    user.setEmail(subAccount.getEmail());
                    user.setConsoleLogin(subAccount.getConsoleLogin());
                    user.setAccountId(cloudAccount.getDomainId());
                    userList.add(user);
                }
            }

            // 分批 分线程 完善用户详细数据
            // 2. 定义每个线程处理的记录数
            final int BATCH_SIZE = 10;

            // 3. 创建一个固定大小的线程池
            // 线程数可以根据CPU核心数或任务特性来定，这里为了演示，设为50
            int numberOfThreads = 50;
            ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);

            // 4. 对大列表进行分区，并为每个分区提交一个任务
            for (int i = 0; i < userList.size(); i += BATCH_SIZE) {
                // 计算当前批次的结束索引，注意防止越界
                int start = i;
                int end = Math.min(i + BATCH_SIZE, userList.size());
                // 使用 subList 创建子列表视图，这不会实际复制数据，效率很高

                // 创建一个处理该子列表的任务（Runnable）
                Runnable task = () -> {
                    attachUserInfos(userList, cloudAccount.getId(), start, end);
                };
                // 将任务提交给线程池执行
                executor.submit(task);
            }

            executor.shutdown(); // 不再接受新任务
            try {
                // 等待所有已提交的任务执行完毕，设置一个最长等待时间
                if (!executor.awaitTermination(10, TimeUnit.MINUTES)) {
                    System.err.println("线程池在超时后仍未终止，强制关闭。");
                    executor.shutdownNow(); // 尝试取消所有正在执行的任务
                }
            } catch (InterruptedException e) {
                System.err.println("主线程在等待时被中断，强制关闭线程池。");
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            return userList;

        } catch (Exception e) {
            log.error("腾讯云子用户详情列表异常", e);
            throw new RuntimeException("腾讯云子用户详情列表异常：" + e.getMessage());
        }
    }

    private void attachUserInfos(List<UserEntity> userList, String accountId, int start, int end) {
        if (userList == null || userList.isEmpty() || start < 0 || end > userList.size() || start >= end) {
            return;
        }
        try {
            // 获取用户信息
            for (int i = start; i < end; i++) {
                UserEntity user = userList.get(i);
                var client= tencentCloudHttpConfig.getTencentCamClient(accountId);

                //   https://console.cloud.tencent.com/api/explorer?Product=cam&Version=2019-01-16&Action=GetUser 
                // 获取最后登录时间 和登录IP


                // 1. 获取用户组列表
                try {
                    ListGroupsForUserRequest groupsRequest = new ListGroupsForUserRequest();
                    groupsRequest.setUid(Long.parseLong(user.getUserId()));
                    groupsRequest.setPage(1L);
                    groupsRequest.setRp(999L);
                    ListGroupsForUserResponse groupsResponse = client.ListGroupsForUser(groupsRequest);

                    if (groupsResponse != null && groupsResponse.getGroupInfo() != null && groupsResponse.getGroupInfo().length > 0) {
                        List<UserGroupInfo> groupIdList = new ArrayList<>();
                        for (GroupInfo groupInfo : groupsResponse.getGroupInfo()) {
                            UserGroupInfo userGroupInfo = new UserGroupInfo();
                            userGroupInfo.setGroupId(String.valueOf(groupInfo.getGroupId()));
                            userGroupInfo.setGroupName(groupInfo.getGroupName());
                            userGroupInfo.setRemark(groupInfo.getRemark());
                            groupIdList.add(userGroupInfo);
                        }
                        user.setGroupList(groupIdList);
                    }
                } catch (Exception e) {
                    log.error("获取用户组信息异常: {}", e.getMessage());
                }

                // 2. 获取用户权限列表
                try {
                    ListAttachedUserAllPoliciesRequest  policiesRequest = new ListAttachedUserAllPoliciesRequest ();
                    policiesRequest.setTargetUin(Long.parseLong(user.getUserIn()));
                    policiesRequest.setPage(1L);
                    policiesRequest.setRp(999L);
                    ListAttachedUserAllPoliciesResponse  policiesResponse = client.ListAttachedUserAllPolicies(policiesRequest);

                    if (policiesResponse != null && policiesResponse.getPolicyList() != null && policiesResponse.getPolicyList().length > 0) {
                        List<PermissionEntity> policyList = new ArrayList<>();
                        for (var policyInfo : policiesResponse.getPolicyList()) {
                            PermissionEntity permission = new PermissionEntity();
                            permission.setPolicyName(policyInfo.getPolicyName());
                            permission.setPolicyType(policyInfo.getStrategyType());
                            permission.setCreateMode(policyInfo.getCreateMode() != null ? Long.valueOf(policyInfo.getCreateMode()) : null);
                            permission.setAddTime(policyInfo.getAddTime());
                            permission.setRemark(policyInfo.getDescription());
                            permission.setBelongName(user.getUserName());
                            permission.setBelongType(Constants.BELONG_TYPE_USER);
                            if (policyInfo.getGroups() != null && policyInfo.getGroups().length > 0) {
                                permission.setBelongType(Constants.BELONG_TYPE_GROUP);
                                permission.setBelongId(String.valueOf(policyInfo.getGroups()[0].getGroupId()));
                                permission.setBelongName(policyInfo.getGroups()[0].getGroupName());
                            }

                            policyList.add(permission);
                        }
                        user.setPolicyList(policyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户权限信息异常: {}", e.getMessage());
                }

                // 3. 获取用户密钥ID列表
                try {
                    ListAccessKeysRequest accessKeysRequest = new ListAccessKeysRequest();
                    accessKeysRequest.setTargetUin(Long.parseLong(user.getUserIn()));
                    ListAccessKeysResponse accessKeysResponse = client.ListAccessKeys(accessKeysRequest);

                    if (accessKeysResponse != null && accessKeysResponse.getAccessKeys() != null && accessKeysResponse.getAccessKeys().length > 0) {
                        List<PermanentAccessKeyEntity> accessKeyList = new ArrayList<>();
                        for (AccessKey accessKey : accessKeysResponse.getAccessKeys()) {
                            PermanentAccessKeyEntity accessKeyEntity = new PermanentAccessKeyEntity();
                            accessKeyEntity.setAccess(accessKey.getAccessKeyId());
                            accessKeyEntity.setCreateTime(accessKey.getCreateTime());
                            accessKeyEntity.setStatus(accessKey.getStatus());
                            accessKeyEntity.setDescription(accessKey.getDescription());
                            accessKeyList.add(accessKeyEntity);

                            try {
                                GetSecurityLastUsedRequest  lastUsedRequest = new GetSecurityLastUsedRequest ();
                                 String[] secretIdList1 = {accessKey.getAccessKeyId()};
                                lastUsedRequest.setSecretIdList(secretIdList1);
                                GetSecurityLastUsedResponse lastUsedResponse = client.GetSecurityLastUsed(lastUsedRequest);
                                if (lastUsedResponse != null&&lastUsedResponse.getSecretIdLastUsedRows()!=null&&lastUsedResponse.getSecretIdLastUsedRows().length>0) {
                                    accessKeyEntity.setLastUsedDate(lastUsedResponse.getSecretIdLastUsedRows()[0].getLastUsedDate());
                                }
                            } catch (Exception e) {
                                log.error("获取用户密钥最后使用时间异常: {}", e.getMessage());
                            }
                        }
                        user.setAccessKeyList(accessKeyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户密钥信息异常: {}", e.getMessage());
                }

                userList.set(i, user);
            }
        } catch (Exception e) {
            log.error("腾讯云完善用户详细信息异常", e);
        }
    }

}
