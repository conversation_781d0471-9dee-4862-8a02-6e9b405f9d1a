package com.tt.cloud.handler.tencent;

import com.alibaba.fastjson.JSON;
import com.tencentcloudapi.cam.v20190116.models.AccessKey;
import com.tencentcloudapi.cam.v20190116.models.AddUserRequest;
import com.tencentcloudapi.cam.v20190116.models.AddUserResponse;
import com.tencentcloudapi.cam.v20190116.models.AttachPolicyInfo;
import com.tencentcloudapi.cam.v20190116.models.GetSecurityLastUsedRequest;
import com.tencentcloudapi.cam.v20190116.models.GetSecurityLastUsedResponse;
import com.tencentcloudapi.cam.v20190116.models.GetUserRequest;
import com.tencentcloudapi.cam.v20190116.models.GetUserResponse;
import com.tencentcloudapi.cam.v20190116.models.GroupInfo;
import com.tencentcloudapi.cam.v20190116.models.ListAccessKeysRequest;
import com.tencentcloudapi.cam.v20190116.models.ListAccessKeysResponse;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserAllPoliciesRequest;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserAllPoliciesResponse;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserPoliciesRequest;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserPoliciesResponse;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsForUserRequest;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsForUserResponse;
import com.tencentcloudapi.cam.v20190116.models.ListUsersRequest;
import com.tencentcloudapi.cam.v20190116.models.ListUsersResponse;
import com.tencentcloudapi.cam.v20190116.models.LoginActionMfaFlag;
import com.tencentcloudapi.cam.v20190116.models.SetMfaFlagRequest;
import com.tencentcloudapi.cam.v20190116.models.SubAccountInfo;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.CloudAllUserResponse;
import com.tt.cloud.bean.GroupEntity;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.bean.PermanentAccessKeyEntity;
import com.tt.cloud.bean.TencentCloudResponse;
import com.tt.cloud.bean.UserAuthEntity;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.UserGroupInfo;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;

import lombok.var;
import lombok.extern.slf4j.Slf4j;

import org.apache.logging.log4j.util.Strings;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 腾讯云子用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/18 9:31
 */
@Slf4j
@Component
public class TencentCloudUserHandler {
    @Resource
    private TencentCloudHttpConfig tencentCloudHttpConfig;
    @Resource
    private TencentCloudUserGroupHandler tencentCloudUserGroupHandler;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 新增腾讯云子用户
     *
     * @param userEntity userEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse addUser(UserEntity userEntity) {
        log.info("查询新增子用户:{} 是否在腾讯云存在", userEntity.getUserName());
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            log.info("新增腾讯云子用户：{}", userEntity.getUserName());
            AddUserRequest request = new AddUserRequest();
            request.setName(userEntity.getUserName());
            request.setRemark(userEntity.getRemark());
            if (userEntity.isApiUser()) {
                request.setConsoleLogin(0L);
                request.setUseApi(1L);
            } else {
                request.setConsoleLogin(1L);
                request.setUseApi(0L);
            }
            request.setNeedResetPassword(0L);
            request.setPhoneNum(userEntity.getPhone());
            request.setEmail(userEntity.getEmail());
            AddUserResponse addUserResponse = tencentCloudHttpConfig.getTencentCamClient(userEntity.getAccountId()).AddUser(request);
            if (null != userEntity.getGroupIdList() && !userEntity.getGroupIdList().isEmpty()) {
                log.info("腾讯云将用户添加到用户组");
                for (String groupId : userEntity.getGroupIdList()) {
                    GroupEntity groupEntity = new GroupEntity();
                    groupEntity.setGroupId(groupId);
                    groupEntity.setAccountId(userEntity.getAccountId());
                    groupEntity.setUserId(String.valueOf(addUserResponse.getUid()));
                    tencentCloudUserGroupHandler.addUserToGroup(groupEntity);
                }
            }
            log.info("新增腾讯云子用户:{}成功", addUserResponse);
            setMfaFlag(userEntity, addUserResponse);
            CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
            userEntity.setPassword(addUserResponse.getPassword());
            userEntity.setUserId(String.valueOf(addUserResponse.getUid()));
            tencentCloudResponse.setUserEntity(userEntity);
            sendNotice(userEntity, cloudAccountEntity);
        } catch (Exception e) {
            log.error("新增腾讯云子用户异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("新增腾讯云子用户异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    private void setMfaFlag(UserEntity userEntity, AddUserResponse addUserResponse) {
        try {
            if (!userEntity.isApiUser()) {
                SetMfaFlagRequest setMfaFlagRequest = new SetMfaFlagRequest();
                LoginActionMfaFlag loginActionMfaFlag = new LoginActionMfaFlag();
                loginActionMfaFlag.setPhone(1L);
                setMfaFlagRequest.setLoginFlag(loginActionMfaFlag);
                setMfaFlagRequest.setActionFlag(loginActionMfaFlag);
                setMfaFlagRequest.setOpUin(addUserResponse.getUin());
                tencentCloudHttpConfig.getTencentCamClient(userEntity.getAccountId()).SetMfaFlag(setMfaFlagRequest);
            }
        } catch (Exception e) {
            log.error("为腾讯云新增用户设置登录及高位操作手机验证失败:{} {}", e, userEntity.getUserName());
        }
    }

    private void sendNotice(UserEntity userEntity, CloudAccountEntity cloudAccountEntity)
            throws Exception {
        if (!userEntity.isApiUser()) {
            Map<String, Object> messageParam = new HashMap<>();
            messageParam.put("cloud", cloudAccountEntity.getMemo() + "/" + cloudAccountEntity.getName() + "/" + cloudAccountEntity.getDomainId());
            messageParam.put("username", userEntity.getUserName());
            messageParam.put("password", userEntity.getPassword());
            messageParam.put("url", "*由于开启云商单点登录，关闭密码登录方式*\\n*请通过[运维平台](https://yw-sso.ttyuyin.com/index/dashboard)->"
                    + "[云泽平台](https://yw-cloud.ttyuyin.com/tt-cloud/cloud/center)进行登录*");
            String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(userEntity.getEmail(), content);
        }
    }

    /**
     * 查詢用戶是否存在
     *
     * @param userEntity userEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse queryUserExists(UserEntity userEntity) {
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            UserAuthEntity userAuthInfo = new UserAuthEntity();
            userAuthInfo.setAccountId(userEntity.getAccountId());
            userAuthInfo.setUname(userEntity.getUserName());
            UserAuthEntity userAuthEntity = cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
            if (null != userAuthEntity) {
                UserEntity userInfo = new UserEntity();
                userInfo.setUserId(userAuthEntity.getUid());
                userInfo.setUserIn(userAuthEntity.getUin());
                userInfo.setUserName(userAuthEntity.getUname());
                tencentCloudResponse.setUserEntity(userInfo);

                CloudAccountRequest cloudAccountRequest = new CloudAccountRequest();
                cloudAccountRequest.setId(userAuthEntity.getAccountId());
                CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfo(cloudAccountRequest);
                tencentCloudResponse.setLoginUrl("https://cloud.tencent.com/login/subAccount/"
                        + cloudAccount.getDomainId() + "?type=subAccount&username=" + userInfo.getUserName());
                if (cloudAccount.isInternational()) {
                    tencentCloudResponse.setLoginUrl("https://www.tencentcloud.com/account/login/subAccount/"
                            + cloudAccount.getDomainId() + "?type=subAccount&username=" + userInfo.getUserName());
                }
            } else {
                tencentCloudResponse.setUserEntity(queryUser(userEntity));
            }
        } catch (Exception e) {
            log.error("查询腾讯云用户是否存在异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("查询腾讯云用户是否存在异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    private UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        try {
            GetUserRequest request = new GetUserRequest();
            request.setName(userEntity.getUserName());
            GetUserResponse response =
                    tencentCloudHttpConfig.getTencentCamClient(userEntity.getAccountId()).GetUser(request);
            if (null != response) {
                userInfo = new UserEntity();
                userInfo.setUserId(String.valueOf(response.getUid()));
                userInfo.setUserIn(String.valueOf(response.getUin()));
                userInfo.setUserName(response.getName());
            }
            } catch (TencentCloudSDKException e) {
                if ("the uid is not exit in receiverInfo".equals(e.getMessage())) {
                    if (!userEntity.getUserName().contains("@52tt.com")) {
                        userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                        return queryUser(userEntity);
                    }
                }
            }
            return userInfo;
        }


        
    public CloudAllUserResponse queryUserListByAccount(CloudAccountEntity cloudAccount,String nextMarker) {
        CloudAllUserResponse userListRst = new CloudAllUserResponse();
        try {
            log.info("腾讯云子用户详情列表");

            // userList 先从redis里获取，如果没有，再调用接口获取，然后存入redis
            // redis key: tencent-all-user-list
            // redis value: userList
            // redis 过期时间 20 分钟
            List<UserEntity> userList = new ArrayList<>();
            String redisKey = "tencent-all-user-list:" + cloudAccount.getId();

            try {
                // 先从 Redis 获取用户列表
                String cachedUserListJson = stringRedisTemplate.opsForValue().get(redisKey);
                if (cachedUserListJson != null) {
                    log.info("从Redis缓存获取到腾讯云用户列表，key: {}", redisKey);
                    // 将JSON字符串转换为用户列表
                    userList = JSON.parseArray(cachedUserListJson, UserEntity.class);
                } else {
                    log.info("Redis缓存中未找到腾讯云用户列表，开始调用API获取，key: {}", redisKey);
                    // Redis中没有数据，调用腾讯云API获取
                    ListUsersRequest request = new ListUsersRequest();
                    ListUsersResponse response = tencentCloudHttpConfig.getTencentCamClient(cloudAccount.getId()).ListUsers(request);

                    if (response != null && response.getData() != null && response.getData().length > 0) {
                        for (SubAccountInfo subAccount : response.getData()) {
                            UserEntity user = new UserEntity();
                            user.setUserId(String.valueOf(subAccount.getUid()));
                            user.setUserIn(String.valueOf(subAccount.getUin()));
                            user.setUserName(subAccount.getName());
                            user.setCreateTime(subAccount.getCreateTime());
                            user.setRemark(subAccount.getRemark());
                            user.setPhone(subAccount.getPhoneNum());
                            user.setEmail(subAccount.getEmail());
                            user.setConsoleLogin(subAccount.getConsoleLogin());
                            user.setAccountId(cloudAccount.getDomainId());
                            userList.add(user);
                        }

                        // 将用户列表存入Redis，过期时间20分钟
                        String userListJson = JSON.toJSONString(userList);
                        stringRedisTemplate.opsForValue().set(redisKey, userListJson, 20, TimeUnit.MINUTES);
                        log.info("已将腾讯云用户列表存入Redis缓存，key: {}, 用户数量: {}", redisKey, userList.size());
                    }
                }
            } catch (Exception e) {
                log.error("Redis操作异常，直接调用API获取用户列表: {}", e.getMessage(), e);
            }

            // nextMarker 有字符串转为int，如果不是数字型或空，则默认为0
            int pageIndex = 0;
            if (nextMarker != null && !nextMarker.isEmpty()) {
                try {
                    pageIndex = Integer.parseInt(nextMarker);
                    if (pageIndex < 0) {
                        pageIndex = 0; // 负数也默认为0
                    }
                } catch (NumberFormatException e) {
                    log.warn("nextMarker格式错误，无法转换为整数: {}, 使用默认值0", nextMarker);
                    pageIndex = 0;
                }
            }

            // 处理分页逻辑：将nextMarker转为int，并从userList中切片
            int size =20;
            int startIndex = pageIndex * size;
            int endIndex = (pageIndex + 1) * size;
            List<UserEntity> pagedUserList = userList;
            if (startIndex >= userList.size()) {
                return userListRst;
            }
            if (endIndex > userList.size()) {
                endIndex = userList.size();
                userListRst.setNextMarker(Strings.EMPTY);
            }else{
                userListRst.setNextMarker(String.valueOf(pageIndex + 1));
            }
            pagedUserList = userList.subList(startIndex, endIndex);

            // 对分页后的用户列表进行详细信息补充
            attachUserInfos(pagedUserList, cloudAccount.getId());

            // 设置返回结果
            userListRst.setUserList(pagedUserList);

        } catch (Exception e) {
            log.error("查询腾讯云子用户详情列表异常", e);
            throw new RuntimeException("查询腾讯云子用户详情列表异常" + e.getMessage());
        }
        return userListRst;
    }

    private void attachUserInfos(List<UserEntity> userList, String accountId) {
        if (userList == null || userList.isEmpty()) {
            return;
        }
        try {
            // 获取用户信息
            for (int i = 0; i < userList.size(); i++) {
                UserEntity user = userList.get(i);
                var client= tencentCloudHttpConfig.getTencentCamClient(accountId);

                //   https://console.cloud.tencent.com/api/explorer?Product=cam&Version=2019-01-16&Action=GetUser
                // 获取最后登录时间 和登录IP
                try {
                    GetUserRequest getUserRequest = new GetUserRequest();
                    getUserRequest.setName(user.getUserName());
                    GetUserResponse getUserResponse = client.GetUser(getUserRequest);

                    if (getUserResponse != null) {
                        user.setLastLoginIp(getUserResponse.getRecentlyLoginIP());
                        user.setLastLoginTime(getUserResponse.getRecentlyLoginTime());
                    }
                } catch (Exception e) {
                    log.error("获取用户登录信息异常: {}", e.getMessage());
                }


                // 1. 获取用户组列表
                try {
                    ListGroupsForUserRequest groupsRequest = new ListGroupsForUserRequest();
                    groupsRequest.setUid(Long.parseLong(user.getUserId()));
                    groupsRequest.setPage(1L);
                    groupsRequest.setRp(999L);
                    ListGroupsForUserResponse groupsResponse = client.ListGroupsForUser(groupsRequest);

                    if (groupsResponse != null && groupsResponse.getGroupInfo() != null && groupsResponse.getGroupInfo().length > 0) {
                        List<UserGroupInfo> groupIdList = new ArrayList<>();
                        for (GroupInfo groupInfo : groupsResponse.getGroupInfo()) {
                            UserGroupInfo userGroupInfo = new UserGroupInfo();
                            userGroupInfo.setGroupId(String.valueOf(groupInfo.getGroupId()));
                            userGroupInfo.setGroupName(groupInfo.getGroupName());
                            userGroupInfo.setRemark(groupInfo.getRemark());
                            groupIdList.add(userGroupInfo);
                        }
                        user.setGroupList(groupIdList);
                    }
                } catch (Exception e) {
                    log.error("获取用户组信息异常: {}", e.getMessage());
                }

                // 2. 获取用户权限列表
                try {
                    ListAttachedUserAllPoliciesRequest  policiesRequest = new ListAttachedUserAllPoliciesRequest ();
                    policiesRequest.setTargetUin(Long.parseLong(user.getUserIn()));
                    policiesRequest.setPage(1L);
                    policiesRequest.setRp(199L);
                    policiesRequest.setAttachType(0L);
                    ListAttachedUserAllPoliciesResponse  policiesResponse = client.ListAttachedUserAllPolicies(policiesRequest);

                    if (policiesResponse != null && policiesResponse.getPolicyList() != null && policiesResponse.getPolicyList().length > 0) {
                        List<PermissionEntity> policyList = new ArrayList<>();
                        for (var policyInfo : policiesResponse.getPolicyList()) {
                            PermissionEntity permission = new PermissionEntity();
                            permission.setPolicyName(policyInfo.getPolicyName());
                            permission.setPolicyType(policyInfo.getStrategyType());
                            permission.setCreateMode(policyInfo.getCreateMode() != null ? Long.valueOf(policyInfo.getCreateMode()) : null);
                            permission.setAddTime(policyInfo.getAddTime());
                            permission.setRemark(policyInfo.getDescription());
                            permission.setBelongName(user.getUserName());
                            permission.setBelongType(Constants.BELONG_TYPE_USER);
                            if (policyInfo.getGroups() != null && policyInfo.getGroups().length > 0) {
                                permission.setBelongType(Constants.BELONG_TYPE_GROUP);
                                permission.setBelongId(String.valueOf(policyInfo.getGroups()[0].getGroupId()));
                                permission.setBelongName(policyInfo.getGroups()[0].getGroupName());
                            }

                            policyList.add(permission);
                        }
                        user.setPolicyList(policyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户权限信息异常: {}", e.getMessage());
                }

                // 3. 获取用户密钥ID列表
                try {
                    ListAccessKeysRequest accessKeysRequest = new ListAccessKeysRequest();
                    accessKeysRequest.setTargetUin(Long.parseLong(user.getUserIn()));
                    ListAccessKeysResponse accessKeysResponse = client.ListAccessKeys(accessKeysRequest);

                    if (accessKeysResponse != null && accessKeysResponse.getAccessKeys() != null && accessKeysResponse.getAccessKeys().length > 0) {
                        List<PermanentAccessKeyEntity> accessKeyList = new ArrayList<>();
                        for (AccessKey accessKey : accessKeysResponse.getAccessKeys()) {
                            PermanentAccessKeyEntity accessKeyEntity = new PermanentAccessKeyEntity();
                            accessKeyEntity.setAccess(accessKey.getAccessKeyId());
                            accessKeyEntity.setCreateTime(accessKey.getCreateTime());
                            accessKeyEntity.setStatus(accessKey.getStatus());
                            accessKeyEntity.setDescription(accessKey.getDescription());
                            accessKeyList.add(accessKeyEntity);

                            try {
                                GetSecurityLastUsedRequest  lastUsedRequest = new GetSecurityLastUsedRequest ();
                                 String[] secretIdList1 = {accessKey.getAccessKeyId()};
                                lastUsedRequest.setSecretIdList(secretIdList1);
                                GetSecurityLastUsedResponse lastUsedResponse = client.GetSecurityLastUsed(lastUsedRequest);
                                if (lastUsedResponse != null&&lastUsedResponse.getSecretIdLastUsedRows()!=null&&lastUsedResponse.getSecretIdLastUsedRows().length>0) {
                                    accessKeyEntity.setLastUsedDate(lastUsedResponse.getSecretIdLastUsedRows()[0].getLastUsedDate());
                                }
                            } catch (Exception e) {
                                log.error("获取用户密钥最后使用时间异常: {}", e.getMessage());
                            }
                        }
                        user.setAccessKeyList(accessKeyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户密钥信息异常: {}", e.getMessage());
                }

                userList.set(i, user);
            }
        } catch (Exception e) {
            log.error("腾讯云完善用户详细信息异常", e);
        }
    }

}
