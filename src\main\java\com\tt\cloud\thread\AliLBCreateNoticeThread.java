package com.tt.cloud.thread;

import com.aliyun.resourcemanager20200331.models.*;
import com.aliyun.slb20140515.models.DescribeRegionsRequest;
import com.aliyun.slb20140515.models.DescribeRegionsResponse;
import com.aliyun.slb20140515.models.DescribeRegionsResponseBody;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.v2.LBItem;
import com.tt.cloud.bean.v2.NetTypeEnum;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class AliLBCreateNoticeThread implements Runnable {

    private static final String TEMPLATE_PATH = "/fly_book_template/create_ali_cloud_lb.txt";
    private static final String RESOURCE_STATUS_OK = "OK";
    private static final int PAGE_SIZE = 100;

    private final LBItem lbItem;
    private final CloudAccountEntity cloudAccountEntity;
    private final FlyBookService flyBookService;
    private final ApplicationContext applicationContext;
    private final AliCloudHttpConfig aliCloudHttpConfig;

    public AliLBCreateNoticeThread(LBItem lbItem, CloudAccountEntity cloudAccountEntity,
                                   FlyBookService flyBookService, ApplicationContext applicationContext,
                                   AliCloudHttpConfig aliCloudHttpConfig) {
        this.lbItem = lbItem;
        this.cloudAccountEntity = cloudAccountEntity;
        this.flyBookService = flyBookService;
        this.applicationContext = applicationContext;
        this.aliCloudHttpConfig = aliCloudHttpConfig;
    }

    @Override
    public void run() {
        try {
            String content = Utils.readTxtFile(TEMPLATE_PATH);
            Map<String, Object> messageParam = new HashMap<>();

            fillTitle(messageParam);
            messageParam.put("cloud", cloudAccountEntity.getDomainId() + "/" + cloudAccountEntity.getName());

            // 获取区域名称
            String regionName = fetchRegionName();
            if (regionName != null) {
                messageParam.put("region", regionName);
            }

            // 获取项目名称
            String projectName = fetchProjectName();
            if (projectName != null) {
                messageParam.put("project", projectName);
            }

            // 设置负载均衡参数
            messageParam.put("lb_name", lbItem.getLb_name());
            messageParam.put("lb_type", getLoadBalancerType(lbItem.getLb_type()));
            messageParam.put("net_type", getNetType(lbItem.getNet_type()));
            messageParam.put("lb_id", lbItem.getLb_id());
            messageParam.put("createTime", new SimpleDateFormat(Constants.DATA_FORMAT).format(new Date()));

            // 渲染内容并发送消息
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(lbItem.getEmail(), content);
        } catch (Exception e) {
            log.error("发送阿里云负载均衡创建消息通知异常: {}", e.getMessage(), e);
        }
    }

    private String fetchRegionName() throws Exception {
        DescribeRegionsResponse response = aliCloudHttpConfig
                .getSLBClient(cloudAccountEntity.getId(), lbItem.getRegion_id())
                .describeRegions(new DescribeRegionsRequest());

        return response.getBody().getRegions().region.stream()
                .filter(region -> region.regionId.equals(lbItem.getRegion_id()))
                .map(DescribeRegionsResponseBody.DescribeRegionsResponseBodyRegionsRegion::getLocalName)
                .findFirst()
                .orElse(null);
    }

    private String fetchProjectName() throws Exception {
        ListResourceGroupsResponse response = aliCloudHttpConfig
                .getResourceClient(cloudAccountEntity.getId())
                .listResourceGroups(new ListResourceGroupsRequest()
                        .setStatus(RESOURCE_STATUS_OK)
                        .setPageSize(PAGE_SIZE));

        return response.body.getResourceGroups().resourceGroup.stream()
                .filter(group -> group.id.equals(lbItem.getProject_id()))
                .map(ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup::getDisplayName)
                .findFirst()
                .orElse(null);
    }

    private String getLoadBalancerType(String lbType) {
        switch (lbType) {
            case "clb":
                return "传统型负载均衡(CLB)";
            case "alb":
                return "应用型负载均衡(ALB)";
            case "nlb":
                return "网络型负载均衡(NLB)";
            default:
                return "未知类型";
        }
    }

    private String getNetType(String netType) {
        if (NetTypeEnum.OPEN.getNetType().equals(netType)) {
            return "公网";
        } else if (NetTypeEnum.INTERNAL.getNetType().equals(netType)) {
            return "内网";
        }
        return "未知网络类型";
    }

    private void fillTitle(Map<String, Object> messageParam) {
        if (Constants.SUCCESS.equals(lbItem.getResultCode())) {
            messageParam.put("color", "green");
            messageParam.put("result", "成功");
            messageParam.put("error", StringUtils.EMPTY);
        } else {
            messageParam.put("color", "red");
            messageParam.put("result", "失败");
            messageParam.put("error", getErrorMessage());
        }
    }

    private String getErrorMessage() {
        return ",\n" + "{\n" +
                "\"tag\": \"div\",\n" +
                "\"fields\": [\n" +
                "{\n" +
                "\"is_short\": true,\n" +
                "\"text\": {\n" +
                "\"tag\": \"lark_md\",\n" +
                "\"content\": \"**\uD83D\uDDF3错误信息：**\\n" + lbItem.getResultMsg() + "\"\n" +
                "}\n" +
                "}\n" +
                "]\n" +
                "}";
    }
}
