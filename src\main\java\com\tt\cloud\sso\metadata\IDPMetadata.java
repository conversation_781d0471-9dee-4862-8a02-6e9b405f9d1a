package com.tt.cloud.sso.metadata;

import com.tt.cloud.sso.config.ServerProperty;

public class IDPMetadata {
    public static final String keystorePath = ServerProperty.getInstance().getProperty("keystorePath");
    public static final String keystorePass = ServerProperty.getInstance().getProperty("keystorePass");
    public static final String aliasName = ServerProperty.getInstance().getProperty("aliasName");
    public static final String idpFilePath = ServerProperty.getInstance().getProperty("idpMetadataFilePath");

    private static MetadataBean idpMetadata;

    private IDPMetadata() {}

    public static MetadataBean getInstance() {
        if (null == idpMetadata) {
            idpMetadata = new MetadataBean(idpFilePath);
        }
        return idpMetadata;
    }
}
