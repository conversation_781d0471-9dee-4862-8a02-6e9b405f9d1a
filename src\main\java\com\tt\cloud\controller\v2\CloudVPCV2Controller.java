package com.tt.cloud.controller.v2;

import com.aliyun.vpc20160428.models.DescribeVSwitchesRequest;
import com.aliyun.vpc20160428.models.DescribeVSwitchesResponse;
import com.aliyun.vpc20160428.models.DescribeVSwitchesResponseBody;
import com.aliyun.vpc20160428.models.DescribeVpcsResponseBody;
import com.huaweicloud.sdk.vpc.v2.VpcClient;
import com.huaweicloud.sdk.vpc.v2.model.ListSubnetsRequest;
import com.huaweicloud.sdk.vpc.v2.model.ListSubnetsResponse;
import com.huaweicloud.sdk.vpc.v2.model.ListVpcsRequest;
import com.huaweicloud.sdk.vpc.v2.model.ListVpcsResponse;
import com.huaweicloud.sdk.vpc.v2.model.Subnet;
import com.huaweicloud.sdk.vpc.v2.model.Vpc;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.vpc.v20170312.models.DescribeSubnetsRequest;
import com.tencentcloudapi.vpc.v20170312.models.DescribeSubnetsResponse;
import com.tencentcloudapi.vpc.v20170312.models.DescribeVpcsRequest;
import com.tencentcloudapi.vpc.v20170312.models.DescribeVpcsResponse;
import com.tencentcloudapi.vpc.v20170312.models.Filter;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudSubnetEntity;
import com.tt.cloud.bean.CloudVPCEntity;
import com.tt.cloud.bean.CommonRequest;
import com.tt.cloud.bean.v2.CommonListResponse;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.config.HuaweiCloudHttpConfig;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.config.VolcEngineCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.CommonUtil;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.volcengine.ApiException;
import com.volcengine.vpc.model.DescribeVpcAttributesRequest;
import com.volcengine.vpc.model.DescribeVpcAttributesResponse;
import com.volcengine.vpc.model.SubnetForDescribeSubnetsOutput;
import com.volcengine.vpc.model.VpcForDescribeVpcsOutput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/1/18 13:44
 */
@Slf4j
@RestController
@RequestMapping("/rest/v2/vpc")
public class CloudVPCV2Controller {
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private HuaweiCloudHttpConfig huaweiCloudHttpConfig;
    @Resource
    private TencentCloudHttpConfig tencentCloudHttpConfig;
    @Resource
    private VolcEngineCloudHttpConfig volcEngineCloudHttpConfig;
    @Resource
    private AliCloudHttpConfig aliCloudHttpConfig;

    @GetMapping("/list")
    public CommonListResponse queryVpcList(CommonRequest commonRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(commonRequest.getCloud_id())
                    || StringUtils.isEmpty(commonRequest.getRegion_id())) {
                return commonListResponse;
            }
            CloudAccountEntity cloudAccountEntity =
                    cloudAccountDao.queryCloudAccountInfoByCloudId(commonRequest.getCloud_id());
            if (null == cloudAccountEntity) {
                return commonListResponse;
            } else if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getHuaweiCloudVPCList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.TENCENT_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getTencentCloudVPCList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getVolcEngineVPCList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getAliCloudVPCList(commonRequest, commonListResponse, cloudAccountEntity);
            }
        } catch (Exception e) {
            log.error("查询云商VPC列表异常", e);
            commonListResponse.getHeader().setErrorMsg("查询云商VPC列表异常：" + e.getMessage());
        }
        return commonListResponse;
    }

    @GetMapping("/subnet/list")
    public CommonListResponse queryListVpcSubnet(CommonRequest commonRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(commonRequest.getCloud_id())
                    || StringUtils.isEmpty(commonRequest.getRegion_id())
                    || StringUtils.isEmpty(commonRequest.getVpc_id())) {
                return commonListResponse;
            }
            CloudAccountEntity cloudAccountEntity =
                    cloudAccountDao.queryCloudAccountInfoByCloudId(commonRequest.getCloud_id());
            if (null == cloudAccountEntity) {
                return commonListResponse;
            } else if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getHuaweiCloudVPCSubnetList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.TENCENT_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getTencentCloudVPCSubnetList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getVolcEngineVPCSubnetList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getAliCloudVPCSubnetList(commonRequest, commonListResponse, cloudAccountEntity);
            }
        } catch (Exception e) {
            log.error("查询云商VPC子网列表异常", e);
            commonListResponse.getHeader().setErrorMsg("查询云商VPC子网列表异常：" + e.getMessage());
        }
        return commonListResponse;
    }

    private void getAliCloudVPCSubnetList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                             CloudAccountEntity cloudAccountEntity) throws Exception {
        DescribeVSwitchesRequest describeVSwitchesRequest = new DescribeVSwitchesRequest();
        describeVSwitchesRequest.setVpcId(commonRequest.getVpc_id());
        describeVSwitchesRequest.setPageNumber(1);
        describeVSwitchesRequest.setPageSize(50);
        DescribeVSwitchesResponse describeVSwitchesResponse =
                aliCloudHttpConfig.getVPCClient(cloudAccountEntity.getId(), commonRequest.getRegion_id())
                        .describeVSwitches(describeVSwitchesRequest);
        List<Map<String, Object>> list = getAliCloudSubnets(commonRequest, describeVSwitchesResponse);
        commonListResponse.setList(list);
        commonListResponse.setTotal(describeVSwitchesResponse.body.totalCount);
    }

    private List<Map<String, Object>> getAliCloudSubnets(CommonRequest commonRequest, DescribeVSwitchesResponse describeVSwitchesResponse) {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch vSwitch : describeVSwitchesResponse.body.getVSwitches().vSwitch) {
            if (null != commonRequest.getZone_ids() && !commonRequest.getZone_ids().isEmpty()) {
                if (!commonRequest.getZone_ids().contains(vSwitch.getZoneId())) {
                    continue;
                }
            }
            Map<String, Object> vpc = new HashMap<>();
            vpc.put("subnet_id", vSwitch.getVSwitchId());
            vpc.put("subnet_name", vSwitch.getVSwitchName() + "【" + vSwitch.getCidrBlock() + "】" + "【" + vSwitch.getZoneId() + "】");
            list.add(vpc);
        }
        return list;
    }

    private void getVolcEngineVPCSubnetList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                              CloudAccountEntity cloudAccountEntity) throws ApiException {

        DescribeVpcAttributesRequest describeVpcAttributesRequest = new DescribeVpcAttributesRequest();
        describeVpcAttributesRequest.setVpcId(commonRequest.getVpc_id());
        DescribeVpcAttributesResponse describeVpcAttributesResponse =
                volcEngineCloudHttpConfig.getVolcEngineVpcApi(cloudAccountEntity.getId(), commonRequest.getRegion_id())
                        .describeVpcAttributes(describeVpcAttributesRequest);

        com.volcengine.vpc.model.DescribeSubnetsRequest describeSubnetsRequest = new com.volcengine.vpc.model.DescribeSubnetsRequest();
        describeSubnetsRequest.setPageSize(100);
        describeSubnetsRequest.setVpcId(describeVpcAttributesResponse.getVpcId());
        com.volcengine.vpc.model.DescribeSubnetsResponse describeSubnetsResponse =
                volcEngineCloudHttpConfig.getVolcEngineVpcApi(cloudAccountEntity.getId(), commonRequest.getRegion_id())
                        .describeSubnets(describeSubnetsRequest);

        List<SubnetForDescribeSubnetsOutput> subnets = describeSubnetsResponse.getSubnets();
        if (null == subnets) {
            subnets = new ArrayList<>();
        } else {
            subnets = subnets.stream().filter(subnet -> subnet.getStatus().equals("Available")
                            && extractSubnetPrefixes(subnet.getCidrBlock()).equals(extractSubnetPrefixes(describeVpcAttributesResponse.getCidrBlock())))
                    .sorted(Comparator.comparing(SubnetForDescribeSubnetsOutput::getZoneId))
                    .collect(Collectors.toList());
        }
        List<CloudSubnetEntity> subnetList = new ArrayList<>();
        for (SubnetForDescribeSubnetsOutput subnet : subnets) {
            CloudSubnetEntity cloudSubnetEntity = new CloudSubnetEntity();
            cloudSubnetEntity.setSubnet_id(subnet.getSubnetId());
            cloudSubnetEntity.setZone_id(subnet.getZoneId());
            cloudSubnetEntity.setSubnet_name("【"+ subnet.getZoneId() + "】" + subnet.getSubnetName() + "【" +subnet.getCidrBlock()+ "】");
            subnetList.add(cloudSubnetEntity);
        }
        commonListResponse.setTotal(subnetList.size());
        commonListResponse.setList(subnetList);
    }

    private void getTencentCloudVPCSubnetList(CommonRequest commonRequest, CommonListResponse commonListResponse,
            CloudAccountEntity cloudAccountEntity) throws TencentCloudSDKException {
        com.tencentcloudapi.vpc.v20170312.VpcClient vpcClient = tencentCloudHttpConfig
                .getTencentVpcClient(cloudAccountEntity.getId(), commonRequest.getRegion_id());
        DescribeSubnetsRequest describeSubnetsRequest = new DescribeSubnetsRequest();
        Filter filter = new Filter();
        filter.setName("vpc-id");
        filter.setValues(new String[]{commonRequest.getVpc_id()});
        describeSubnetsRequest.setFilters(new Filter[]{filter});
        DescribeSubnetsResponse describeSubnetsResponse = vpcClient.DescribeSubnets(
                describeSubnetsRequest);
        List<CloudSubnetEntity> subnetList = new ArrayList<>();
        for (com.tencentcloudapi.vpc.v20170312.models.Subnet subnet : describeSubnetsResponse.getSubnetSet()) {
            CloudSubnetEntity cloudSubnetEntity = new CloudSubnetEntity();
            cloudSubnetEntity.setSubnet_id(subnet.getSubnetId());
            cloudSubnetEntity.setSubnet_name(subnet.getSubnetName() + "【" +subnet.getCidrBlock()+ "】");
            subnetList.add(cloudSubnetEntity);
        }
        commonListResponse.setTotal(subnetList.size());
        commonListResponse.setList(subnetList);
    }

    private void getHuaweiCloudVPCSubnetList(CommonRequest commonRequest, CommonListResponse commonListResponse,
            CloudAccountEntity cloudAccountEntity) {
        VpcClient vpcClient = huaweiCloudHttpConfig.getHuaweiVpcClient(
                cloudAccountEntity.getId(),
                commonRequest.getRegion_id());
        ListSubnetsRequest listSubnetsRequest = new ListSubnetsRequest();
        listSubnetsRequest.setVpcId(commonRequest.getVpc_id());
        ListSubnetsResponse listSubnetsResponse = vpcClient.listSubnets(listSubnetsRequest);
        List<CloudSubnetEntity> subnetList = new ArrayList<>();
        for (Subnet subnet : listSubnetsResponse.getSubnets()) {
            CloudSubnetEntity cloudSubnetEntity = new CloudSubnetEntity();
            cloudSubnetEntity.setSubnet_id(subnet.getNeutronSubnetId());
            cloudSubnetEntity.setSubnet_name(subnet.getName() + "【" +subnet.getCidr()+ "】");
            subnetList.add(cloudSubnetEntity);
        }
        commonListResponse.setTotal(subnetList.size());
        commonListResponse.setList(subnetList);
    }

    private void getVolcEngineVPCList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                        CloudAccountEntity cloudAccountEntity) throws ApiException {
        com.volcengine.vpc.model.DescribeVpcsRequest describeVpcsRequest = new com.volcengine.vpc.model.DescribeVpcsRequest();
        describeVpcsRequest.setPageSize(100);
        com.volcengine.vpc.model.DescribeVpcsResponse describeVpcsResponse =
                volcEngineCloudHttpConfig.getVolcEngineVpcApi(cloudAccountEntity.getId(), commonRequest.getRegion_id()).describeVpcs(describeVpcsRequest);
        List<VpcForDescribeVpcsOutput> vpcForDescribeVpcsOutputs = describeVpcsResponse.getVpcs().stream()
                .filter(vpcForDescribeVpcsOutput -> "Available".equals(vpcForDescribeVpcsOutput.getStatus())).collect(Collectors.toList());
        List<CloudVPCEntity> vpcList = new ArrayList<>();
        for (VpcForDescribeVpcsOutput vpc : vpcForDescribeVpcsOutputs) {
            CloudVPCEntity cloudVPCEntity = new CloudVPCEntity();
            cloudVPCEntity.setVpc_id(vpc.getVpcId());
            cloudVPCEntity.setVpc_name(vpc.getVpcName() + "【" + vpc.getCidrBlock() + "】");
            vpcList.add(cloudVPCEntity);
        }
        filterVpcList(commonRequest, commonListResponse, vpcList);
    }

    private void getAliCloudVPCList(CommonRequest commonRequest, CommonListResponse commonListResponse, CloudAccountEntity cloudAccountEntity) throws Exception {
        int pageNumber = 1;
        int pageSize = 50;
        int totalCount;
        List<CloudVPCEntity> vpcList = new ArrayList<>();
        do {
            com.aliyun.vpc20160428.models.DescribeVpcsRequest describeVpcsRequest = new com.aliyun.vpc20160428.models.DescribeVpcsRequest();
            describeVpcsRequest.setPageNumber(pageNumber);
            describeVpcsRequest.setPageSize(pageSize);
            com.aliyun.vpc20160428.models.DescribeVpcsResponse describeVpcsResponse =
                    aliCloudHttpConfig.getVPCClient(cloudAccountEntity.getId(), commonRequest.getRegion_id()).describeVpcs(describeVpcsRequest);
            totalCount = describeVpcsResponse.body.totalCount;
            for (DescribeVpcsResponseBody.DescribeVpcsResponseBodyVpcsVpc vpc : describeVpcsResponse.body.getVpcs().vpc) {
                CloudVPCEntity cloudVPCEntity = new CloudVPCEntity();
                cloudVPCEntity.setVpc_id(vpc.getVpcId());
                cloudVPCEntity.setVpc_name(vpc.getVpcName() + "【" + vpc.getCidrBlock() + "】");
                vpcList.add(cloudVPCEntity);
            }
            pageNumber++;
        } while ((pageNumber - 1) * pageSize < totalCount);

        filterVpcList(commonRequest, commonListResponse, vpcList);
    }

    private void getTencentCloudVPCList(CommonRequest commonRequest, CommonListResponse commonListResponse,
            CloudAccountEntity cloudAccountEntity) throws TencentCloudSDKException {
        com.tencentcloudapi.vpc.v20170312.VpcClient vpcClient = tencentCloudHttpConfig
                .getTencentVpcClient(cloudAccountEntity.getId(), commonRequest.getRegion_id());
        DescribeVpcsResponse describeVpcsResponse = vpcClient.DescribeVpcs(
                new DescribeVpcsRequest());
        List<CloudVPCEntity> vpcList = new ArrayList<>();
        for (com.tencentcloudapi.vpc.v20170312.models.Vpc vpc : describeVpcsResponse.getVpcSet()) {
            CloudVPCEntity cloudVPCEntity = new CloudVPCEntity();
            cloudVPCEntity.setVpc_id(vpc.getVpcId());
            cloudVPCEntity.setVpc_name(vpc.getVpcName() + "【" + vpc.getCidrBlock() + "】");
            vpcList.add(cloudVPCEntity);
        }
        filterVpcList(commonRequest, commonListResponse, vpcList);
    }

    private void getHuaweiCloudVPCList(CommonRequest commonRequest, CommonListResponse commonListResponse,
            CloudAccountEntity cloudAccountEntity) {
        List<CloudVPCEntity> vpcList = new ArrayList<>();
        VpcClient vpcClient = huaweiCloudHttpConfig.getHuaweiVpcClient(
                cloudAccountEntity.getId(),
                commonRequest.getRegion_id());
        ListVpcsResponse listVpcsResponse = vpcClient.listVpcs(new ListVpcsRequest());
        for (Vpc vpc : listVpcsResponse.getVpcs()) {
            CloudVPCEntity cloudVPCEntity = new CloudVPCEntity();
            cloudVPCEntity.setVpc_id(vpc.getId());
            cloudVPCEntity.setVpc_name(vpc.getName() + "【" + vpc.getCidr() + "】");
            vpcList.add(cloudVPCEntity);
        }
        filterVpcList(commonRequest, commonListResponse, vpcList);
    }

    private void filterVpcList(CommonRequest commonRequest, CommonListResponse commonListResponse,
            List<CloudVPCEntity> vpcList) {
        if (StringUtils.isNotEmpty(commonRequest.getVpc_name())) {
            vpcList = vpcList.stream().filter(vpc -> vpc.getVpc_name()
                    .toLowerCase(Locale.ROOT).contains(commonRequest.getVpc_name().toLowerCase(
                            Locale.ROOT))).collect(Collectors.toList());
        }
        commonListResponse.setTotal(vpcList.size());
        commonListResponse.setList(CommonUtil.getPage(vpcList, commonRequest.getPageNo(),
                commonRequest.getPageSize()));
    }

    private static String extractSubnetPrefixes(String cidrBlock) {
        String[] parts = cidrBlock.split("\\.");
        return parts[0] + "." + parts[1] + ".";
    }

}
