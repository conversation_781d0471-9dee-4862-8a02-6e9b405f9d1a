package com.tt.cloud.thread;

import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.dcdb.v20180411.DcdbClient;
import com.tencentcloudapi.dcdb.v20180411.models.DescribeProjectsRequest;
import com.tencentcloudapi.dcdb.v20180411.models.DescribeProjectsResponse;
import com.tencentcloudapi.dcdb.v20180411.models.Project;
import com.tencentcloudapi.region.v20220627.models.DescribeRegionsRequest;
import com.tencentcloudapi.region.v20220627.models.DescribeRegionsResponse;
import com.tencentcloudapi.region.v20220627.models.RegionInfo;
import com.tencentcloudapi.tke.v20180525.models.Cluster;
import com.tencentcloudapi.tke.v20180525.models.DescribeClustersRequest;
import com.tencentcloudapi.tke.v20180525.models.DescribeClustersResponse;
import com.tencentcloudapi.vpc.v20170312.models.DescribeSubnetsRequest;
import com.tencentcloudapi.vpc.v20170312.models.DescribeSubnetsResponse;
import com.tencentcloudapi.vpc.v20170312.models.DescribeVpcsRequest;
import com.tencentcloudapi.vpc.v20170312.models.DescribeVpcsResponse;
import com.tencentcloudapi.vpc.v20170312.models.Filter;
import com.tencentcloudapi.vpc.v20170312.models.Subnet;
import com.tencentcloudapi.vpc.v20170312.models.Vpc;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.v2.LBItem;
import com.tt.cloud.bean.v2.NetTypeEnum;
import com.tt.cloud.bean.v2.ResourceTypeEnum;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/2/28 11:19
 */
@Slf4j
public class TencentLBCreateNoticeThread implements Runnable {
    private final LBItem lbItem;
    private final CloudAccountEntity cloudAccountEntity;
    private final FlyBookService flyBookService;
    private final ApplicationContext applicationContext;
    private final TencentCloudHttpConfig tencentCloudHttpConfig;

    public TencentLBCreateNoticeThread(LBItem lbItem, CloudAccountEntity cloudAccountEntity,
            FlyBookService flyBookService, ApplicationContext applicationContext,
            TencentCloudHttpConfig tencentCloudHttpConfig) {
        this.lbItem = lbItem;
        this.cloudAccountEntity = cloudAccountEntity;
        this.flyBookService = flyBookService;
        this.applicationContext = applicationContext;
        this.tencentCloudHttpConfig = tencentCloudHttpConfig;
    }

    @Override
    public void run() {
        try {
            String content = Utils.readTxtFile("/fly_book_template/create_tencent_cloud_lb.txt");
            Map<String, Object> messageParam = new HashMap<>();
            fillTitle(messageParam);
            messageParam.put("cloud", cloudAccountEntity.getDomainId() + "/" + cloudAccountEntity.getName());
            fillRegion(messageParam);
            fillProject(messageParam);
            messageParam.put("lb_name", lbItem.getLb_name());
            if (ResourceTypeEnum.HOST_LB.getResourceType().equals(lbItem.getResource_type())) {
                messageParam.put("type", "主机LB");
                messageParam.put("cluster_istio", StringUtils.EMPTY);
            } else {
                messageParam.put("type", "容器LB");
                fillCluster(messageParam);
            }
            dealLBType(messageParam);
            fill_VPC_Subnet(messageParam);
            messageParam.put("lb_id", StringUtils.isNotEmpty(lbItem.getLb_id()) ? lbItem.getLb_id() : "-");
            messageParam.put("createTime", new SimpleDateFormat(Constants.DATA_FORMAT).format(new Date()));
            messageParam.put("username", lbItem.getUsername());
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(lbItem.getEmail(), content);
        } catch (Exception e) {
            log.error("发送腾讯云LB创建成功消息异常", e);
        }
    }

    private void fillTitle(Map<String, Object> messageParam) {
        if (Constants.SUCCESS.equals(lbItem.getResultCode())) {
            messageParam.put("color", "green");
            messageParam.put("result", "成功");
            messageParam.put("error", StringUtils.EMPTY);
        } else {
            messageParam.put("color", "red");
            messageParam.put("result", "失败");
            String error = ",\n"
                    + "{\n"
                    + "\"tag\": \"div\",\n"
                    + "\"fields\": [\n"
                    + "{\n"
                    + "\"is_short\": true,\n"
                    + "\"text\": {\n"
                    + "\"tag\": \"lark_md\",\n"
                    + "\"content\": \"**\uD83D\uDDF3错误信息：**\\n" + lbItem.getResultMsg() + "\"\n"
                    + "}\n"
                    + "}\n"
                    + "]\n"
                    + "}";
            messageParam.put("error", error);
        }
    }

    private void fillCluster(Map<String, Object> messageParam) throws TencentCloudSDKException {
        messageParam.put("cluster_istio", "{\n"
                + "      \"tag\": \"div\",\n"
                + "      \"fields\": [\n"
                + "        {\n"
                + "          \"is_short\": true,\n"
                + "          \"text\": {\n"
                + "            \"tag\": \"lark_md\",\n"
                + "            \"content\": \"**\uD83D\uDDF3集群名称：**\\n" + getCluster_name() + "\"\n"
                + "          }\n"
                + "        },\n"
                + "        {\n"
                + "          \"is_short\": true,\n"
                + "          \"text\": {\n"
                + "            \"tag\": \"lark_md\",\n"
                + "            \"content\": \"**\uD83D\uDCDD是否istio网关：**\\n" + ("1".equals(lbItem.getIstio_net()) ? "是": "否") + "\"\n"
                + "          }\n"
                + "        }\n"
                + "      ]\n"
                + "    },");
    }

    private String getCluster_name() throws TencentCloudSDKException {
        String cluster_name = StringUtils.EMPTY;
        DescribeClustersResponse describeClustersResponse =
                tencentCloudHttpConfig.getTencentTkeClient(
                                cloudAccountEntity.getId(), lbItem.getRegion_id())
                        .DescribeClusters(new DescribeClustersRequest());

        Optional<Cluster> clusterOptional = Arrays.stream(describeClustersResponse.getClusters())
                .filter(cluster -> cluster.getClusterId().equals(lbItem.getCluster_id())).findFirst();
        if (clusterOptional.isPresent()) {
            cluster_name = clusterOptional.get().getClusterName();
        }
        return cluster_name;
    }

    private void fill_VPC_Subnet(Map<String, Object> messageParam) throws TencentCloudSDKException {
        com.tencentcloudapi.vpc.v20170312.VpcClient vpcClient = tencentCloudHttpConfig
                .getTencentVpcClient(cloudAccountEntity.getId(), lbItem.getRegion_id());
        DescribeVpcsResponse describeVpcsResponse = vpcClient.DescribeVpcs(
                new DescribeVpcsRequest());
        Optional<Vpc> vpcOptional = Arrays.stream(describeVpcsResponse.getVpcSet()).filter(vpc -> vpc.getVpcId().equals(lbItem.getVpc_id())).findFirst();
        vpcOptional.ifPresent(
                vpc -> messageParam.put("vpc", vpc.getVpcName() + "【" + vpc.getCidrBlock() + "】"));
        DescribeSubnetsRequest describeSubnetsRequest = new DescribeSubnetsRequest();
        Filter filter = new Filter();
        filter.setName("vpc-id");
        filter.setValues(new String[]{lbItem.getVpc_id()});
        describeSubnetsRequest.setFilters(new Filter[]{filter});
        DescribeSubnetsResponse describeSubnetsResponse = vpcClient.DescribeSubnets(
                describeSubnetsRequest);
        Optional<Subnet> subnetOptional = Arrays.stream(describeSubnetsResponse.getSubnetSet())
                .filter(subnet -> subnet.getSubnetId().equals(lbItem.getSubnet_id())).findFirst();
        subnetOptional.ifPresent(subnet -> messageParam.put("subnet",
                subnet.getSubnetName() + "【" + subnet.getCidrBlock() + "】"));
    }

    private void fillProject(Map<String, Object> messageParam) throws TencentCloudSDKException {
        DcdbClient dcdbClient = tencentCloudHttpConfig
                .getTencentDcdbClient(cloudAccountEntity.getId(), lbItem.getRegion_id());
        DescribeProjectsResponse describeProjectsResponse = dcdbClient.DescribeProjects(
                new DescribeProjectsRequest());
        Optional<Project> projectOptional = Arrays.stream(describeProjectsResponse.getProjects())
                .filter(project -> project.getProjectId().toString().equals(lbItem.getProject_id())).findFirst();
        projectOptional.ifPresent(
                epDetail -> messageParam.put("project", epDetail.getName()));
    }

    private void fillRegion(Map<String, Object> messageParam) throws TencentCloudSDKException {
        DescribeRegionsRequest describeRegionsRequest = new DescribeRegionsRequest();
        describeRegionsRequest.setProduct("clb");
        DescribeRegionsResponse describeRegionsResponse =
                tencentCloudHttpConfig.getTencentRegionClient(cloudAccountEntity.getId())
                        .DescribeRegions(describeRegionsRequest);
        Optional<RegionInfo> regionOption = Arrays.stream(describeRegionsResponse.getRegionSet())
                .filter(region -> region.getRegion().equals(lbItem.getRegion_id())).findFirst();
        regionOption.ifPresent(region -> messageParam.put("region", region.getRegionName()));
    }

    private void dealLBType(Map<String, Object> messageParam) {
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            String zone_band_width = "{\n"
                    + "      \"tag\": \"div\",\n"
                    + "      \"fields\": [\n"
                    + "        {\n"
                    + "          \"is_short\": true,\n"
                    + "          \"text\": {\n"
                    + "            \"tag\": \"lark_md\",\n"
                    + "            \"content\": \"**\uD83D\uDDF3可用区：**\\n " + ("可用区" + lbItem.getZone_id().split(lbItem.getRegion_id() + "-")[1]) +"\"\n"
                    + "          }\n"
                    + "        },\n"
                    + "        {\n"
                    + "          \"is_short\": true,\n"
                    + "          \"text\": {\n"
                    + "            \"tag\": \"lark_md\",\n"
                    + "            \"content\": \"**\uD83D\uDCDD网络带宽：**\\n" + lbItem.getBandwidth_size() + "\"\n"
                    + "          }\n"
                    + "        }\n"
                    + "      ]\n"
                    + "    },";
            messageParam.put("zone_band_width", zone_band_width);
            messageParam.put("net_type", "公网");
        } else {
            messageParam.put("zone_band_width", StringUtils.EMPTY);
            messageParam.put("net_type", "内网");
        }
    }

}
