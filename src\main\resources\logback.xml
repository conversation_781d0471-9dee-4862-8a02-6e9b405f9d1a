<?xml version="1.0" encoding="UTF-8" ?>

<configuration>

  <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
    <layout class="ch.qos.logback.classic.PatternLayout">
      <pattern>[%date{yyyy-MM-dd HH:mm:ss:SSS}] [%contextName] [%thread] %-5level [%logger{36}] - %msg%n</pattern>
    </layout>
  </appender>

  <logger name="org.mybatis" level="info" additivity="true"/>
  <logger name="org.springframework" level="info" additivity="true"/>
  <logger name="org.spring" level="info" additivity="true"/>
  <logger name="org.apache" level="info" additivity="true"/>
  <logger name="com.alibaba" level="info" additivity="true"/>
  <logger name="com.xxl" level="info" additivity="true"/>

  <root level="info">
    <appender-ref ref="consoleLog"/>
  </root>

</configuration>
