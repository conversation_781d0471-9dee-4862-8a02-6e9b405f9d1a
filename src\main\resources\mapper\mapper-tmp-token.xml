<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--上面2行的是约束依赖，固定照抄就好-->
<!--下面的才是要自己编写的地方-->
<!--写mapper的配置文件第一步就是要写<mapper></mapper>标签-->
<!--<mapper></mapper>标签里包含着各个CURD操作的SQL语句-->
<mapper namespace="com.tt.cloud.dao.TmpTokenDao">

  <update id="removeTmpTokenById">
    update tt_cloud_tmp_token set status = 0 where id = #{id}
  </update>

  <select id="queryTmpTokenByToken" resultType="com.tt.cloud.bean.TmpToken">
    select id, token, userid, createtime, status from tt_cloud_tmp_token where token = #{token} and status = 1
  </select>

</mapper>
