package com.tt.cloud.controller;

import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.GroupEntity;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.handler.ali.AliCloudUserGroupHandler;
import com.tt.cloud.handler.aws.AwsCloudUserGroupHandler;
import com.tt.cloud.handler.huawei.HuaweiCloudUserGroupHandler;
import com.tt.cloud.handler.tencent.TencentCloudUserGroupHandler;
import com.tt.cloud.operatelog.OperateLog;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公有云用户组管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 11:04
 */
@Slf4j
@RestController
@RequestMapping("/rest/v1")
public class CloudUserGroupManageController {
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private HuaweiCloudUserGroupHandler huaweiCloudUserGroupHandler;
    @Resource
    private TencentCloudUserGroupHandler tencentCloudUserGroupHandler;
    @Resource
    private AliCloudUserGroupHandler aliCloudUserGroupHandler;
    @Resource
    private AwsCloudUserGroupHandler awsCloudUserGroupHandler;

    /**
     * 查询用户组列表
     *
     * @param groupEntity groupEntity
     * @return Object
     */
    @GetMapping("/group/list")
    public Object queryGroupList(GroupEntity groupEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(groupEntity.getAccountId());
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                return huaweiCloudUserGroupHandler.keystoneListGroups(groupEntity);
            case Constants.TENCENT_CLOUD:
                return tencentCloudUserGroupHandler.queryListGroups(groupEntity);
            case Constants.ALI_CLOUD:
                return aliCloudUserGroupHandler.queryGroupList(groupEntity);
            case Constants.AWS_CLOUD:
                return awsCloudUserGroupHandler.queryGroupList(groupEntity);
        }
        return Optional.empty();
    }

    /**
     * 创建用户组
     *
     * @param groupEntity groupEntity
     * @return Object
     */
    @PostMapping("/group")
    @OperateLog(operateDesc = "用户组", businessName = "创建用户组")
    public Object createGroup(@RequestBody GroupEntity groupEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(groupEntity.getAccountId());
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                return huaweiCloudUserGroupHandler.keystoneCreateGroup(groupEntity);
            case Constants.TENCENT_CLOUD:
                return tencentCloudUserGroupHandler.createGroup(groupEntity);
            case Constants.ALI_CLOUD:
                return aliCloudUserGroupHandler.createGroup(groupEntity);
            case Constants.AWS_CLOUD:
                return awsCloudUserGroupHandler.createGroup(groupEntity);
        }
        return Optional.empty();
    }

    /**
     * 删除用户组
     *
     * @param groupEntity groupEntity
     * @return Object
     */
    @DeleteMapping("/group")
    @OperateLog(operateDesc = "用户组", businessName = "删除用户组")
    public Object deleteGroup(GroupEntity groupEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(groupEntity.getAccountId());
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                return huaweiCloudUserGroupHandler.keystoneDeleteGroup(groupEntity);
            case Constants.TENCENT_CLOUD:
                return tencentCloudUserGroupHandler.deleteGroup(groupEntity);
            case Constants.ALI_CLOUD:
                return aliCloudUserGroupHandler.deleteGroup(groupEntity);
            case Constants.AWS_CLOUD:
                return awsCloudUserGroupHandler.deleteGroup(groupEntity);
        }
        return Optional.empty();
    }

    /**
     * 更细用户组
     *
     * @param groupEntity groupEntity
     * @return Object
     */
    @PutMapping("/group")
    @OperateLog(operateDesc = "用户组", businessName = "修改用户组")
    public Object updateGroup(@RequestBody GroupEntity groupEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(groupEntity.getAccountId());
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                return huaweiCloudUserGroupHandler.keystoneUpdateGroup(groupEntity);
            case Constants.TENCENT_CLOUD:
                return tencentCloudUserGroupHandler.updateGroup(groupEntity);
            case Constants.ALI_CLOUD:
                return aliCloudUserGroupHandler.updateGroup(groupEntity);
            case Constants.AWS_CLOUD:
                return awsCloudUserGroupHandler.updateGroup(groupEntity);
        }
        return Optional.empty();
    }

    /**
     * 查询用户组详情
     *
     * @param groupEntity groupEntity
     * @return Object
     */
    @GetMapping("/group")
    public Object queryGroupInfo(GroupEntity groupEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(groupEntity.getAccountId());
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                return huaweiCloudUserGroupHandler.keystoneShowGroup(groupEntity);
            case Constants.TENCENT_CLOUD:
                return tencentCloudUserGroupHandler.getGroupInfo(groupEntity);
            case Constants.ALI_CLOUD:
                return aliCloudUserGroupHandler.queryGroupInfo(groupEntity);
            case Constants.AWS_CLOUD:
                return awsCloudUserGroupHandler.queryGroupInfo(groupEntity);
        }
        return Optional.empty();
    }

    /**
     * 添加用户到用户组
     *
     * @param groupEntity groupEntity
     * @return Object
     */
    @PostMapping("/group/userToGroup")
    @OperateLog(operateDesc = "用户组", businessName = "添加用户到用户组")
    public Object addUserToGroup(@RequestBody GroupEntity groupEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(groupEntity.getAccountId());
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                return huaweiCloudUserGroupHandler.keystoneAddUserToGroup(groupEntity);
            case Constants.TENCENT_CLOUD:
                return tencentCloudUserGroupHandler.addUserToGroup(groupEntity);
            case Constants.ALI_CLOUD:
                return aliCloudUserGroupHandler.addUserToGroup(groupEntity);
            case Constants.AWS_CLOUD:
                return awsCloudUserGroupHandler.addUserToGroup(groupEntity);
        }
        return Optional.empty();
    }

    /**
     * 从用户组中删除用户
     *
     * @param groupEntity groupEntity
     * @return Object
     */
    @DeleteMapping("/group/userFromGroup")
    @OperateLog(operateDesc = "用户组", businessName = "从用户组中删除用户")
    public Object removeUserFromGroup(GroupEntity groupEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(groupEntity.getAccountId());
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                return huaweiCloudUserGroupHandler.keystoneRemoveUserFromGroup(groupEntity);
            case Constants.TENCENT_CLOUD:
                return tencentCloudUserGroupHandler.removeUserFromGroup(groupEntity);
            case Constants.ALI_CLOUD:
                return aliCloudUserGroupHandler.removeUserFromGroup(groupEntity);
            case Constants.AWS_CLOUD:
                return awsCloudUserGroupHandler.removeUserFromGroup(groupEntity);
        }
        return Optional.empty();
    }

    /**
     * 查询用户组下的用户
     *
     * @param userEntity userEntity
     * @return Object
     */
    @GetMapping("/group/listUsersForGroup")
    public Object queryListUsersForGroup(UserEntity userEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                return huaweiCloudUserGroupHandler.keystoneListUsersForGroupByAdmin(userEntity);
            case Constants.TENCENT_CLOUD:
                return tencentCloudUserGroupHandler.queryListUsersForGroup(userEntity);
            case Constants.ALI_CLOUD:
                return aliCloudUserGroupHandler.queryListUsersForGroup(userEntity);
            case Constants.AWS_CLOUD:
                return awsCloudUserGroupHandler.queryListUsersForGroup(userEntity);
        }
        return Optional.empty();
    }

    /**
     * 查询用户关联的用户组
     *
     * @param groupEntity groupEntity
     * @return Object
     */
    @GetMapping("/group/listGroupsForUser")
    public Object queryListGroupsForUser(GroupEntity groupEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(groupEntity.getAccountId());
        switch (cloudAccount.getCloudType()) {
            case Constants.HUAWEI_CLOUD:
                return huaweiCloudUserGroupHandler.keystoneListGroupsForUser(groupEntity);
            case Constants.TENCENT_CLOUD:
                return tencentCloudUserGroupHandler.queryListGroupsForUser(groupEntity);
            case Constants.ALI_CLOUD:
                return aliCloudUserGroupHandler.queryListGroupsForUser(groupEntity);
            case Constants.AWS_CLOUD:
                return awsCloudUserGroupHandler.queryListGroupsForUser(groupEntity);
        }
        return Optional.empty();
    }

}
