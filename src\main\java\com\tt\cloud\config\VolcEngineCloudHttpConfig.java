package com.tt.cloud.config;

import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.CacheUtil;
import com.tt.cloud.util.EncryptUtils;
import com.volcengine.ApiClient;
import com.volcengine.alb.AlbApi;
import com.volcengine.clb.ClbApi;
import com.volcengine.ecs.EcsApi;
import com.volcengine.iam.IamApi;
import com.volcengine.iam20210801.Iam20210801Api;
import com.volcengine.sign.Credentials;
import com.volcengine.vke.VkeApi;
import com.volcengine.vpc.VpcApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 15:02
 */
@Slf4j
@Component
public class VolcEngineCloudHttpConfig {
    @Resource
    private CloudAccountDao cloudAccountDao;


    public Iam20210801Api getVolcEngineIam20210801Api(String accountId, String region) {
        String cacheKey = "Volc_Engine_Iam20210801Api_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (Iam20210801Api) object;
        }
        if (StringUtils.isEmpty(region)) {
            region = "cn-beijing";
        }
        ApiClient apiClient = getApiClient(accountId, region);
        Iam20210801Api api = new Iam20210801Api(apiClient);
        CacheUtil.put(cacheKey, api, 24 * 60 * 60);
        return api;
    }

    public VpcApi getVolcEngineVpcApi(String accountId, String region) {
        String cacheKey = "Volc_Engine_VpcApi_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (VpcApi) object;
        }
        ApiClient apiClient = getApiClient(accountId, region);
        VpcApi api = new VpcApi(apiClient);
        CacheUtil.put(cacheKey, api, 24 * 60 * 60);
        return api;
    }

    public VkeApi getVolcEngineVkeApi(String accountId, String region) {
        String cacheKey = "Volc_Engine_VkeApi_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (VkeApi) object;
        }
        ApiClient apiClient = getApiClient(accountId, region);
        VkeApi api = new VkeApi(apiClient);
        CacheUtil.put(cacheKey, api, 24 * 60 * 60);
        return api;
    }

    public ClbApi getVolcEngineClbApi(String accountId, String region) {
        String cacheKey = "Volc_Engine_ClbApi_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (ClbApi) object;
        }
        ApiClient apiClient = getApiClient(accountId, region);
        ClbApi api = new ClbApi(apiClient);
        CacheUtil.put(cacheKey, api, 24 * 60 * 60);
        return api;
    }

    public AlbApi getVolcEngineAlbApi(String accountId, String region) {
        String cacheKey = "Volc_Engine_AlbApi_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (AlbApi) object;
        }
        ApiClient apiClient = getApiClient(accountId, region);
        AlbApi api = new AlbApi(apiClient);
        CacheUtil.put(cacheKey, api, 24 * 60 * 60);
        return api;
    }


    /**
     * 创建 Volc_Engine_IamClient
     *
     * @param accountId accountId
     * @return AwsIamClient
     */
    public IamApi getVolcEngineIamClient(String accountId) {
        String cacheKey = "Volc_Engine_IamClient_" + accountId;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (IamApi) object;
        }
        CloudAccountEntity cloudAccountEntity
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        ApiClient apiClient = new ApiClient()
                .setCredentials(Credentials.getCredentials(cloudAccountEntity.getSecretId(),
                        EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccountEntity.getSecretKey())))
                .setRegion("cn-beijing");

        IamApi api = new IamApi(apiClient);

        CacheUtil.put(cacheKey, api, 24 * 60 * 60);
        return api;
    }

    public EcsApi getVolcEngineEcsApi(String accountId, String region) {
        String cacheKey = "Volc_Engine_EcsApi_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (EcsApi) object;
        }
        if (StringUtils.isEmpty(region)) {
            region = "cn-beijing";
        }
        ApiClient apiClient = getApiClient(accountId, region);
        EcsApi api = new EcsApi(apiClient);
        CacheUtil.put(cacheKey, api, 24 * 60 * 60);
        return api;
    }


    private ApiClient getApiClient(String accountId, String region) {
        CloudAccountEntity cloudAccountEntity
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        return new ApiClient()
                .setCredentials(Credentials.getCredentials(cloudAccountEntity.getSecretId(),
                        EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccountEntity.getSecretKey())))
                .setRegion(region);
    }


}
