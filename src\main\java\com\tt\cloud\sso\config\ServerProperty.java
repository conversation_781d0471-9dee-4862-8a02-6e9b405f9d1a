package com.tt.cloud.sso.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class ServerProperty {
    private static final Properties properties;

    static {
        properties = new Properties();
        InputStream in = ServerProperty.class.getClassLoader().getResourceAsStream("config/server.properties");
        try {
            properties.load(in);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static Properties getInstance() {
        return properties;
    }
}
