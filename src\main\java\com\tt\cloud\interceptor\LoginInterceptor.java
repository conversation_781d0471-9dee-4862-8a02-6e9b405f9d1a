package com.tt.cloud.interceptor;

import com.tt.cloud.bean.TmpToken;
import com.tt.cloud.bean.UserInfo;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.service.UserServiceImpl;
import com.tt.cloud.util.CommonUtil;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 登录及权限验证
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2021/9/6 17:00
 */
@Slf4j
@Component
public class LoginInterceptor implements HandlerInterceptor {
  private String logOutUrl;
  private String clientSecret;
  private UserServiceImpl userService;

  public LoginInterceptor() {}

  public LoginInterceptor(
          String logOutUrl,
          String clientSecret,
          UserServiceImpl userService) {
    this.logOutUrl = logOutUrl;
    this.userService = userService;
    this.clientSecret = clientSecret;
  }

  @Override
  public boolean preHandle(
          HttpServletRequest request, HttpServletResponse response, Object handler) {
    if (filterResource(request.getRequestURI())) {
      return true;
    }
    log.info("当前URL为={}", request.getRequestURI());
    String authorization = CommonUtil.getCookieValue(request, "Authorization");
    // String authorization = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ3YW5naGVuZyIsImlzcyI6ImFkbWluIiwiZXhwIjoxOTY3NTA5NjUyLCJ1c2VySWQiOiJ3YW5naGVuZyJ9.qptuMbHzAuOl45IHi0jRWELCLQui5FU4frho4tilCRk";
    if (StringUtils.isEmpty(authorization)) {
      authorization = request.getHeader("Authorization");
    }

    // String newToken = CommonUtil.createToken("InfoSecDept", 365*5*24*60*60*1000L, clientSecret);
    // System.out.println( newToken);

    if (StringUtils.isEmpty(authorization)) {
      authorization = request.getParameter("token");
      if (StringUtils.isEmpty(authorization)) {
        log.error("获取token为空，跳转登录页面进行登录");
        setErrorResponse(response);
        return false;
      }
      TmpToken tmpToken = userService.queryTmpTokenByToken(authorization);
      if (null == tmpToken) {
        log.error("获取token为空，跳转登录页面进行登录");
        setErrorResponse(response);
        return false;
      }
      authorization = CommonUtil.parseTmpToken(tmpToken.getToken(), clientSecret);
      if (StringUtils.isEmpty(authorization)) {
        log.error("获取token为空，跳转登录页面进行登录");
        setErrorResponse(response);
        return false;
      }
    }
    initUserContext(authorization);
    UserInfo userInfo = getUserEntity(response, authorization); // 查询用户信息
    if (null == userInfo) {
      log.error("获取用户信息为空，跳转登录页面进行登录");
      setErrorResponse(response);
      return false;
    } else {
      UserContext.setUserInfoThreadLocal(userInfo);
    }
    return true;
  }

  private void initUserContext(String authorization) {
    UserInfo userInfo = new UserInfo();
    userInfo.setAuthorization(authorization);
    UserContext.setUserInfoThreadLocal(userInfo);
  }

  @Override
  public void afterCompletion(
          HttpServletRequest request,
          HttpServletResponse response,
          Object handler,
          @Nullable Exception ex) {
    UserContext.clearUserInfoThreadLocal();
  }

  /**
   * 获取当前用户
   *
   * @param response response
   * @param authorization authorization
   * @return UserEntity
   */
  private UserInfo getUserEntity(HttpServletResponse response, String authorization) {
    UserInfo userInfo;
    try {
      userInfo = userService.queryUserInfo(CommonUtil.verifyToken(authorization, clientSecret));
      if (null != userInfo) {
        userInfo.setAuthorization(authorization);
      }
    } catch (Exception e) {
      log.error("获取用户信息异常", e);
      setErrorResponse(response);
      return null;
    }
    return userInfo;
  }

  /**
   * 设置异常信息
   *
   * @param response response
   */
  private void setErrorResponse(HttpServletResponse response) {
    response.setStatus(401);
    response.setHeader("logOutUrl", logOutUrl);
    CommonUtil.addCookie(response, "Authorization", StringUtils.EMPTY);
  }

  /**
   * 放行的URL
   *
   * @param requestURI requestURI
   * @return boolean
   */
  private boolean filterResource(String requestURI) {
    return requestURI.contains("/tt-cloud-auth-platform-web/error")
            || requestURI.contains("/api/registry")
            || requestURI.contains("/api/callback");
  }
}
