<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--上面2行的是约束依赖，固定照抄就好-->
<!--下面的才是要自己编写的地方-->
<!--写mapper的配置文件第一步就是要写<mapper></mapper>标签-->
<!--<mapper></mapper>标签里包含着各个CURD操作的SQL语句-->
<mapper namespace="com.tt.cloud.dao.DomainDao">

  <insert id="insertRecord">
    insert into tt_cloud_dns_record(recordid, dnsid, recordname, remark, lineid, ttl, type, value,
                                    createtime, updatetime, status, mx, weight)
    values (#{recordId}, #{dnsId}, #{recordName}, #{remark}, #{lineId}, #{ttl}, #{type}, #{value},
            #{createTime}, #{updateTime}, #{status}, #{mx}, #{weight})
  </insert>

  <update id="updateRecord">
    update tt_cloud_dns_record
    set recordname = #{recordName},
        lineid     = #{lineId},
        ttl        = #{ttl},
        mx         = #{mx},
        weight     = #{weight},
        type       = #{type},
        value      = #{value},
        updatetime = #{updateTime},
        status     = #{status}
    where recordid = #{recordId}
  </update>

  <update id="updateRecordStatus">
     update tt_cloud_dns_record set status = #{status} where recordId = #{recordId}
  </update>

  <select id="queryDomainList" resultType="com.tt.cloud.bean.DomainEntity">
    select dnsid   domainid,
           dnsname domainname,
           accountid,
           gradetitle,
           grade,
           recordcount,
           remark,
           createtime,
           updateTime,
           status
    from tt_cloud_dns
    where status = 'ENABLE'
  </select>

  <select id="queryDomainInfoById" resultType="com.tt.cloud.bean.DomainEntity">
    select dnsid   domainid,
           dnsname domainname,
           accountid,
           gradetitle,
           grade,
           recordcount,
           remark,
           createtime,
           updateTime,
           status
    from tt_cloud_dns
    where status = 'ENABLE'
      and dnsid = #{domain_id}
  </select>

  <select id="queryRecordInfo" resultType="com.tt.cloud.bean.DomainRecordEntity">
     select lbid, recordid, dnsid, recordname, lbname, lineid, type, ttl, mx,
            weight, value, lbtype, createtime, updatetime, remark, status
     from tt_cloud_dns_record where recordid = #{recordId}
  </select>

</mapper>
