package com.tt.cloud.notice;

import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserReq;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserReqBody;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserResp;
import com.lark.oapi.service.contact.v3.model.UserContactInfo;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import java.util.UUID;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/25 15:23
 */
@Slf4j
@Component
public class FlyBookService {
    @Resource
    private Client flyBookClient;

    public void sendMessage(String email, String content) {
        try {
            if (StringUtils.isNotEmpty(email)) {
                BatchGetIdUserReq userReq = BatchGetIdUserReq.newBuilder()
                        .userIdType("user_id")
                        .batchGetIdUserReqBody(BatchGetIdUserReqBody.newBuilder()
                                .emails(new String[]{email}).build()).build();
                BatchGetIdUserResp resp = flyBookClient.contact().user()
                        .batchGetId(userReq, RequestOptions.newBuilder().build());
                if (null != resp.getData().getUserList()
                        && resp.getData().getUserList().length > 0) {
                    UserContactInfo userContactInfo = resp.getData().getUserList()[0];
                    if (StringUtils.isNotEmpty(userContactInfo.getUserId())) {
                        CreateMessageReq createMessageReq = CreateMessageReq.newBuilder()
                                .receiveIdType("user_id")
                                .createMessageReqBody(CreateMessageReqBody.newBuilder()
                                        .receiveId(userContactInfo.getUserId())
                                        .msgType("interactive")
                                        .content(content)
                                        .uuid(UUID.randomUUID().toString())
                                        .build())
                                .build();
                        CreateMessageResp createMessageResp = flyBookClient.im().message().create(createMessageReq, RequestOptions.newBuilder().build());
                        log.info("消息发送结果:{}-{}-{}", createMessageResp.getMsg(), createMessageResp.getCode(), createMessageResp.getRequestId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("发送飞书消息失败", e);
        }
    }

}
