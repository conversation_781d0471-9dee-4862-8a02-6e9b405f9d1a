package com.tt.cloud.dao;

import com.tt.cloud.bean.DomainEntity;
import com.tt.cloud.bean.DomainRecordEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/1/30 16:11
 */
public interface DomainDao {

    List<DomainEntity> queryDomainList();

    DomainEntity queryDomainInfoById(@Param("domain_id") String domain_id);

    void insertRecord(DomainRecordEntity dnsRecord);

    DomainRecordEntity queryRecordInfo(@Param("recordId") String recordId);

    void updateRecord(DomainRecordEntity recordEntity);

    void updateRecordStatus(DomainRecordEntity recordEntity);
}
