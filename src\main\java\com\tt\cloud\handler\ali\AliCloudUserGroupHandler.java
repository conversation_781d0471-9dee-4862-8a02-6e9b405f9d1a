package com.tt.cloud.handler.ali;

import com.aliyun.ram20150501.models.AddUserToGroupRequest;
import com.aliyun.ram20150501.models.CreateGroupRequest;
import com.aliyun.ram20150501.models.DeleteGroupRequest;
import com.aliyun.ram20150501.models.DetachPolicyFromGroupRequest;
import com.aliyun.ram20150501.models.GetGroupRequest;
import com.aliyun.ram20150501.models.GetGroupResponse;
import com.aliyun.ram20150501.models.ListGroupsForUserRequest;
import com.aliyun.ram20150501.models.ListGroupsForUserResponse;
import com.aliyun.ram20150501.models.ListGroupsForUserResponseBody.ListGroupsForUserResponseBodyGroupsGroup;
import com.aliyun.ram20150501.models.ListGroupsRequest;
import com.aliyun.ram20150501.models.ListGroupsResponse;
import com.aliyun.ram20150501.models.ListGroupsResponseBody.ListGroupsResponseBodyGroupsGroup;
import com.aliyun.ram20150501.models.ListPoliciesForGroupRequest;
import com.aliyun.ram20150501.models.ListPoliciesForGroupResponse;
import com.aliyun.ram20150501.models.ListPoliciesForGroupResponseBody.ListPoliciesForGroupResponseBodyPoliciesPolicy;
import com.aliyun.ram20150501.models.ListUsersForGroupRequest;
import com.aliyun.ram20150501.models.ListUsersForGroupResponse;
import com.aliyun.ram20150501.models.ListUsersForGroupResponseBody.ListUsersForGroupResponseBodyUsersUser;
import com.aliyun.ram20150501.models.RemoveUserFromGroupRequest;
import com.aliyun.ram20150501.models.UpdateGroupRequest;
import com.tt.cloud.bean.AliCloudResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.GroupEntity;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.UserGroupInfo;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 阿里云用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/19 9:55
 */
@Slf4j
@Component
public class AliCloudUserGroupHandler {

    @Resource
    private AliCloudHttpConfig aliCloudHttpConfig;
    @Resource
    private CloudAccountDao cloudAccountDao;

    /**
     * 查询用户组列表
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryGroupList(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("查询用户组列表");
            ListGroupsRequest listGroupsRequest = new ListGroupsRequest();
            listGroupsRequest.setMaxItems(1000);
            ListGroupsResponse listGroupsResponse = aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).listGroups(listGroupsRequest);
            List<GroupEntity> groupList = getGroupList(listGroupsResponse);
            if (StringUtils.isNotEmpty(groupEntity.getGroupName())) {
                groupList = groupList.stream().filter(item -> item.getGroupName().contains(groupEntity.getGroupName())).collect(
                        Collectors.toList());
            }
            aliCloudResponse.setGroupList(groupList);
        } catch (Exception e) {
            log.error("查询用户组列表异常", e);
            aliCloudResponse.getHeader().setErrorMsg("查询用户组列表异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 创建阿里云用户组
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse createGroup(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("创建阿里云用户组：{}", groupEntity);
            CreateGroupRequest createGroupRequest = new CreateGroupRequest();
            createGroupRequest.setGroupName(groupEntity.getGroupName());
            createGroupRequest.setComments(groupEntity.getMemo());
            aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).createGroup(createGroupRequest);
            log.info("创建阿里云用户组完毕");
        } catch (Exception e) {
            log.error("创建阿里云用户组异常", e);
            aliCloudResponse.getHeader().setErrorMsg("创建阿里云用户组异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 删除用户组
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse deleteGroup(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("先刪除用户组包含的用户");
            ListUsersForGroupRequest listUsersForGroupRequest = new ListUsersForGroupRequest();
            listUsersForGroupRequest.setGroupName(groupEntity.getGroupName());
            listUsersForGroupRequest.setMaxItems(1000);
            ListUsersForGroupResponse listUsersForGroupResponse =
                    aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).listUsersForGroup(listUsersForGroupRequest);
            for (ListUsersForGroupResponseBodyUsersUser listUsersForGroupResponseBodyUsersUser
                    : listUsersForGroupResponse.getBody().getUsers().getUser()) {
                RemoveUserFromGroupRequest removeUserFromGroupRequest = new RemoveUserFromGroupRequest();
                removeUserFromGroupRequest.setUserName(listUsersForGroupResponseBodyUsersUser.getUserName());
                removeUserFromGroupRequest.setGroupName(groupEntity.getGroupName());
                aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).removeUserFromGroup(removeUserFromGroupRequest);
            }
            log.info("删除用户组绑定的权限");
            ListPoliciesForGroupRequest listPoliciesForGroupRequest = new ListPoliciesForGroupRequest();
            listPoliciesForGroupRequest.setGroupName(groupEntity.getGroupName());
            ListPoliciesForGroupResponse listPoliciesForGroupResponse =
                    aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).listPoliciesForGroup(listPoliciesForGroupRequest);
            for (ListPoliciesForGroupResponseBodyPoliciesPolicy listPoliciesForGroupResponseBodyPoliciesPolicy
                    : listPoliciesForGroupResponse.getBody().policies.getPolicy()) {
                DetachPolicyFromGroupRequest detachPolicyFromGroupRequest = new DetachPolicyFromGroupRequest();
                detachPolicyFromGroupRequest.setPolicyName(listPoliciesForGroupResponseBodyPoliciesPolicy.policyName);
                detachPolicyFromGroupRequest.setPolicyType(listPoliciesForGroupResponseBodyPoliciesPolicy.policyType);
                detachPolicyFromGroupRequest.setGroupName(groupEntity.getGroupName());
                aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).detachPolicyFromGroup(detachPolicyFromGroupRequest);
            }
            log.info("删除阿里云用户组：{}", groupEntity);
            DeleteGroupRequest deleteGroupRequest = new DeleteGroupRequest();
            deleteGroupRequest.setGroupName(groupEntity.getGroupName());
            aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).deleteGroup(deleteGroupRequest);
            log.info("删除阿里云用户组完毕");
        } catch (Exception e) {
            log.error("删除阿里云用户组异常", e);
            aliCloudResponse.getHeader().setErrorMsg("删除阿里云用户组异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 更新用户组
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse updateGroup(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("更新阿里云用户组：{}", groupEntity);
            UpdateGroupRequest updateGroupRequest = new UpdateGroupRequest();
            updateGroupRequest.setGroupName(groupEntity.getGroupName());
            updateGroupRequest.setNewGroupName(groupEntity.getNewGroupName());
            updateGroupRequest.setNewComments(groupEntity.getMemo());
            aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).updateGroup(updateGroupRequest);
            log.info("更新阿里云用户组完毕");
        } catch (Exception e) {
            log.error("更新阿里云用户组异常", e);
            aliCloudResponse.getHeader().setErrorMsg("更新阿里云用户组异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 查詢用戶組詳情
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryGroupInfo(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("查询用户组详情:{}", groupEntity);
            GetGroupRequest getGroupRequest = new GetGroupRequest();
            getGroupRequest.setGroupName(groupEntity.getGroupName());
            GetGroupResponse getGroupResponse =
                    aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).getGroup(getGroupRequest);
            GroupEntity group = new GroupEntity();
            group.setGroupId(getGroupResponse.getBody().getGroup().getGroupId());
            group.setGroupName(getGroupResponse.getBody().getGroup().groupName);
            group.setMemo(getGroupResponse.getBody().getGroup().getComments());
            aliCloudResponse.setGroupEntity(group);
        } catch (Exception e) {
            log.error("查询用户组详情异常", e);
            aliCloudResponse.getHeader().setErrorMsg("查询用户组详情异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 用户加入用户组
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse addUserToGroup(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("阿里云用户加入用户组:{}", groupEntity);
            AddUserToGroupRequest addUserToGroupRequest = new AddUserToGroupRequest();
            if (null != groupEntity.getGroupNameList() && !groupEntity.getGroupNameList().isEmpty()) {
                for (String groupName : groupEntity.getGroupNameList()) {
                    addUserToGroupRequest.setGroupName(groupName);
                    addUserToGroupRequest.setUserName(groupEntity.getUserName());
                    aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                }
            }
            if (null != groupEntity.getGroupName()) {
                if (null != groupEntity.getUserNameList() && !groupEntity.getUserNameList().isEmpty()) {
                    for (String userName : groupEntity.getUserNameList()) {
                        addUserToGroupRequest.setGroupName(groupEntity.getGroupName());
                        addUserToGroupRequest.setUserName(userName);
                        aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                    }
                }
            }
        } catch (Exception e) {
            log.error("阿里云用户加入用户组异常", e);
            aliCloudResponse.getHeader().setErrorMsg("阿里云用户加入用户组异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 用户从用户组中移除
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse removeUserFromGroup(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("阿里云用户从用户组中移除:{}", groupEntity);
            RemoveUserFromGroupRequest request = new RemoveUserFromGroupRequest();
            if (null != groupEntity.getGroupNameList() && null != groupEntity.getUserNameList()) {
                for (String groupName : groupEntity.getGroupNameList()) {
                    for (String userName : groupEntity.getUserNameList()) {
                        request.setGroupName(groupName);
                        request.setUserName(userName);
                        aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).removeUserFromGroup(request);
                    }
                }
            }
        } catch (Exception e) {
            log.error("阿里云用户从用户组中移除异常", e);
            aliCloudResponse.getHeader().setErrorMsg("阿里云用户从用户组中移除异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 查詢用戶組包含的用戶列表
     *
     * @param userEntity userEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryListUsersForGroup(UserEntity userEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("查询阿里云包含用户列表：{}", userEntity.getGroupName());
            ListUsersForGroupRequest listUsersForGroupRequest = new ListUsersForGroupRequest();
            listUsersForGroupRequest.setGroupName(userEntity.getGroupName());
            listUsersForGroupRequest.setMaxItems(1000);
            ListUsersForGroupResponse listUsersForGroupResponse =
                    aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).listUsersForGroup(listUsersForGroupRequest);
            List<UserEntity> userList = new ArrayList<>();
            for (ListUsersForGroupResponseBodyUsersUser listUsersResponseBodyUsersUser : listUsersForGroupResponse.getBody()
                    .getUsers().getUser()) {
                UserEntity user = new UserEntity();
                user.setUserName(listUsersResponseBodyUsersUser.getUserName());
                user.setCreateTime(listUsersResponseBodyUsersUser.getJoinDate());
                userList.add(user);
            }
            aliCloudResponse.setUserList(userList);
        } catch (Exception e) {
            log.error("查询阿里云包含用户列表异常", e);
            aliCloudResponse.getHeader().setErrorMsg("查询阿里云包含用户列表异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 查詢用戶归属的用户组列表
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryListGroupsForUser(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("查询阿里云用户所属用户组列表:{}", groupEntity.getUserName());
            ListGroupsForUserRequest listGroupsForUserRequest = new ListGroupsForUserRequest();
            listGroupsForUserRequest.setUserName(groupEntity.getUserName());
            ListGroupsForUserResponse listGroupsForUserResponse =
                    aliCloudHttpConfig.getAliIamClient(groupEntity.getAccountId()).listGroupsForUser(listGroupsForUserRequest);
            List<GroupEntity> groupList = new ArrayList<>();
            for (ListGroupsForUserResponseBodyGroupsGroup listGroupsResponseBodyGroupsGroup : listGroupsForUserResponse.getBody()
                    .getGroups().getGroup()) {
                GroupEntity group = new GroupEntity();
                group.setGroupId(listGroupsResponseBodyGroupsGroup.groupId);
                group.setGroupName(listGroupsResponseBodyGroupsGroup.groupName);
                group.setCreateTime(listGroupsResponseBodyGroupsGroup.getJoinDate());
                group.setMemo(listGroupsResponseBodyGroupsGroup.getComments());
                groupList.add(group);
            }
            aliCloudResponse.setGroupList(groupList);
        } catch (Exception e) {
            log.error("查询阿里云用户所属用户组列表异常", e);
            aliCloudResponse.getHeader().setErrorMsg("查询阿里云用户所属用户组列表异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 查询用户组全量列表
     *
     * @return List
     */
    public List<UserGroupInfo> queryAllGroupList() throws Exception {
        List<UserGroupInfo> groupList = new ArrayList<>();
        List<ListGroupsResponseBodyGroupsGroup> groups = new ArrayList<>();
        List<CloudAccountEntity> accountList = cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
        for (CloudAccountEntity cloudAccount : accountList) {
            if (Constants.ALI_CLOUD.equals(cloudAccount.getCloudType())) {
                ListGroupsRequest listGroupsRequest = new ListGroupsRequest();
                listGroupsRequest.setMaxItems(1000);
                ListGroupsResponse listGroupsResponse = aliCloudHttpConfig.getAliIamClient(cloudAccount.getId()).listGroups(listGroupsRequest);
                if (!listGroupsResponse.getBody().getGroups().getGroup().isEmpty()) {
                    if (groups.isEmpty()) {
                        groups.addAll(listGroupsResponse.getBody().getGroups().getGroup());
                    } else {
                        groups = groups.stream().filter(item -> listGroupsResponse.getBody()
                                .getGroups().getGroup().stream().anyMatch(item1 -> item.getGroupName()
                                        .equals(item1.getGroupName()))).collect(Collectors.toList());
                    }
                }
            }
        }
        for (ListGroupsResponseBodyGroupsGroup listGroupsResponseBodyGroupsGroup : groups) {
            UserGroupInfo userGroupInfo = new UserGroupInfo();
            userGroupInfo.setGroupId(listGroupsResponseBodyGroupsGroup.getGroupName());
            userGroupInfo.setGroupName(listGroupsResponseBodyGroupsGroup.getGroupName());
            groupList.add(userGroupInfo);
        }
        return groupList;
    }

    private List<GroupEntity> getGroupList(ListGroupsResponse listGroupsResponse) {
        List<GroupEntity> groupList = new ArrayList<>();
        for (ListGroupsResponseBodyGroupsGroup listGroupsResponseBodyGroupsGroup : listGroupsResponse.getBody()
                .getGroups().getGroup()) {
            GroupEntity group = new GroupEntity();
            group.setGroupId(listGroupsResponseBodyGroupsGroup.groupId);
            group.setGroupName(listGroupsResponseBodyGroupsGroup.groupName);
            group.setCreateTime(listGroupsResponseBodyGroupsGroup.createDate);
            group.setMemo(listGroupsResponseBodyGroupsGroup.getComments());
            groupList.add(group);
        }
        return groupList;
    }
}
