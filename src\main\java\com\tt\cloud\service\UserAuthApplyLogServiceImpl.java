package com.tt.cloud.service;

import com.tt.cloud.bean.UserAuthApplyLog;
import com.tt.cloud.dao.UserAuthApplyDao;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/12 10:36
 */
@Slf4j
@Component
public class UserAuthApplyLogServiceImpl {
    @Resource
    private UserAuthApplyDao userAuthApplyDao;

    /**
     * 新增用户权限申请记录
     *
     * @param userAuthApplyLog userAuthApplyLog
     */
    public void addUserAuthApplyLog(UserAuthApplyLog userAuthApplyLog) {
        try {
            if (StringUtils.isEmpty(userAuthApplyLog.getResultMsg())) {
                userAuthApplyLog.setResultMsg("-");
            }
            userAuthApplyDao.insertUserAuthApplyLog(userAuthApplyLog);
        } catch (Exception e) {
            log.error("新增用户权限申请记录异常", e);
        }
    }

}
