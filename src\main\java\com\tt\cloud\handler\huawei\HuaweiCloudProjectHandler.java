package com.tt.cloud.handler.huawei;

import com.huaweicloud.sdk.iam.v3.model.KeystoneListProjectsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListProjectsResponse;
import com.tt.cloud.bean.HuaweiCloudResponse;

import com.tt.cloud.config.HuaweiCloudHttpConfig;

import com.tt.cloud.util.Utils;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/20 18:22
 */
@Slf4j
@Component
public class HuaweiCloudProjectHandler {

    @Resource
    private HuaweiCloudHttpConfig huaweiCloudHttpConfig;

    /**
     * 华为云查询项目列表
     *
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse queryProjectList(String accountId) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            log.info("华为云查询项目列表");
            KeystoneListProjectsResponse keystoneListProjectsResponse =
                    huaweiCloudHttpConfig.getHuaweiIamClient(accountId)
                            .keystoneListProjects(new KeystoneListProjectsRequest());
            log.info("华为云查询项目列表完毕：{}",  keystoneListProjectsResponse.getProjects().size());
            huaweiCloudResponse.setProjectResultList(keystoneListProjectsResponse
                    .getProjects().stream().filter(item-> !"MOS".equals(item.getName())).collect(
                    Collectors.toList()));
        } catch (Exception e) {
            log.error("华为云查询项目列表异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "华为云查询项目列表异常");
        }
        return huaweiCloudResponse;
    }

}
