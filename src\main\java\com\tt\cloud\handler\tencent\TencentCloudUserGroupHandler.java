package com.tt.cloud.handler.tencent;

import com.tencentcloudapi.cam.v20190116.models.AddUserToGroupRequest;
import com.tencentcloudapi.cam.v20190116.models.CreateGroupRequest;
import com.tencentcloudapi.cam.v20190116.models.DeleteGroupRequest;
import com.tencentcloudapi.cam.v20190116.models.GetGroupRequest;
import com.tencentcloudapi.cam.v20190116.models.GetGroupResponse;
import com.tencentcloudapi.cam.v20190116.models.GroupIdOfUidInfo;
import com.tencentcloudapi.cam.v20190116.models.GroupInfo;
import com.tencentcloudapi.cam.v20190116.models.GroupMemberInfo;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsForUserRequest;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsForUserResponse;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsRequest;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsResponse;
import com.tencentcloudapi.cam.v20190116.models.ListUsersForGroupRequest;
import com.tencentcloudapi.cam.v20190116.models.ListUsersForGroupResponse;
import com.tencentcloudapi.cam.v20190116.models.RemoveUserFromGroupRequest;
import com.tencentcloudapi.cam.v20190116.models.UpdateGroupRequest;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.GroupEntity;
import com.tt.cloud.bean.TencentCloudResponse;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.UserGroupInfo;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

/**
 * 腾讯云用户组管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/18 10:58
 */
@Slf4j
@Component
public class TencentCloudUserGroupHandler {

    @Resource
    private TencentCloudHttpConfig tencentCloudHttpConfig;
    @Resource
    private CloudAccountDao cloudAccountDao;

    /**
     * 新增騰訊雲用戶組
     *
     * @param groupEntity groupEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse createGroup(GroupEntity groupEntity) {
        log.info("新增腾讯云用户组：{}", groupEntity.getGroupName());
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            CreateGroupRequest request = new CreateGroupRequest();
            request.setGroupName(groupEntity.getGroupName());
            request.setRemark(groupEntity.getMemo());
            tencentCloudHttpConfig.getTencentCamClient(groupEntity.getAccountId()).CreateGroup(request);
            log.info("新增腾讯云用户组成功：{}", groupEntity.getGroupName());
        } catch (TencentCloudSDKException e) {
            log.error("新增腾讯云用户组异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("新增腾讯云用户组异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 删除腾讯云用户组
     *
     * @param groupEntity groupEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse deleteGroup(GroupEntity groupEntity) {
        log.info("删除腾讯云用户组:{}", groupEntity.getGroupId());
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            DeleteGroupRequest request = new DeleteGroupRequest();
            request.setGroupId(Long.parseLong(groupEntity.getGroupId()));
            tencentCloudHttpConfig.getTencentCamClient(groupEntity.getAccountId()).DeleteGroup(request);
            log.info("删除腾讯云用户组：{}成功", groupEntity.getGroupId());
        } catch (TencentCloudSDKException e) {
            log.error("删除腾讯云用户组异常");
            tencentCloudResponse.getHeader().setErrorMsg("删除腾讯云用户组异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 更新腾讯云用户组
     *
     * @param groupEntity groupEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse updateGroup(GroupEntity groupEntity) {
        log.info("更新腾讯云用户组：{}", groupEntity);
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            UpdateGroupRequest request = new UpdateGroupRequest();
            request.setGroupId(Long.parseLong(groupEntity.getGroupId()));
            request.setGroupName(groupEntity.getGroupName());
            request.setRemark(groupEntity.getMemo());
            tencentCloudHttpConfig.getTencentCamClient(groupEntity.getAccountId()).UpdateGroup(request);
            log.info("更新腾讯云用户组：{}成功", groupEntity);
        } catch (TencentCloudSDKException e) {
            log.error("更新腾讯云用户组异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("更新腾讯云用户组异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 查詢騰訊雲用戶組詳情
     *
     * @param groupEntity groupEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse getGroupInfo(GroupEntity groupEntity){
        log.info("查询腾讯云用户组详情：{}", groupEntity.getGroupId());
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            GetGroupRequest request = new GetGroupRequest();
            request.setGroupId(Long.parseLong(groupEntity.getGroupId()));
            GetGroupResponse response =
                    tencentCloudHttpConfig.getTencentCamClient(groupEntity.getAccountId()).GetGroup(request);
            GroupEntity getGroup = new GroupEntity();
            getGroup.setGroupId(String.valueOf(response.getGroupId()));
            getGroup.setGroupName(response.getGroupName());
            getGroup.setMemo(response.getRemark());
            getGroup.setCreateTime(response.getCreateTime());
            log.info("查询腾讯云用户组详情成功：{}", getGroup);
            tencentCloudResponse.setGroupEntity(getGroup);
        } catch (TencentCloudSDKException e) {
            log.error("查询腾讯云用户组详情异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("查询腾讯云用户组详情异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 查询用户组列表
     *
     * @param groupEntity groupEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse queryListGroups(GroupEntity groupEntity) {
        log.info("开始查询腾讯云用户组列表");
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            ListGroupsRequest listGroupsRequest = new ListGroupsRequest();
            listGroupsRequest.setKeyword(groupEntity.getGroupName());
            listGroupsRequest.setPage(1L);
            listGroupsRequest.setRp(200L);
            ListGroupsResponse list =
                    tencentCloudHttpConfig.getTencentCamClient(groupEntity.getAccountId())
                            .ListGroups(listGroupsRequest);
            List<GroupEntity> groups = new ArrayList<>();
            getGroupList(list, groups);
            tencentCloudResponse.setGroupList(groups);
        } catch (TencentCloudSDKException e) {
            log.info("开始查询腾讯云用户组列表异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("开始查询腾讯云用户组列表异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    private void getGroupList(ListGroupsResponse list, List<GroupEntity> groups) {
        if (null != list && list.getGroupInfo().length > 0) {
            for (GroupInfo groupInfo : list.getGroupInfo()) {
                GroupEntity getGroup = new GroupEntity();
                getGroup.setGroupId(String.valueOf(groupInfo.getGroupId()));
                getGroup.setGroupName(groupInfo.getGroupName());
                getGroup.setMemo(groupInfo.getRemark());
                getGroup.setCreateTime(groupInfo.getCreateTime());
                groups.add(getGroup);
            }
        }
    }

    /**
     * 查询用户组下用户列表
     *
     * @param userEntity userEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse queryListUsersForGroup(UserEntity userEntity) {
        log.info("查询腾讯云用户组下用户列表:{}", userEntity);
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            ListUsersForGroupRequest request = new ListUsersForGroupRequest();
            request.setGroupId(Long.parseLong(userEntity.getGroupId()));
            request.setPage(1L);
            request.setRp(200L);
            ListUsersForGroupResponse response =
                    tencentCloudHttpConfig.getTencentCamClient(userEntity.getAccountId()).ListUsersForGroup(request);
            GroupMemberInfo[] memberInfoArray = response.getUserInfo();
            List<UserEntity> userList = new ArrayList<>();
            if (null != memberInfoArray && memberInfoArray.length > 0) {
                for (GroupMemberInfo memberInfo : memberInfoArray) {
                    UserEntity userInfo = new UserEntity();
                    userInfo.setUserId(String.valueOf(memberInfo.getUid()));
                    userInfo.setUserIn(String.valueOf(memberInfo.getUid()));
                    userInfo.setUserName(memberInfo.getName());
                    userInfo.setEmail(memberInfo.getEmail());
                    userInfo.setPhone(memberInfo.getPhoneNum());
                    userInfo.setCreateTime(memberInfo.getCreateTime());
                    userList.add(userInfo);
                }
            }
            if (Strings.isNotEmpty(userEntity.getUserName())) { // 根据名称过滤用户列表
                userList = userList.stream()
                        .filter(item -> item.getUserName().toLowerCase(Locale.ROOT)
                                .contains(userEntity.getUserName().toLowerCase(Locale.ROOT)))
                        .collect(Collectors.toList());
            }
            log.info("查询用户组下用户列表成功，共：{}条", userList.size());
            tencentCloudResponse.setUserList(userList);
        } catch (Exception e) {
            log.error("查询腾讯云用户组下用户列表异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("查询腾讯云用户组下用户列表异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 查询用户关联的用户组
     *
     * @param groupEntity groupEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse queryListGroupsForUser(GroupEntity groupEntity) {
        log.info("查询腾讯云用户关联的用户组");
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            ListGroupsForUserRequest request = new ListGroupsForUserRequest();
            request.setUid(Long.parseLong(groupEntity.getUserId()));
            request.setPage(1L);
            request.setRp(200L);
            ListGroupsForUserResponse response =
                    tencentCloudHttpConfig.getTencentCamClient(groupEntity.getAccountId()).ListGroupsForUser(request);
            List<GroupEntity> groupList = new ArrayList<>();
            if (null != response.getGroupInfo() && response.getGroupInfo().length > 0) {
                for (GroupInfo groupInfo : response.getGroupInfo()) {
                    GroupEntity group = new GroupEntity();
                    group.setGroupId(String.valueOf(groupInfo.getGroupId()));
                    group.setGroupName(groupInfo.getGroupName());
                    group.setCreateTime(groupInfo.getCreateTime());
                    group.setMemo(groupInfo.getRemark());
                    groupList.add(group);
                }
            }
            log.info("查询腾讯云用户关联的用户组，用户：{}共光联用户组：{}个", groupEntity.getUserId(), groupList.size());
            tencentCloudResponse.setGroupList(groupList);
        } catch (Exception e) {
            log.error("查询腾讯云用户关联的用户组异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("查询腾讯云用户关联的用户组异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 用户加入到用户组
     *
     * @param groupEntity groupEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse addUserToGroup(GroupEntity groupEntity) {
        log.info("腾讯云用户加入到用户组:{}", groupEntity);
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            if (null != groupEntity.getGroupIdList() && !groupEntity.getGroupIdList().isEmpty()) {
                for (String groupId : groupEntity.getGroupIdList()) {
                    addUserToGroupInfo(groupEntity, groupId);
                }
            }
            if (StringUtils.isNotEmpty(groupEntity.getGroupId())) {
                addUserToGroupInfo(groupEntity, groupEntity.getGroupId());
            }
            log.info("腾讯云用户加入到用户组成功");
        } catch (TencentCloudSDKException e) {
            log.error("腾讯云用户加入到用户组异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("腾讯云用户加入到用户组异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    private void addUserToGroupInfo(GroupEntity groupEntity, String groupId)
            throws TencentCloudSDKException {
        AddUserToGroupRequest request = new AddUserToGroupRequest();
        GroupIdOfUidInfo[] info = new GroupIdOfUidInfo[]{new GroupIdOfUidInfo()};
        if (null != groupEntity.getUserIdList() && !groupEntity.getUserIdList().isEmpty()) {
            for (String userId : groupEntity.getUserIdList()) {
                info[0].setGroupId(Long.parseLong(groupId));
                info[0].setUid(Long.parseLong(userId));
                request.setInfo(info);
                tencentCloudHttpConfig.getTencentCamClient(groupEntity.getAccountId()).AddUserToGroup(request);
            }
        } else {
            info[0].setGroupId(Long.parseLong(groupId));
            info[0].setUid(Long.parseLong(groupEntity.getUserId()));
            request.setInfo(info);
            tencentCloudHttpConfig.getTencentCamClient(groupEntity.getAccountId()).AddUserToGroup(request);
        }
    }

    /**
     * 从用户组删除用户
     *
     * @param groupEntity groupEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse removeUserFromGroup(GroupEntity groupEntity) {
        log.info("腾讯云从用户组删除用户:{}", groupEntity);
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            RemoveUserFromGroupRequest request = new RemoveUserFromGroupRequest();
            if (null != groupEntity.getGroupIdList() && null != groupEntity.getUserIdList()) {
                for (String groupId : groupEntity.getGroupIdList()) {
                    for (String userId : groupEntity.getUserIdList()) {
                        GroupIdOfUidInfo[] info = new GroupIdOfUidInfo[]{new GroupIdOfUidInfo()};
                        info[0].setGroupId(Long.parseLong(groupId));
                        info[0].setUid(Long.parseLong(userId));
                        request.setInfo(info);
                        tencentCloudHttpConfig.getTencentCamClient(groupEntity.getAccountId()).RemoveUserFromGroup(request);
                    }
                }
            }
            log.info("腾讯云从用户组删除用户成功");
        } catch (Exception e) {
            log.error("腾讯云从用户组删除用户异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("腾讯云从用户组删除用户异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 查询全量用户组列表
     *
     * @return List
     */
    public List<UserGroupInfo> queryAllGroupList() throws TencentCloudSDKException {
        List<GroupInfo> groupInfoList = new ArrayList<>();
        List<UserGroupInfo> groups = new ArrayList<>();
        List<CloudAccountEntity> accountList = cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
        for (CloudAccountEntity cloudAccount : accountList) {
            if (Constants.TENCENT_CLOUD.equals(cloudAccount.getCloudType())) {
                ListGroupsRequest listGroupsRequest = new ListGroupsRequest();
                listGroupsRequest.setRp(200L);
                listGroupsRequest.setPage(1L);
                ListGroupsResponse list = tencentCloudHttpConfig.getTencentCamClient(cloudAccount.getId())
                        .ListGroups(listGroupsRequest);
                if (null != list && list.getGroupInfo().length > 0) {
                    if (groupInfoList.isEmpty()) {
                        groupInfoList.addAll(Arrays.asList(list.getGroupInfo()));
                    } else {
                        List<GroupInfo> groupInfos = Arrays.asList(list.getGroupInfo());
                        groupInfoList = groupInfoList.stream()
                                .filter(item -> groupInfos.stream()
                                        .anyMatch(item1 -> item.getGroupName()
                                                .equals(item1.getGroupName())))
                                .collect(Collectors.toList());
                    }
                }
            }
        }
        for (GroupInfo groupInfo : groupInfoList) {
            UserGroupInfo userGroupInfo = new UserGroupInfo();
            userGroupInfo.setGroupId(groupInfo.getGroupName());
            userGroupInfo.setGroupName(groupInfo.getGroupName());
            groups.add(userGroupInfo);
        }
        return groups;
    }


}
