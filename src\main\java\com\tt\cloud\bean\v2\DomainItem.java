package com.tt.cloud.bean.v2;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2023/4/23 17:25
 */
@Getter
@Setter
@ToString
public class DomainItem {
    private String domain_id;
    private String domain_name;
    private String sub_domain;
    private String sub_domain_id;
    private String record_type;
    private String record_value;
    private String operation_type;
    private String line_id;
    private String record_line;
    private long ttl;
    private long weight;
    private long mx;
    private String email;
}
