package com.tt.cloud.handler.aws;

import com.tt.cloud.bean.AwsCloudResponse;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.config.AwsCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.handler.AbstractCloudPermissionHandler;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.services.iam.model.AttachGroupPolicyRequest;
import software.amazon.awssdk.services.iam.model.AttachUserPolicyRequest;
import software.amazon.awssdk.services.iam.model.AttachedPolicy;
import software.amazon.awssdk.services.iam.model.GetPolicyRequest;
import software.amazon.awssdk.services.iam.model.GetPolicyResponse;
import software.amazon.awssdk.services.iam.model.GetPolicyVersionRequest;
import software.amazon.awssdk.services.iam.model.GetPolicyVersionResponse;
import software.amazon.awssdk.services.iam.model.Group;
import software.amazon.awssdk.services.iam.model.ListAttachedGroupPoliciesRequest;
import software.amazon.awssdk.services.iam.model.ListAttachedGroupPoliciesResponse;
import software.amazon.awssdk.services.iam.model.ListAttachedUserPoliciesRequest;
import software.amazon.awssdk.services.iam.model.ListAttachedUserPoliciesResponse;
import software.amazon.awssdk.services.iam.model.ListGroupsForUserRequest;
import software.amazon.awssdk.services.iam.model.ListGroupsForUserResponse;
import software.amazon.awssdk.services.iam.model.ListPoliciesRequest;
import software.amazon.awssdk.services.iam.model.ListPoliciesResponse;
import software.amazon.awssdk.services.iam.model.Policy;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/24 11:10
 */
@Slf4j
@Component
public class AwsCloudPermissionHandler extends AbstractCloudPermissionHandler {
    @Resource
    private AwsCloudHttpConfig awsCloudHttpConfig;

    public boolean supports(String cloudType) {
        return Constants.AWS_CLOUD.equals(cloudType);
    }

    /**
     * 查询aws权限列表
     *
     * @param permissionEntity permissionEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse queryPermissionList(PermissionEntity permissionEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            List<PermissionEntity> permissionList = new ArrayList<>();

            ListPoliciesResponse listPoliciesResponse = awsCloudHttpConfig.getAWSIamClient(permissionEntity.getAccountId()).listPolicies(
                    ListPoliciesRequest.builder().maxItems(1000).build()
            );
            List<Policy> policies = listPoliciesResponse.policies();
            if (null != policies
                    && !policies.isEmpty()
                    && StringUtils.isNotEmpty(permissionEntity.getPolicyName())) {
                policies = listPoliciesResponse.policies().stream()
                        .filter(item -> item.policyName().toLowerCase(Locale.ROOT)
                                .contains(permissionEntity.getPolicyName().toLowerCase(Locale.ROOT)))
                        .collect(Collectors.toList());
            }
            if (null != policies) {
                for (Policy policy : policies) {
                    PermissionEntity permission = new PermissionEntity();
                    permission.setPolicyName(policy.policyName());
                    permission.setAddTime(policy.createDate().toString());
                    permission.setDescription(policy.description());
                    permission.setArn(policy.arn());
                    permissionList.add(permission);
                }
            }
            awsCloudResponse.setPermissionList(permissionList);
        } catch (Exception e) {
            log.error("查询AWS权限列表异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询AWS权限列表异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 查询aws用户全部权限列表
     *
     * @param permissionEntity permissionEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse queryUserPermissions(PermissionEntity permissionEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            List<PermissionEntity> permissionList = new ArrayList<>();
            if (null == permissionEntity.getPermissionType()
                    || Constants.BELONG_TYPE_USER.equals(permissionEntity.getPermissionType())) {
                ListAttachedUserPoliciesRequest listAttachedUserPoliciesRequest =
                        ListAttachedUserPoliciesRequest.builder().userName(permissionEntity.getUserName()).build();
                ListAttachedUserPoliciesResponse listAttachedUserPoliciesResponse =
                        awsCloudHttpConfig.getAWSIamClient(permissionEntity.getAccountId()).listAttachedUserPolicies(listAttachedUserPoliciesRequest);
                for (AttachedPolicy attachedPolicy : listAttachedUserPoliciesResponse.attachedPolicies()) {
                    PermissionEntity permission = new PermissionEntity();
                    permission.setPolicyName(attachedPolicy.policyName());
                    permission.setBelongName(permissionEntity.getUserName());
                    permission.setBelongType(Constants.BELONG_TYPE_USER);
                    permission.setArn(attachedPolicy.policyArn());
                    permissionList.add(permission);
                }
                log.info("个人权限:{}", permissionList.size());
            }
            if (null == permissionEntity.getPermissionType()
                    || Constants.BELONG_TYPE_GROUP.equals(permissionEntity.getPermissionType())) {
                ListGroupsForUserRequest listGroupsForUserRequest =
                        ListGroupsForUserRequest.builder().userName(permissionEntity.getUserName()).build();
                ListGroupsForUserResponse listGroupsForUserResponse =
                        awsCloudHttpConfig.getAWSIamClient(permissionEntity.getAccountId()).listGroupsForUser(listGroupsForUserRequest);
                for (Group group : listGroupsForUserResponse.groups()) {
                    ListAttachedGroupPoliciesRequest listAttachedGroupPoliciesRequest =
                            ListAttachedGroupPoliciesRequest.builder().groupName(group.groupName()).build();
                    ListAttachedGroupPoliciesResponse listAttachedGroupPoliciesResponse =
                            awsCloudHttpConfig.getAWSIamClient(permissionEntity.getAccountId()).listAttachedGroupPolicies(listAttachedGroupPoliciesRequest);
                    for (AttachedPolicy attachedPolicy : listAttachedGroupPoliciesResponse.attachedPolicies()) {
                        PermissionEntity permission = new PermissionEntity();
                        permission.setPolicyName(attachedPolicy.policyName());
                        permission.setBelongName(group.groupName());
                        permission.setBelongType(Constants.BELONG_TYPE_GROUP);
                        permission.setArn(attachedPolicy.policyArn());
                        permissionList.add(permission);
                    }
                    log.info("分组权限:{}", listAttachedGroupPoliciesResponse.attachedPolicies().size());
                }
            }
            awsCloudResponse.setPermissionList(permissionList);
        } catch (AwsServiceException | SdkClientException e) {
            if (!e.getMessage().contains("cannot be found")) {
                log.error("查询aws用户全部权限列表异常" , e);
                awsCloudResponse.getHeader().setErrorMsg("查询aws用户全部权限列表异常: " + e.getMessage());
                return awsCloudResponse;
            }
            if (!permissionEntity.getUserName().contains("@52tt.com")) {
                permissionEntity.setUserName(permissionEntity.getUserName() + "@52tt.com");
                return queryUserPermissions(permissionEntity);
            }

        }
        return awsCloudResponse;
    }

    /**
     * 向aws用户授权
     *
     * @param permissionEntity permissionEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse grantUserPermissions(PermissionEntity permissionEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            if (null != permissionEntity.getPermissionList()
                    && !permissionEntity.getPermissionList().isEmpty()) {
                for (PermissionEntity permission : permissionEntity.getPermissionList()) {
                    AttachUserPolicyRequest attachUserPolicyRequest =
                            AttachUserPolicyRequest.builder().userName(permissionEntity.getUserName())
                                    .policyArn(permission.getArn()).build();
                    awsCloudHttpConfig.getAWSIamClient(permissionEntity.getAccountId()).attachUserPolicy(attachUserPolicyRequest);
                }
            }
        } catch (AwsServiceException | SdkClientException e) {
            log.error("向aws用户授权异常", e);
            awsCloudResponse.getHeader().setErrorMsg("向aws用户授权异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 查询aws用户组权限列表
     *
     * @param permissionEntity permissionEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse queryGroupPermissionList(PermissionEntity permissionEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            if (StringUtils.isNotEmpty(permissionEntity.getGroupName())) {
                List<PermissionEntity> permissionList = new ArrayList<>();
                ListAttachedGroupPoliciesRequest listAttachedGroupPoliciesRequest =
                        ListAttachedGroupPoliciesRequest.builder()
                                .groupName(permissionEntity.getGroupName()).maxItems(1000).build();
                ListAttachedGroupPoliciesResponse listAttachedGroupPoliciesResponse =
                        awsCloudHttpConfig.getAWSIamClient(permissionEntity.getAccountId())
                                .listAttachedGroupPolicies(listAttachedGroupPoliciesRequest);
                for (AttachedPolicy attachedPolicy : listAttachedGroupPoliciesResponse.attachedPolicies()) {
                    PermissionEntity permission = new PermissionEntity();
                    permission.setPolicyName(attachedPolicy.policyName());
                    permission.setArn(attachedPolicy.policyArn());
                    permissionList.add(permission);
                }
                awsCloudResponse.setPermissionList(permissionList);
            }
        } catch (AwsServiceException | SdkClientException e) {
            log.error("查询aws用户组权限列表异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询aws用户组权限列表异常：" + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * aws用户组授权
     *
     * @param permissionEntity permissionEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse grantGroupPermissions(PermissionEntity permissionEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            if (StringUtils.isNotEmpty(permissionEntity.getGroupName())) {
                if (null != permissionEntity.getPolicyArnList()
                        && !permissionEntity.getPolicyArnList().isEmpty()) {
                    for (String arn : permissionEntity.getPolicyArnList()) {
                        AttachGroupPolicyRequest attachGroupPolicyRequest =
                                AttachGroupPolicyRequest.builder()
                                        .groupName(permissionEntity.getGroupName())
                                        .policyArn(arn).build();
                        awsCloudHttpConfig.getAWSIamClient(permissionEntity.getAccountId()).attachGroupPolicy(attachGroupPolicyRequest);
                    }
                }
            }
        } catch (AwsServiceException | SdkClientException e) {
            log.error("aws用户组授权", e);
            awsCloudResponse.getHeader().setErrorMsg("aws用户组授权: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 查询权限详情
     *
     * @param permissionEntity permissionEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse queryPermissionVersion(PermissionEntity permissionEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            GetPolicyRequest getPolicyRequest = GetPolicyRequest.builder().policyArn(permissionEntity.getArn()).build();
            GetPolicyResponse getPolicyResponse = awsCloudHttpConfig.getAWSIamClient(permissionEntity.getAccountId()).getPolicy(getPolicyRequest);
            GetPolicyVersionRequest getPolicyVersionRequest =
                    GetPolicyVersionRequest.builder().policyArn(getPolicyResponse.policy().arn()).versionId(getPolicyResponse.policy().defaultVersionId()).build();
            GetPolicyVersionResponse getPolicyVersionResponse = awsCloudHttpConfig.getAWSIamClient(permissionEntity.getAccountId()).getPolicyVersion(getPolicyVersionRequest);
            awsCloudResponse.setPolicyVersion(URLDecoder.decode(getPolicyVersionResponse.policyVersion().document(), "UTF-8"));
        } catch (AwsServiceException | SdkClientException | UnsupportedEncodingException e) {
            log.error("查询权限版本内容异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询权限版本内容异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }
}
