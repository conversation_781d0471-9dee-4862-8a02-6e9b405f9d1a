package com.tt.cloud.context;


import com.tt.cloud.bean.UserInfo;

/**
 * 用户线程变量
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2021/9/6 11:21
 */
public class UserContext {
    private static final ThreadLocal<UserInfo> USER_INFO_ENTITY_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 获取
     *
     * @return Sso
     */
    public static UserInfo getUserInfoThreadLocal() {
        return USER_INFO_ENTITY_THREAD_LOCAL.get();
    }

    /**
     * 清除
     */
    public static void clearUserInfoThreadLocal() {
        USER_INFO_ENTITY_THREAD_LOCAL.remove();
    }

    /**
     * 获取当前登录用户ID
     *
     * @return String
     */
    public static String getCurrentUserId() {
        UserInfo userEntity = USER_INFO_ENTITY_THREAD_LOCAL.get();
        if (null != userEntity) {
            return userEntity.getUserId();
        }
        return "";
    }

    /**
     * 设置
     *
     * @param userInfo userInfo
     */
    public static void setUserInfoThreadLocal(UserInfo userInfo) {
        USER_INFO_ENTITY_THREAD_LOCAL.set(userInfo);
    }

}
