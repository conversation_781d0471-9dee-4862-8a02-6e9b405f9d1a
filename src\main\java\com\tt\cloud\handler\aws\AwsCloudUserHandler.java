package com.tt.cloud.handler.aws;

import com.tt.cloud.bean.AwsCloudResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAllUserResponse;
import com.tt.cloud.bean.UserAuthEntity;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.config.AwsCloudHttpConfig;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.services.iam.model.AddUserToGroupRequest;
import software.amazon.awssdk.services.iam.model.CreateLoginProfileRequest;
import software.amazon.awssdk.services.iam.model.CreateUserRequest;
import software.amazon.awssdk.services.iam.model.GetUserRequest;
import software.amazon.awssdk.services.iam.model.GetUserResponse;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/23 17:59
 */
@Slf4j
@Component
public class AwsCloudUserHandler {
    @Resource
    private AwsCloudHttpConfig awsCloudHttpConfig;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private ApplicationContext applicationContext;


    private void sendNotice(UserEntity userEntity, CloudAccountEntity cloudAccountEntity)
            throws Exception {
        String loginUrl = "https://" + cloudAccountEntity.getDomainId() + ".signin.aws.amazon.com/console";
        loginUrl = "[" + loginUrl + "](" + loginUrl + ")";
        Map<String, Object> messageParam = new HashMap<>();
        messageParam.put("cloud", cloudAccountEntity.getMemo() + "/" + cloudAccountEntity.getName());
        messageParam.put("username", userEntity.getUserName());
        messageParam.put("password", userEntity.getPassword());
        messageParam.put("url", loginUrl);
        String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
        content = Utils.getParseValue(applicationContext, messageParam, content);
        flyBookService.sendMessage(userEntity.getEmail(), content);
    }

    /**
     * 创建AWS用户
     *
     * @param userEntity userEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse createUser(UserEntity userEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            CreateUserRequest createUserRequest = CreateUserRequest.builder().userName(userEntity.getUserName()).build();
            awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).createUser(createUserRequest);
            if (!userEntity.isApiUser()) {
                CreateLoginProfileRequest createLoginProfileRequest = CreateLoginProfileRequest.builder()
                        .userName(userEntity.getUserName()).password(Utils.getPassword()).passwordResetRequired(true).build();
                awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).createLoginProfile(createLoginProfileRequest);
                userEntity.setPassword(createLoginProfileRequest.password());
            }
            if (null != userEntity.getGroupNameList() && !userEntity.getGroupNameList().isEmpty()) {
                for (String groupName : userEntity.getGroupNameList()) {
                    AddUserToGroupRequest addUserToGroupRequest =
                            AddUserToGroupRequest.builder().userName(userEntity.getUserName())
                                    .groupName(groupName).build();
                    awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                }
            }
            if (!userEntity.isApiUser()) {
                CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
                sendNotice(userEntity, cloudAccountEntity);
            }
            awsCloudResponse.setUserEntity(userEntity);
        } catch (Exception e) {
            log.error("创建AWS用户异常", e);
            awsCloudResponse.getHeader().setErrorMsg("创建AWS用户异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }



    /**
     * 查询用户是否存在
     *
     * @param userEntity userEntity
     * @return AliCloudResponse
     */
    public AwsCloudResponse queryUserExists(UserEntity userEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            UserAuthEntity userAuthInfo = new UserAuthEntity();
            userAuthInfo.setAccountId(userEntity.getAccountId());
            userAuthInfo.setUname(userEntity.getUserName());
            UserAuthEntity userAuthEntity = cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
            if (null != userAuthEntity) {
                UserEntity user = new UserEntity();
                user.setUserId(userAuthEntity.getUid());
                user.setUserName(userAuthEntity.getUname());
                awsCloudResponse.setUserEntity(user);
            } else {
                awsCloudResponse.setUserEntity(queryUser(userEntity));
            }
        } catch (Exception e) {
            log.error("查询aws用户详情异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询aws用户详情异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    public UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        try {
            GetUserRequest getUserRequest =
                    GetUserRequest.builder().userName(userEntity.getUserName()).build();
            GetUserResponse getUserResponse =
                    awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).getUser(getUserRequest);
            if (null != getUserResponse.user()) {
                userInfo = new UserEntity();
                userInfo.setUserId(getUserResponse.user().userId());
                userInfo.setUserName(getUserResponse.user().userName());
            }
        } catch (AwsServiceException | SdkClientException e) {
            if (e.getMessage().contains("cannot be found")) {
                if (!userEntity.getUserName().contains("@52tt.com")) {
                    userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                    return queryUser(userEntity);
                }
            }
        }
        return userInfo;
    }

    public CloudAllUserResponse queryUserListByAccount(CloudAccountEntity cloudAccount, String nextMarker) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'queryUserListByAccount'");
    }


}
