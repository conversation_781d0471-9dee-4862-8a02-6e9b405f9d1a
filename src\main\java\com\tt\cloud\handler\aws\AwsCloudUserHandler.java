package com.tt.cloud.handler.aws;

import com.tt.cloud.config.AwsCloudHttpConfig;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.services.iam.IamClient;
import software.amazon.awssdk.services.iam.model.*;
import com.tt.cloud.bean.*;
import com.tt.cloud.constant.Constants;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/23 17:59
 */
@Slf4j
@Component
public class AwsCloudUserHandler {
    @Resource
    private AwsCloudHttpConfig awsCloudHttpConfig;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private ApplicationContext applicationContext;


    private void sendNotice(UserEntity userEntity, CloudAccountEntity cloudAccountEntity)
            throws Exception {
        String loginUrl = "https://" + cloudAccountEntity.getDomainId() + ".signin.aws.amazon.com/console";
        loginUrl = "[" + loginUrl + "](" + loginUrl + ")";
        Map<String, Object> messageParam = new HashMap<>();
        messageParam.put("cloud", cloudAccountEntity.getMemo() + "/" + cloudAccountEntity.getName());
        messageParam.put("username", userEntity.getUserName());
        messageParam.put("password", userEntity.getPassword());
        messageParam.put("url", loginUrl);
        String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
        content = Utils.getParseValue(applicationContext, messageParam, content);
        flyBookService.sendMessage(userEntity.getEmail(), content);
    }

    /**
     * 创建AWS用户
     *
     * @param userEntity userEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse createUser(UserEntity userEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            CreateUserRequest createUserRequest = CreateUserRequest.builder().userName(userEntity.getUserName()).build();
            awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).createUser(createUserRequest);
            if (!userEntity.isApiUser()) {
                CreateLoginProfileRequest createLoginProfileRequest = CreateLoginProfileRequest.builder()
                        .userName(userEntity.getUserName()).password(Utils.getPassword()).passwordResetRequired(true).build();
                awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).createLoginProfile(createLoginProfileRequest);
                userEntity.setPassword(createLoginProfileRequest.password());
            }
            if (null != userEntity.getGroupNameList() && !userEntity.getGroupNameList().isEmpty()) {
                for (String groupName : userEntity.getGroupNameList()) {
                    AddUserToGroupRequest addUserToGroupRequest =
                            AddUserToGroupRequest.builder().userName(userEntity.getUserName())
                                    .groupName(groupName).build();
                    awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                }
            }
            if (!userEntity.isApiUser()) {
                CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
                sendNotice(userEntity, cloudAccountEntity);
            }
            awsCloudResponse.setUserEntity(userEntity);
        } catch (Exception e) {
            log.error("创建AWS用户异常", e);
            awsCloudResponse.getHeader().setErrorMsg("创建AWS用户异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }



    /**
     * 查询用户是否存在
     *
     * @param userEntity userEntity
     * @return AliCloudResponse
     */
    public AwsCloudResponse queryUserExists(UserEntity userEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            UserAuthEntity userAuthInfo = new UserAuthEntity();
            userAuthInfo.setAccountId(userEntity.getAccountId());
            userAuthInfo.setUname(userEntity.getUserName());
            UserAuthEntity userAuthEntity = cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
            if (null != userAuthEntity) {
                UserEntity user = new UserEntity();
                user.setUserId(userAuthEntity.getUid());
                user.setUserName(userAuthEntity.getUname());
                awsCloudResponse.setUserEntity(user);
            } else {
                awsCloudResponse.setUserEntity(queryUser(userEntity));
            }
        } catch (Exception e) {
            log.error("查询aws用户详情异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询aws用户详情异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    public UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        try {
            GetUserRequest getUserRequest =
                    GetUserRequest.builder().userName(userEntity.getUserName()).build();
            GetUserResponse getUserResponse =
                    awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).getUser(getUserRequest);
            if (null != getUserResponse.user()) {
                userInfo = new UserEntity();
                userInfo.setUserId(getUserResponse.user().userId());
                userInfo.setUserName(getUserResponse.user().userName());
            }
        } catch (AwsServiceException | SdkClientException e) {
            if (e.getMessage().contains("cannot be found")) {
                if (!userEntity.getUserName().contains("@52tt.com")) {
                    userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                    return queryUser(userEntity);
                }
            }
        }
        return userInfo;
    }

    public CloudAllUserResponse queryUserListByAccount(CloudAccountEntity cloudAccount, String nextMarker) {
        CloudAllUserResponse userListRst = new CloudAllUserResponse();
        List<UserEntity> userList = new ArrayList<>();

        try {
            log.info("AWS云子用户详情列表，nextMarker: {}", nextMarker);

            // AWS支持分页获取用户列表，不需要缓存到redis，每次传入nextMarker分别获取
            ListUsersRequest.Builder requestBuilder = ListUsersRequest.builder()
                    .maxItems(20); // 每页20条记录

            if (StringUtils.isNotEmpty(nextMarker)) {
                requestBuilder.marker(nextMarker);
            }

            ListUsersResponse response = awsCloudHttpConfig.getAWSIamClient(cloudAccount.getId()).listUsers(requestBuilder.build());
            if (response == null) {
                log.info("AWS用户列表查询结果为空");
                userListRst.setUserList(userList);
                userListRst.setNextMarker(null);
                return userListRst;
            }

            if (response.users() != null && !response.users().isEmpty()) {
                for (User user : response.users()) {
                    UserEntity userEntity = new UserEntity();
                    userEntity.setUserId(safeGetString(user.userId()));
                    userEntity.setUserIn(safeGetString(user.arn())); // AWS没有UIN概念，使用UserId
                    userEntity.setUserName(safeGetString(user.userName()));
                    userEntity.setCreateTime(user.createDate() != null ? user.createDate().toString() : "");
                    userEntity.setAccountId(cloudAccount.getDomainId());
                    userList.add(userEntity);
                }
            }

            // 对当前页的用户列表进行详细信息补充
            attachUserInfos(userList, cloudAccount.getId());

            // 设置返回结果
            userListRst.setUserList(userList);
            // 设置下一页的nextMarker
            if (response.isTruncated() != null && response.isTruncated()) {
                userListRst.setNextMarker(response.marker());
            } else {
                userListRst.setNextMarker(Strings.EMPTY);
            }

            log.info("AWS用户列表查询完成，当前页用户数: {}, nextMarker: {}", userList.size(), userListRst.getNextMarker());

        } catch (Exception e) {
            log.error("AWS子用户详情列表异常", e);
            throw new RuntimeException("AWS子用户详情列表异常：" + e.getMessage());
        }

        return userListRst;
    }

    /**
     * 安全获取字符串值，如果为null则返回空字符串
     */
    private String safeGetString(String value) {
        return value != null ? value : "";
    }

    /**
     * 补充用户详细信息
     */
    private void attachUserInfos(List<UserEntity> userList, String accountId) {
        if (userList == null || userList.isEmpty()) {
            return;
        }
        try {
            // 获取用户信息
            for (int i = 0; i < userList.size(); i++) {
                UserEntity user = userList.get(i);
                user.setPolicyList(new ArrayList<>());
                IamClient iamClient =awsCloudHttpConfig.getAWSIamClient(accountId);

                


                // 1. 获取用户组列表
                try {
                    ListGroupsForUserRequest listGroupsRequest = ListGroupsForUserRequest.builder()
                            .userName(user.getUserName())
                            .build();
                    ListGroupsForUserResponse listGroupsResponse = iamClient.listGroupsForUser(listGroupsRequest);

                    if (listGroupsResponse != null && listGroupsResponse.groups() != null && !listGroupsResponse.groups().isEmpty()) {
                        List<UserGroupInfo> groupList = new ArrayList<>();
                        for (Group group : listGroupsResponse.groups()) {
                            UserGroupInfo userGroupInfo = new UserGroupInfo();
                            userGroupInfo.setGroupId(safeGetString(group.groupId()));
                            userGroupInfo.setGroupName(safeGetString(group.groupName()));
                            userGroupInfo.setRemark(""); // AWS Group对象不直接包含描述信息
                            groupList.add(userGroupInfo);

                            // 1.1 获取用户组权限列表
                            try {
                                ListAttachedGroupPoliciesRequest listGroupPoliciesRequest = ListAttachedGroupPoliciesRequest.builder()
                                        .groupName(group.groupName())
                                        .build();
                                ListAttachedGroupPoliciesResponse listGroupPoliciesResponse = iamClient.listAttachedGroupPolicies(listGroupPoliciesRequest);

                                if (listGroupPoliciesResponse != null && listGroupPoliciesResponse.attachedPolicies() != null
                                    && !listGroupPoliciesResponse.attachedPolicies().isEmpty()) {
                                    List<PermissionEntity> policyList = new ArrayList<>();
                                    for (AttachedPolicy policy : listGroupPoliciesResponse.attachedPolicies()) {
                                        PermissionEntity permission = new PermissionEntity();
                                        permission.setPolicyName(safeGetString(policy.policyName()));
                                        permission.setPolicyType(""); // AWS AttachedPolicy不直接返回类型
                                        permission.setAddTime(""); // AWS AttachedPolicy不返回附加时间
                                        permission.setRemark(""); // AWS AttachedPolicy不返回描述
                                        permission.setBelongId(group.groupId());
                                        permission.setBelongName(group.groupName());
                                        permission.setBelongType(Constants.BELONG_TYPE_GROUP);
                                        permission.setArn(safeGetString(policy.policyArn()));
                                        policyList.add(permission);
                                    }
                                    user.getPolicyList().addAll(policyList);
                                }
                            } catch (Exception e) {
                                log.error("获取用户组权限信息异常: {}", e.getMessage());
                            }
                        }
                        user.setGroupList(groupList);
                    }
                } catch (Exception e) {
                    log.error("获取用户组信息异常: {}", e.getMessage());
                }

                // 2. 获取用户权限列表
                try {
                    // 初始化用户权限列表
                    user.setPolicyList(new ArrayList<>());

                    ListAttachedUserPoliciesRequest listUserPoliciesRequest = ListAttachedUserPoliciesRequest.builder()
                            .userName(user.getUserName())
                            .build();
                    ListAttachedUserPoliciesResponse listUserPoliciesResponse = iamClient.listAttachedUserPolicies(listUserPoliciesRequest);

                    if (listUserPoliciesResponse != null && listUserPoliciesResponse.attachedPolicies() != null
                        && !listUserPoliciesResponse.attachedPolicies().isEmpty()) {
                        List<PermissionEntity> policyList = new ArrayList<>();
                        for (AttachedPolicy policy : listUserPoliciesResponse.attachedPolicies()) {
                            PermissionEntity permission = new PermissionEntity();
                            permission.setPolicyName(safeGetString(policy.policyName()));
                            permission.setPolicyType(""); // AWS AttachedPolicy不直接返回类型
                            permission.setAddTime(""); // AWS AttachedPolicy不返回附加时间
                            permission.setRemark(""); // AWS AttachedPolicy不返回描述
                            permission.setBelongType(Constants.BELONG_TYPE_USER);
                            permission.setArn(safeGetString(policy.policyArn()));
                            policyList.add(permission);
                        }
                        user.getPolicyList().addAll(policyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户权限信息异常: {}", e.getMessage());
                }

                // 3. 获取用户密钥ID列表
                try {
                    ListAccessKeysRequest listAccessKeysRequest = ListAccessKeysRequest.builder()
                            .userName(user.getUserName())
                            .build();
                    ListAccessKeysResponse listAccessKeysResponse = iamClient.listAccessKeys(listAccessKeysRequest);

                    if (listAccessKeysResponse != null && listAccessKeysResponse.accessKeyMetadata() != null
                        && !listAccessKeysResponse.accessKeyMetadata().isEmpty()) {
                        List<PermanentAccessKeyEntity> accessKeyList = new ArrayList<>();
                        for (AccessKeyMetadata accessKey : listAccessKeysResponse.accessKeyMetadata()) {
                            PermanentAccessKeyEntity accessKeyEntity = new PermanentAccessKeyEntity();
                            accessKeyEntity.setAccess(safeGetString(accessKey.accessKeyId()));
                            accessKeyEntity.setStatus(accessKey.status() != null ? accessKey.status().toString() : "");
                            accessKeyEntity.setCreateTime(accessKey.createDate() != null ? accessKey.createDate().toString() : "");
                            accessKeyEntity.setLastUsedDate(""); // AWS需要单独调用GetAccessKeyLastUsed获取
                            accessKeyEntity.setUserId(user.getUserId());
                            accessKeyEntity.setUserName(user.getUserName());
                            accessKeyList.add(accessKeyEntity);
                        }
                        user.setAccessKeyList(accessKeyList);
                    } else {
                        user.setAccessKeyList(new ArrayList<>());
                    }
                } catch (Exception e) {
                    log.error("获取用户密钥信息异常: {}", e.getMessage());
                    user.setAccessKeyList(new ArrayList<>());
                }

                userList.set(i, user);
            }
        } catch (Exception e) {
            log.error("AWS完善用户详细信息异常", e);
        }
    }

}
