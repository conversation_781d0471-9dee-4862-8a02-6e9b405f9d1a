package com.tt.cloud.controller;

import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.CloudAccountResponse;
import com.tt.cloud.service.CloudAccountServiceImpl;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 云商账号
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/26 15:08
 */
@Slf4j
@RestController
@RequestMapping("/rest/v1")
public class CloudAccountController {

    @Resource
    private CloudAccountServiceImpl cloudAccountService;

    /**
     * 查询云商账号列表
     *
     * @param cloudAccountRequest cloudAccountRequest
     * @return CloudAccountResponse
     */
    @GetMapping("/account/list")
    public CloudAccountResponse queryCloudAccountList(CloudAccountRequest cloudAccountRequest) {
        return cloudAccountService.queryCloudAccountList(cloudAccountRequest);
    }

}
