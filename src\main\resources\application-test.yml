spring:
  application:
    name: tt-cloud-auth-platform-web
  datasource:
    # 配置数据源类型
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************
    username: root
    password: Wh068794.
    druid:
      #初始化大小
      initialSize: 5
      #最小值
      minIdle: 10
      #最大值
      maxActive: 1500
      #最大等待时间，配置获取连接等待超时，时间单位都是毫秒ms
      maxWait: 15000
      #配置间隔多久才进行一次检测，检测需要关闭的空闲连接
      timeBetweenEvictionRunsMillis: 60000
      #配置一个连接在池中最小生存的时间
      minEvictableIdleTimeMillis: 30000
      validationQueryTimeout: 30000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: false
      removeAbandoned: false
      logAbandoned: false
      removeAbandonedTimeout: 60
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，
      #'wall'用于防火墙，SpringBoot中没有log4j，我改成了log4j2
      filters: stat,wall,log4j2
  rabbitmq:
    addresses: **************
    port: 5672
    username: root
    password: 123456
    virtual-host: /
    listener:
      simple:
        prefetch: 1

server:
  port: 9097
  servlet:
    context-path: /tt-cloud-auth-platform-web

logging:
  config: classpath:logback.xml
  level:
    cn.jay.repository: trace

http:
  maxTotal: 1000                           # 最大连接数
  defaultMaxPerRoute: 50                  # 并发数
  connectTimeout: 5000                    # 创建连接最长时间
  connectionRequestTimeout: 5000          # 从连接池中获取到连接的最长时间
  socketTimeout: 10000                    # 数据传输的最长时间

#mybatyis的配置
mybatis:
  mapper-locations: classpath:mapper/*.xml #指定mapper的配置文件的路径是mapper文件夹下的所有 xml文件
# 单点登录
sso:
  client_id: tt-cloud-auth-platform
  grant_type: authorization_code
  client_secret: yjlhy2i5mjatngnhoc00njdjltlkntgtmwjjntezzgrizgvh
  private_key: |-
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  salt: PT6cHD7IFyWdlSLM
  sc: tt-cloud-auth-platform:development
  name: 多云权限管理平台:云开发
  sso_api: http://testing-yw.ttyuyin.com/sso/
  validate_api: http://testing-yw-sso-inner.ttyuyin.com:10001/validate/
  logout_api: http://testing-yw.ttyuyin.com/accounts/logout/
  public_domain: http://testing-yw.ttyuyin.com
  jsonrpc: http://testing-yw-sso-inner.ttyuyin.com:10001/api/jsonrpc/

# 跳转首页
tt:
  op:
    home_url: http://**************:1080/

monitor:
  url: http://testing-tt-telemetry.ttyuyin.com/solo/api/v2/thanos/query_range?query=
  telemetry_session: OB2ILdW+lErw5lwhGG6tlO9iCcJhUSay6njdqJxvNgg=

cmdb:
  sync_url: http://yw-inner-cmdb.ttyuyin.com:5000/api/jsonrpc/
  Authorization: aDvBYqwf0MPupIwuHWDEj0rt33KsnaiU2dtpGZ2tLFnL3QRAL8scE5mYAWMwe6Vk

oapi:
  appId: cli_a68e73fb4863900b
  appSecret: FKsKb4ZVoCduvQQkvyy7ChxOiMXRDLzU

tmpauth:
  approver: <EMAIL>
