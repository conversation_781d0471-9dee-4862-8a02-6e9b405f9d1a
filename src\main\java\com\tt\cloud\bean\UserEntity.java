package com.tt.cloud.bean;

import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 15:53
 */
@Getter
@Setter
@ToString
public class UserEntity {
    private String userId;
    private String userIn;
    private String userName;
    private String newUserName;
    private String email;
    private String newEmail;
    private String areaCode;
    private String phone;
    private String newPhone;
    private Long Force; // 1:强制删除，帶有API秘鑰的子用戶
    private Long consoleLogin; // 子用户是否可以登录控制台。传0子用户无法登录控制台，传1子用户可以登录控制台。
    private Long userApi;
    private String remark; // 子用户备注
    private String password;
    private String pwdStatus;
    private String accountId;

    private String countryCode;
    private String createTime;
    private String updateTime;
    private String lastLoginTime;
    private String lastLoginIp;
    private String groupId;
    private Long needResetPassword;
    private String originalPassword;
    private String accessMode;
    private Boolean enabled;
    private Boolean isDomainOwner;

    private List<String> groupIdList;
    private List<String> groupNameList;

     private List<UserGroupInfo> groupList;
    private List<PermissionEntity> policyList ;
    private List<PermanentAccessKeyEntity> accessKeyList;

    private String groupName;

    private int startNum = 1;
    private int pageSize = 10;

    private boolean apiUser = false;

}

