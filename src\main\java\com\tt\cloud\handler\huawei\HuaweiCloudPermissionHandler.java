package com.tt.cloud.handler.huawei;

import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.KeystoneAssociateGroupWithDomainPermissionRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneAssociateGroupWithProjectPermissionRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneGroupResult;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAllProjectPermissionsForGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAllProjectPermissionsForGroupResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListDomainPermissionsForGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListDomainPermissionsForGroupResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListGroupsForUserRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListGroupsForUserResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListPermissionsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListProjectPermissionsForGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListProjectPermissionsForGroupResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneShowGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneShowGroupResponse;
import com.huaweicloud.sdk.iam.v3.model.ProjectResult;
import com.huaweicloud.sdk.iam.v3.model.RoleResult;
import com.huaweicloud.sdk.iam.v3.model.UpdateDomainGroupInheritRoleRequest;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.HuaweiCloudResponse;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.config.HuaweiCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.handler.AbstractCloudPermissionHandler;
import com.tt.cloud.util.Utils;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/20 18:22
 */
@Slf4j
@Component
public class HuaweiCloudPermissionHandler extends AbstractCloudPermissionHandler {

    @Resource
    private HuaweiCloudHttpConfig huaweiCloudHttpConfig;
    @Resource
    private HuaweiCloudProjectHandler huaweiCloudProjectHandler;
    @Resource
    private CloudAccountDao cloudAccountDao;

    public boolean supports(String cloudType) {
        return Constants.HUAWEI_CLOUD.equals(cloudType);
    }

    /**
     * 查询用户被授予的所有权限
     *
     * @param permissionEntity permissionEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse queryUserPermissions(PermissionEntity permissionEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
            IamClient iamClient = huaweiCloudHttpConfig.getHuaweiIamClient(
                    permissionEntity.getAccountId());
            log.info("查询用户被授予的所有权限，通过用户查询分组，再根据分组查询权限");
            KeystoneListGroupsForUserRequest request = new KeystoneListGroupsForUserRequest();
            request.setUserId(permissionEntity.getUserId());
            KeystoneListGroupsForUserResponse listGroupsForUserResponse =
                    iamClient.keystoneListGroupsForUser(request);
            HuaweiCloudResponse cloudResponse = huaweiCloudProjectHandler.queryProjectList(
                    permissionEntity.getAccountId());

            List<KeystoneGroupResult> groupResultList = listGroupsForUserResponse.getGroups();
            List<PermissionEntity> permissionList = new ArrayList<>();
            if (null != groupResultList && !groupResultList.isEmpty()) {
                for (KeystoneGroupResult groupResult : groupResultList) {
                    KeystoneListAllProjectPermissionsForGroupRequest permissionsForGroupRequest
                            = new KeystoneListAllProjectPermissionsForGroupRequest();
                    permissionsForGroupRequest.setGroupId(groupResult.getId());
                    permissionsForGroupRequest.setDomainId(cloudAccount.getDomainId());
                    KeystoneListAllProjectPermissionsForGroupResponse response =
                            iamClient.keystoneListAllProjectPermissionsForGroup(
                                            permissionsForGroupRequest);
                    getPermissionList(permissionList, groupResult, response.getRoles(),
                            "所有资源 [包含未来新增项目]");

                    KeystoneListDomainPermissionsForGroupRequest
                            listDomainPermissionsForGroupRequest =
                            new KeystoneListDomainPermissionsForGroupRequest();
                    listDomainPermissionsForGroupRequest.setGroupId(groupResult.getId());
                    listDomainPermissionsForGroupRequest.setDomainId(cloudAccount.getDomainId());
                    KeystoneListDomainPermissionsForGroupResponse listDomainPermissionsForGroupResponse =
                            iamClient.keystoneListDomainPermissionsForGroup(
                                    listDomainPermissionsForGroupRequest);
                    getPermissionList(permissionList, groupResult,
                            listDomainPermissionsForGroupResponse.getRoles(), "全局服务 [全局]");

                    for (ProjectResult projectResult : cloudResponse.getProjectResultList()) {
                        KeystoneListProjectPermissionsForGroupRequest keystoneListProjectPermissionsForGroupRequest = new KeystoneListProjectPermissionsForGroupRequest();
                        keystoneListProjectPermissionsForGroupRequest.setGroupId(
                                groupResult.getId());
                        keystoneListProjectPermissionsForGroupRequest.setProjectId(
                                projectResult.getId());
                        KeystoneListProjectPermissionsForGroupResponse keystoneListProjectPermissionsForGroupResponse =
                                iamClient.keystoneListProjectPermissionsForGroup(
                                                keystoneListProjectPermissionsForGroupRequest);
                        for (RoleResult role : keystoneListProjectPermissionsForGroupResponse.getRoles()) {
                            fillPermissionList(permissionList, role, projectResult.getName());
                        }
                    }
                }
            }
            log.info("查询用户权限列表完毕共：{}", permissionList.size());
            permissionList.sort(Comparator.comparing(PermissionEntity::getDisplay_name));
            huaweiCloudResponse.setPermissionList(permissionList);
        } catch (Exception e) {
            log.error("查询用户权限列表异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "查询用户权限列表异常");
        }
        return huaweiCloudResponse;
    }

    private void getPermissionList(List<PermissionEntity> permissionList,
            KeystoneGroupResult groupResult,
            List<RoleResult> roles, String projectName) {
        if (null != roles && !roles.isEmpty()) {
            for (RoleResult role : roles) {
                PermissionEntity permissionEntity = new PermissionEntity();
                permissionEntity.setId(role.getId());
                permissionEntity.setName(role.getName());
                permissionEntity.setDisplay_name(role.getDisplayName());
                permissionEntity.setDescription(groupResult.getDescription());
                permissionEntity.setCatalog(role.getCatalog());
                permissionEntity.setType(role.getType());
                permissionEntity.setDescription_cn(role.getDescriptionCn());
                permissionEntity.setBelongId(groupResult.getId());
                permissionEntity.setBelongName(groupResult.getName());
                permissionEntity.setBelongType(Constants.BELONG_TYPE_GROUP);
                permissionEntity.setBelongDescription(groupResult.getDescription());
                permissionEntity.setProjectName(projectName);
                permissionList.add(permissionEntity);
            }
        }
    }

    /**
     * 查询权限列表
     *
     * @param permissionEntity permissionEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse queryPermissionList(PermissionEntity permissionEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            log.info("查询权限列表：{}", permissionEntity);
            if (Strings.isEmpty(permissionEntity.getPermissionType())
                    || !"custom".equals(permissionEntity.getPermissionType())) {
                log.info("全部权限列表，系統权限、系统角色、自定义权限");
                List<RoleResult> systemRoleList = querySystemOrRolePermissionList(permissionEntity);
                systemRoleList.addAll(queryCustomPermissionList(permissionEntity));
                systemRoleList = systemRoleList.stream()
                        .filter(item -> StringUtils.isNotEmpty(item.getDisplayName())
                                && !"XX".equals(item.getType())).collect(
                                Collectors.toList());
                systemRoleList.sort(Comparator.comparing(RoleResult::getDisplayName));
                huaweiCloudResponse.setRoleResultList(systemRoleList);
            } else {
                log.info("查询自定义权限");
                List<RoleResult> customRoleList = queryCustomPermissionList(permissionEntity);
                customRoleList = customRoleList.stream()
                        .filter(item -> StringUtils.isNotEmpty(item.getDisplayName())
                                && !"XX".equals(item.getType())).collect(
                                Collectors.toList());
                customRoleList.sort(Comparator.comparing(RoleResult::getDisplayName));
                huaweiCloudResponse.setRoleResultList(customRoleList);
            }
        } catch (Exception e) {
            log.error("查询权限列表异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "查询权限列表异常");
        }
        return huaweiCloudResponse;
    }

    public List<RoleResult> querySystemOrRolePermissionList(PermissionEntity permissionEntity) {
        KeystoneListPermissionsRequest keystoneListPermissionsRequest =
                new KeystoneListPermissionsRequest();
        keystoneListPermissionsRequest.setCatalog(permissionEntity.getCatalog());
        if (Strings.isNotEmpty(permissionEntity.getPermissionType())) {
            keystoneListPermissionsRequest.setPermissionType(permissionEntity.getPermissionType());
        }
        if (Strings.isNotEmpty(permissionEntity.getName())) {
            keystoneListPermissionsRequest.setDisplayName(permissionEntity.getName());
        }
        return huaweiCloudHttpConfig.getHuaweiIamClient(permissionEntity.getAccountId())
                .keystoneListPermissions(keystoneListPermissionsRequest).getRoles();
    }

    public List<RoleResult> queryCustomPermissionList(PermissionEntity permissionEntity) {
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
        IamClient iamClient = huaweiCloudHttpConfig.getHuaweiIamClient(
                permissionEntity.getAccountId());
        KeystoneListPermissionsRequest keystoneListPermissionsRequest =
                new KeystoneListPermissionsRequest();
        keystoneListPermissionsRequest.setCatalog(permissionEntity.getCatalog());
        keystoneListPermissionsRequest.setDomainId(cloudAccount.getDomainId());
        if (Strings.isNotEmpty(permissionEntity.getName())) {
            keystoneListPermissionsRequest.setDisplayName(permissionEntity.getName());
        }
        return iamClient.keystoneListPermissions(keystoneListPermissionsRequest).getRoles();
    }


    /**
     * 给用户组授权
     *
     * @param permissionEntity permissionEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse grantGroupPermissions(PermissionEntity permissionEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        log.info("华为云用户组授权：{}", permissionEntity);
        try {
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
            IamClient iamClient = huaweiCloudHttpConfig.getHuaweiIamClient(
                    permissionEntity.getAccountId());
            if (null != permissionEntity.getAllPermissionIdList()
                    && !permissionEntity.getAllPermissionIdList().isEmpty()) {
                log.info("华为云为用户组授予所有项目服务权限:{}", permissionEntity.getAllPermissionIdList().size());
                for (String roleId : permissionEntity.getAllPermissionIdList()) {
                    UpdateDomainGroupInheritRoleRequest updateDomainGroupInheritRoleRequest = new UpdateDomainGroupInheritRoleRequest();
                    updateDomainGroupInheritRoleRequest.setDomainId(cloudAccount.getDomainId());
                    updateDomainGroupInheritRoleRequest.setGroupId(permissionEntity.getGroupId());
                    updateDomainGroupInheritRoleRequest.setRoleId(roleId);
                    iamClient.updateDomainGroupInheritRole(updateDomainGroupInheritRoleRequest);
                }
                log.info("华为云为用户组授予所有项目服务权限完毕");
            }
            if (null != permissionEntity.getGlobalPermissionIdList()
                    && !permissionEntity.getGlobalPermissionIdList().isEmpty()) {
                log.info("华为云为用户组授予全局权限:{}", permissionEntity.getGlobalPermissionIdList().size());
                for (String roleId : permissionEntity.getGlobalPermissionIdList()) {
                    KeystoneAssociateGroupWithDomainPermissionRequest associateGroupWithDomainPermissionRequest =
                            new KeystoneAssociateGroupWithDomainPermissionRequest();
                    associateGroupWithDomainPermissionRequest.setDomainId(
                            cloudAccount.getDomainId());
                    associateGroupWithDomainPermissionRequest.setGroupId(
                            permissionEntity.getGroupId());
                    associateGroupWithDomainPermissionRequest.setRoleId(roleId);
                    iamClient.keystoneAssociateGroupWithDomainPermission(
                                    associateGroupWithDomainPermissionRequest);
                }
                log.info("华为云为用户组授予全局权限完毕");
            }
            if (null != permissionEntity.getProjectPermissionIdList()
                    && !permissionEntity.getProjectPermissionIdList().isEmpty()) {
                log.info("华为云为用户组授予项目权限");
                for (String projectId : permissionEntity.getProjectIdList()) {
                    for (String roleId : permissionEntity.getProjectPermissionIdList()) {
                        KeystoneAssociateGroupWithProjectPermissionRequest keystoneAssociateGroupWithProjectPermissionRequest = new KeystoneAssociateGroupWithProjectPermissionRequest();
                        keystoneAssociateGroupWithProjectPermissionRequest.setGroupId(
                                permissionEntity.getGroupId());
                        keystoneAssociateGroupWithProjectPermissionRequest.setProjectId(projectId);
                        keystoneAssociateGroupWithProjectPermissionRequest.setRoleId(roleId);
                        iamClient.keystoneAssociateGroupWithProjectPermission(
                                        keystoneAssociateGroupWithProjectPermissionRequest);
                    }
                }
                log.info("华为云为用户组授予项目权限完毕");
            }
        } catch (Exception e) {
            log.error("华为云用户组授权异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "华为云用户组授权异常");
        }
        return huaweiCloudResponse;
    }

    public HuaweiCloudResponse queryGroupPermissionList(PermissionEntity permissionEntity) {
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
        IamClient iamClient = huaweiCloudHttpConfig.getHuaweiIamClient(
                permissionEntity.getAccountId());
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        List<PermissionEntity> permissionList = new ArrayList<>();
        try {
            KeystoneShowGroupRequest keystoneShowGroupRequest = new KeystoneShowGroupRequest();
            keystoneShowGroupRequest.setGroupId(permissionEntity.getGroupId());
            KeystoneShowGroupResponse keystoneShowGroupResponse =
                    iamClient.keystoneShowGroup(keystoneShowGroupRequest);
            KeystoneListAllProjectPermissionsForGroupRequest permissionsForGroupRequest =
                    new KeystoneListAllProjectPermissionsForGroupRequest();
            permissionsForGroupRequest.setDomainId(cloudAccount.getDomainId());
            permissionsForGroupRequest.setGroupId(permissionEntity.getGroupId());
            KeystoneListAllProjectPermissionsForGroupResponse keystoneListAllProjectPermissionsForGroupResponse =
                    iamClient.keystoneListAllProjectPermissionsForGroup(permissionsForGroupRequest);
            log.info("华为云查询用户组包含的所有项目权限");
            for (RoleResult role : keystoneListAllProjectPermissionsForGroupResponse.getRoles()) {
                fillPermissionList(permissionList, role, "所有资源 [包含未来新增项目]");
            }
            log.info("华为云遍历项目列表查询每个项目包含的权限");
            HuaweiCloudResponse cloudResponse = huaweiCloudProjectHandler.queryProjectList(
                    permissionEntity.getAccountId());
            for (ProjectResult projectResult : cloudResponse.getProjectResultList()) {
                KeystoneListProjectPermissionsForGroupRequest keystoneListProjectPermissionsForGroupRequest =
                        new KeystoneListProjectPermissionsForGroupRequest();
                keystoneListProjectPermissionsForGroupRequest.setGroupId(
                        permissionEntity.getGroupId());
                keystoneListProjectPermissionsForGroupRequest.setProjectId(projectResult.getId());
                KeystoneListProjectPermissionsForGroupResponse keystoneListProjectPermissionsForGroupResponse =
                        iamClient.keystoneListProjectPermissionsForGroup(
                                        keystoneListProjectPermissionsForGroupRequest);
                for (RoleResult role : keystoneListProjectPermissionsForGroupResponse.getRoles()) {
                    fillPermissionList(permissionList, role, projectResult.getName());
                }
            }
            KeystoneListDomainPermissionsForGroupRequest
                    listDomainPermissionsForGroupRequest =
                    new KeystoneListDomainPermissionsForGroupRequest();
            listDomainPermissionsForGroupRequest.setGroupId(permissionEntity.getGroupId());
            listDomainPermissionsForGroupRequest.setDomainId(cloudAccount.getDomainId());
            KeystoneListDomainPermissionsForGroupResponse listDomainPermissionsForGroupResponse =
                    iamClient.keystoneListDomainPermissionsForGroup(
                                    listDomainPermissionsForGroupRequest);
            for (RoleResult role : listDomainPermissionsForGroupResponse.getRoles()) {
                fillPermissionList(permissionList, role, "全局服务 [全局]");
            }
            for (PermissionEntity permission : permissionList) {
                permission.setBelongId(keystoneShowGroupResponse.getGroup().getId());
                permission.setBelongName(keystoneShowGroupResponse.getGroup().getName());
                permission.setBelongDescription(
                        keystoneShowGroupResponse.getGroup().getDescription());
                permission.setBelongType(Constants.BELONG_TYPE_GROUP);
            }
            permissionList.sort(Comparator.comparing(PermissionEntity::getDisplay_name));
            huaweiCloudResponse.setPermissionList(permissionList);
        } catch (Exception e) {
            log.error("华为云查询用户组包含权限异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "华为云查询用户组包含权限异常");
        }
        return huaweiCloudResponse;
    }

    private void fillPermissionList(List<PermissionEntity> permissionList, RoleResult role,
            String projectName) {
        PermissionEntity permission = new PermissionEntity();
        permission.setId(role.getId());
        permission.setName(role.getName());
        permission.setDisplay_name(role.getDisplayName());
        permission.setDescription(role.getDescription());
        permission.setDescription_cn(role.getDescriptionCn());
        permission.setPolicy(role.getPolicy());
        permission.setProjectName(projectName);
        permissionList.add(permission);
    }
}
