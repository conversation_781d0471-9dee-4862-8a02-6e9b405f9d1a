package com.tt.cloud.handler.huawei;

import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.KeystoneAddUserToGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneCreateGroupOption;
import com.huaweicloud.sdk.iam.v3.model.KeystoneCreateGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneCreateGroupRequestBody;
import com.huaweicloud.sdk.iam.v3.model.KeystoneDeleteGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneGroupResult;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListGroupsForUserRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListGroupsForUserResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListGroupsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListGroupsResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListUsersForGroupByAdminRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListUsersForGroupByAdminResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneRemoveUserFromGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneShowGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneShowGroupResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneUpdateGroupOption;
import com.huaweicloud.sdk.iam.v3.model.KeystoneUpdateGroupRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneUpdateGroupRequestBody;
import com.huaweicloud.sdk.iam.v3.model.KeystoneUserResult;
import com.huaweicloud.sdk.iam.v3.model.ShowUserRequest;
import com.huaweicloud.sdk.iam.v3.model.ShowUserResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.GroupEntity;
import com.tt.cloud.bean.HuaweiCloudResponse;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.UserGroupInfo;
import com.tt.cloud.config.HuaweiCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.Utils;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

/**
 * 华为云用户组管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 14:58
 */
@Slf4j
@Component
public class HuaweiCloudUserGroupHandler {
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat(Constants.DATA_FORMAT);
    @Resource
    private HuaweiCloudHttpConfig huaweiCloudHttpConfig;
    @Resource
    private CloudAccountDao cloudAccountDao;

    /**
     * 查询用户组详情
     *
     * @param groupEntity groupEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse keystoneShowGroup(GroupEntity groupEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            log.info("查询华为云用户组详情");
            KeystoneShowGroupResponse response = huaweiCloudHttpConfig.getHuaweiIamClient(groupEntity.getAccountId())
                    .keystoneShowGroup(
                            new KeystoneShowGroupRequest().withGroupId(groupEntity.getGroupId()));
            GroupEntity groupInfo = new GroupEntity();
            if (null != response.getGroup()) {
                groupInfo.setGroupId(response.getGroup().getId());
                groupInfo.setGroupName(response.getGroup().getName());
                groupInfo.setMemo(response.getGroup().getDescription());
                Date createTime = getCreateTime(response.getGroup());
                groupInfo.setCreateTime(SIMPLE_DATE_FORMAT.format(createTime));
            }
            huaweiCloudResponse.setGroupEntity(groupInfo);
            log.info("查询华为云用户组成功");
        } catch (Exception e) {
            log.info("查询华为云用户组详情异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "查询华为云用户组详情异常");
        }
        return huaweiCloudResponse;
    }

    private Date getCreateTime(KeystoneGroupResult group) {
        Date createTime = new Date();
        createTime.setTime(group.getCreateTime());
        return createTime;
    }

    /**
     * 查询用户组列表
     *
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse keystoneListGroups(GroupEntity groupEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        List<GroupEntity> groupList = new ArrayList<>();
        try {
            KeystoneListGroupsRequest request = new KeystoneListGroupsRequest();
            request.setName(groupEntity.getGroupName());
            KeystoneListGroupsResponse response =
                    huaweiCloudHttpConfig.getHuaweiIamClient(groupEntity.getAccountId())
                            .keystoneListGroups(request);
            groupList = getGroupList(response.getGroups());
        } catch (Exception e) {
            log.info("查询用户组列表异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "查询用户组列表异常");
        }
        huaweiCloudResponse.setGroupList(groupList);
        return huaweiCloudResponse;
    }

    private List<GroupEntity> getGroupList(List<KeystoneGroupResult> groups) {
        List<GroupEntity> groupList = new ArrayList<>();
        if (null != groups && !groups.isEmpty()) {
            for (KeystoneGroupResult keystoneGroupResult : groups) {
                GroupEntity groupInfo = new GroupEntity();
                groupInfo.setGroupId(keystoneGroupResult.getId());
                groupInfo.setGroupName(keystoneGroupResult.getName());
                groupInfo.setMemo(keystoneGroupResult.getDescription());
                Date createTime = getCreateTime(keystoneGroupResult);
                groupInfo.setCreateTime(SIMPLE_DATE_FORMAT.format(createTime));
                groupList.add(groupInfo);
            }
        }
        return groupList;
    }

    /**
     * 删除用户组
     *
     * @param groupEntity groupEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse keystoneDeleteGroup(GroupEntity groupEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            huaweiCloudHttpConfig.getHuaweiIamClient(groupEntity.getAccountId())
                    .keystoneDeleteGroup(
                            new KeystoneDeleteGroupRequest().withGroupId(
                                    groupEntity.getGroupId()));
        } catch (Exception e) {
            log.info("华为云删除用户组异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "更新用户组异常");
        }
        return huaweiCloudResponse;
    }

    /**
     * 创建用户组
     *
     * @param groupEntity groupEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse keystoneCreateGroup(GroupEntity groupEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            huaweiCloudHttpConfig.getHuaweiIamClient(groupEntity.getAccountId())
                    .keystoneCreateGroup(new KeystoneCreateGroupRequest().withBody(
                            new KeystoneCreateGroupRequestBody().withGroup(
                                    new KeystoneCreateGroupOption().withName(
                                                    groupEntity.getGroupName())
                                            .withDescription(groupEntity.getMemo()))));
        } catch (Exception e) {
            log.info("华为云创建用户组异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "更新用户组异常");
        }
        return huaweiCloudResponse;
    }

    /**
     * 更新用户组
     *
     * @param groupEntity groupEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse keystoneUpdateGroup(GroupEntity groupEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            log.info("更新华为云用户组");
            huaweiCloudHttpConfig.getHuaweiIamClient(groupEntity.getAccountId())
                    .keystoneUpdateGroup(new KeystoneUpdateGroupRequest()
                            .withGroupId(groupEntity.getGroupId())
                            .withBody(new KeystoneUpdateGroupRequestBody().withGroup(
                                    new KeystoneUpdateGroupOption().withName(
                                                    groupEntity.getGroupName())
                                            .withDescription(groupEntity.getMemo()))));
            log.info("更新华为云用户组成功");
        } catch (Exception e) {
            log.info("更新用户组异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "更新用户组异常");
        }
        return huaweiCloudResponse;
    }

    /**
     * 添加IAM用户到用户组
     *
     * @param groupEntity groupEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse keystoneAddUserToGroup(GroupEntity groupEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            log.info("华为云添加IAM用户到用户组:{}", groupEntity);
            if (null != groupEntity.getUserId()) {
                for (String groupId : groupEntity.getGroupIdList()) {
                    huaweiCloudHttpConfig.getHuaweiIamClient(groupEntity.getAccountId()).keystoneAddUserToGroup(
                            new KeystoneAddUserToGroupRequest()
                                    .withGroupId(groupId)
                                    .withUserId(groupEntity.getUserId()));
                }
            }
            if (null != groupEntity.getGroupId()) {
                for (String userId : groupEntity.getUserIdList()) {
                    huaweiCloudHttpConfig.getHuaweiIamClient(groupEntity.getAccountId()).keystoneAddUserToGroup(
                            new KeystoneAddUserToGroupRequest()
                                    .withGroupId(groupEntity.getGroupId())
                                    .withUserId(userId));
                }
            }
            log.info("华为云添加IAM用户到用户组成功");
        } catch (Exception e) {
            log.error("华为云添加IAM用户到用户组异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "华为云添加IAM用户到用户组异常");
        }
        return huaweiCloudResponse;
    }

    /**
     * 移除用户组中的IAM用户
     *
     * @param groupEntity groupEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse keystoneRemoveUserFromGroup(GroupEntity groupEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            log.info("华为云移除用户组中的IAM用户");
            if (null != groupEntity.getGroupIdList() && !groupEntity.getGroupIdList().isEmpty()) {
                for (String groupId : groupEntity.getGroupIdList()) {
                    for (String userId : groupEntity.getUserIdList()) {
                        huaweiCloudHttpConfig.getHuaweiIamClient(groupEntity.getAccountId())
                                .keystoneRemoveUserFromGroup(
                                new KeystoneRemoveUserFromGroupRequest().withGroupId(groupId)
                                        .withUserId(userId));
                    }
                }
            }
            log.info("华为云移除用户组中的IAM用户成功");
        } catch (Exception e) {
            log.error("华为云移除用户组中的IAM用户异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "华为云移除用户组中的IAM用户异常");
        }
        return huaweiCloudResponse;
    }

    /**
     * 查询用户组下的用户
     *
     * @param userEntity userEntity
     * @return HuaweiCloudResponse
     */
    public HuaweiCloudResponse keystoneListUsersForGroupByAdmin(UserEntity userEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            log.info("查询华为云用户组下的用户:{}", userEntity.getGroupId());
            KeystoneListUsersForGroupByAdminRequest request = new KeystoneListUsersForGroupByAdminRequest();
            request.setGroupId(userEntity.getGroupId());
            KeystoneListUsersForGroupByAdminResponse response =
                    huaweiCloudHttpConfig.getHuaweiIamClient(userEntity.getAccountId())
                            .keystoneListUsersForGroupByAdmin(request);
            List<KeystoneUserResult> list = response.getUsers();
            List<UserEntity> userList = new ArrayList<>();
            if (null != list && !list.isEmpty()) {
                for (KeystoneUserResult keystoneUserResult : list) {
                    ShowUserRequest showUserRequest = new ShowUserRequest();
                    showUserRequest.setUserId(keystoneUserResult.getId());
                    ShowUserResponse showUserResponse =
                            huaweiCloudHttpConfig.getHuaweiIamClient(userEntity.getAccountId())
                                    .showUser(showUserRequest);
                    UserEntity userInfo = new UserEntity();
                    userInfo.setUserId(keystoneUserResult.getId());
                    userInfo.setUserName(keystoneUserResult.getName());
                    userInfo.setRemark(showUserResponse.getUser().getDescription());
                    userInfo.setIsDomainOwner(showUserResponse.getUser().getIsDomainOwner());
                    userList.add(userInfo);
                }
            }
            // 根据名称过滤用户列表
            if (Strings.isNotEmpty(userEntity.getUserName())) {
                userList = userList.stream()
                        .filter(item -> item.getUserName().toLowerCase(Locale.ROOT)
                                .contains(userEntity.getUserName().toLowerCase(Locale.ROOT)))
                        .collect(Collectors.toList());
            }
            huaweiCloudResponse.setUserList(userList);
            log.info("查询华为云用户组下的用户:{}", userList.size());
        } catch (Exception e) {
            log.error("查询华为云用户组下的用户异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "查询华为云用户组下的用户异常");
        }
        return huaweiCloudResponse;
    }

    /**
     * 查询用户关联的用户组
     *
     * @param groupEntity groupEntity
     * @return List
     */
    public HuaweiCloudResponse keystoneListGroupsForUser(GroupEntity groupEntity) {
        HuaweiCloudResponse huaweiCloudResponse = new HuaweiCloudResponse();
        try {
            log.info("查询华为云用户关联的用户组");
            KeystoneListGroupsForUserRequest request = new KeystoneListGroupsForUserRequest();
            
            request.setUserId(groupEntity.getUserId());
            KeystoneListGroupsForUserResponse response =
                    huaweiCloudHttpConfig.getHuaweiIamClient(groupEntity.getAccountId())
                            .keystoneListGroupsForUser(request);
            List<GroupEntity> groupList = getGroupList(response.getGroups());
            log.info("查询华为云用户关联的用户组成功：{}", groupList.size());
            huaweiCloudResponse.setGroupList(groupList);
        } catch (Exception e) {
            log.error("查询华为云用户关联的用户组异常", e);
            Utils.setHuaweiClodResponseErrorMsg(huaweiCloudResponse, e, "查询华为云用户关联的用户组异常");
        }
        return huaweiCloudResponse;
    }

    /**
     * 查詢用戶組列表
     *
     * @return List
     */
    public List<UserGroupInfo> queryAllGroupList() {
        List<UserGroupInfo> groupList = new ArrayList<>();
        List<KeystoneGroupResult> groups = new ArrayList<>();

        List<CloudAccountEntity> accountList = cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
        for (CloudAccountEntity cloudAccount : accountList) {
            if (Constants.HUAWEI_CLOUD.equals(cloudAccount.getCloudType())) {
                IamClient iamClient = huaweiCloudHttpConfig.getHuaweiIamClient(cloudAccount.getId());
                KeystoneListGroupsResponse response =
                        iamClient.keystoneListGroups(new KeystoneListGroupsRequest());
                if (null != response.getGroups() && !response.getGroups().isEmpty()) {
                    if (groups.isEmpty()) {
                        groups.addAll(response.getGroups());
                    } else {
                        groups = groups.stream().filter(item -> response.getGroups().stream()
                                        .anyMatch(item1 -> item.getName().equals(item1.getName())))
                                .collect(Collectors.toList());
                    }
                }
            }
        }
        for (KeystoneGroupResult keystoneGroupResult : groups) {
            UserGroupInfo userGroupInfo = new UserGroupInfo();
            userGroupInfo.setGroupId(keystoneGroupResult.getName());
            userGroupInfo.setGroupName(keystoneGroupResult.getName());
            groupList.add(userGroupInfo);
        }
        return groupList;
    }
}
