package com.tt.cloud.bean;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2021/12/22 14:15
 */
@Getter
@Setter
@ToString
public class BusinessOperationRequest {
    private String operateId;
    private String operateType;
    private String businessName;
    private String operateDesc;
    private String requestParam;
    private String responseParam;
    private String requestUrl;
    private String requestMethod;
    private String classMethod;
    private String userId;
    private String operateState;
    private Date operateTime;

    private String startTime;
    private String endTime;

    private int startNum = 0;
    private int pageSize = 10;

    private BusinessOperationLog businessOperationLog;
}
