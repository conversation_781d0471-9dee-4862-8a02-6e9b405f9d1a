package com.tt.cloud.bean;

import java.time.Instant;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.services.elasticloadbalancingv2.model.AvailabilityZone;
import software.amazon.awssdk.services.elasticloadbalancingv2.model.LoadBalancerState;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/8/10 18:12
 */
@Getter
@Setter
@ToString
public class AwsLoadBalancer {
    private String loadBalancerArn;
    private String dnsName;
    private String canonicalHostedZoneId;
    private Instant createdTime;
    private String loadBalancerName;
    private String scheme;
    private String vpcId;
    private String state;
    private String type;
    private List<AwsAvailabilityZone> availabilityZones;
    private List<String> securityGroups;
    private String ipAddressType;
    private String customerOwnedIpv4Pool;
}
