package com.tt.cloud.config;

import com.aliyun.ram20150501.Client;
import com.aliyun.teaopenapi.models.Config;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.CacheUtil;
import com.tt.cloud.util.EncryptUtils;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/19 9:46
 */
@Slf4j
@Component
public class AliCloudHttpConfig {
    @Resource
    private CloudAccountDao cloudAccountDao;

    public Client getAliIamClient(String accountId) {
        Client client = null;
        try {
            String cacheKey = "Ali_IamClient_" + accountId;
            Object object = CacheUtil.get(cacheKey);
            if (null != object) {
                return (Client) object;
            }
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(accountId);
            Config config = new Config()
                    .setAccessKeyId(cloudAccount.getSecretId())
                    .setAccessKeySecret(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
            config.endpoint = cloudAccount.getEndpoint();
            client = new Client(config);
            CacheUtil.put(cacheKey, client, 24 * 60 * 60);
        } catch (Exception e) {
            log.error("初始化阿里IAMClient异常", e);
        }
        return client;
    }

    public com.aliyun.slb20140515.Client getSLBClient(String accountId, String region) {
        com.aliyun.slb20140515.Client client = null;
        try {
            if (StringUtils.isEmpty(region)) {
                region = "cn-beijing";
            }
            String cacheKey = "Ali_SLBClient_" + accountId + "_" + region;
            Object object = CacheUtil.get(cacheKey);
            if (null != object) {
                return (com.aliyun.slb20140515.Client) object;
            }
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(accountId);
            Config config = new Config()
                    .setAccessKeyId(cloudAccount.getSecretId())
                    .setAccessKeySecret(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
            config.endpoint = "slb." + region + ".aliyuncs.com";
            client = new com.aliyun.slb20140515.Client(config);
            CacheUtil.put(cacheKey, client, 24 * 60 * 60);
        } catch (Exception e) {
            log.error("初始化阿里SLBClient异常", e);
        }
        return client;
    }

    public com.aliyun.alb20200616.Client getALBClient(String accountId, String region) {
        com.aliyun.alb20200616.Client client = null;
        try {
            if (StringUtils.isEmpty(region)) {
                region = "cn-beijing";
            }
            String cacheKey = "Ali_ALBClient_" + accountId + "_" + region;
            Object object = CacheUtil.get(cacheKey);
            if (null != object) {
                return (com.aliyun.alb20200616.Client) object;
            }
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(accountId);
            Config config = new Config()
                    .setAccessKeyId(cloudAccount.getSecretId())
                    .setAccessKeySecret(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
            config.endpoint = "alb." + region + ".aliyuncs.com";
            client = new com.aliyun.alb20200616.Client(config);
            CacheUtil.put(cacheKey, client, 24 * 60 * 60);
        } catch (Exception e) {
            log.error("初始化阿里ALBClient异常", e);
        }
        return client;
    }

    public com.aliyun.nlb20220430.Client getNLBClient(String accountId, String region) {
        com.aliyun.nlb20220430.Client client = null;
        try {
            if (StringUtils.isEmpty(region)) {
                region = "cn-beijing";
            }
            String cacheKey = "Ali_NLBClient_" + accountId + "_" + region;
            Object object = CacheUtil.get(cacheKey);
            if (null != object) {
                return (com.aliyun.nlb20220430.Client) object;
            }
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(accountId);
            Config config = new Config()
                    .setAccessKeyId(cloudAccount.getSecretId())
                    .setAccessKeySecret(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
            config.endpoint = "nlb." + region + ".aliyuncs.com";
            client = new com.aliyun.nlb20220430.Client(config);
            CacheUtil.put(cacheKey, client, 24 * 60 * 60);
        } catch (Exception e) {
            log.error("初始化阿里NLBClient异常", e);
        }
        return client;
    }

    public com.aliyun.resourcemanager20200331.Client getResourceClient(String accountId) {
        com.aliyun.resourcemanager20200331.Client client = null;
        try {
            String cacheKey = "Ali_ResourceClient_" + accountId;
            Object object = CacheUtil.get(cacheKey);
            if (null != object) {
                return (com.aliyun.resourcemanager20200331.Client) object;
            }
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(accountId);
            Config config = new Config()
                    .setAccessKeyId(cloudAccount.getSecretId())
                    .setAccessKeySecret(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
            config.endpoint = "resourcemanager.aliyuncs.com";
            client = new com.aliyun.resourcemanager20200331.Client(config);
            CacheUtil.put(cacheKey, client, 24 * 60 * 60);
        } catch (Exception e) {
            log.error("初始化阿里ResourceClient异常", e);
        }
        return client;
    }

    public com.aliyun.vpc20160428.Client getVPCClient(String accountId, String region) {
        com.aliyun.vpc20160428.Client client = null;
        try {
            String cacheKey = "Ali_VPCClient_" + accountId + "_" + region;
            Object object = CacheUtil.get(cacheKey);
            if (null != object) {
                return (com.aliyun.vpc20160428.Client) object;
            }
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(accountId);
            Config config = new Config()
                    .setAccessKeyId(cloudAccount.getSecretId())
                    .setAccessKeySecret(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
            config.endpoint = "vpc." + region + ".aliyuncs.com";
            client = new com.aliyun.vpc20160428.Client(config);
            CacheUtil.put(cacheKey, client, 24 * 60 * 60);
        } catch (Exception e) {
            log.error("初始化阿里VPCClient异常", e);
        }
        return client;
    }

    public com.aliyun.cs20151215.Client getCSClient(String accountId, String region) {
        com.aliyun.cs20151215.Client client = null;
        try {
            String cacheKey = "Ali_CSClient_" + accountId + "_" + region;
            Object object = CacheUtil.get(cacheKey);
            if (null != object) {
                return (com.aliyun.cs20151215.Client) object;
            }
            CloudAccountEntity cloudAccount
                    = cloudAccountDao.queryCloudAccountInfoById(accountId);
            Config config = new Config()
                    .setAccessKeyId(cloudAccount.getSecretId())
                    .setAccessKeySecret(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
            config.endpoint = "cs." + region + ".aliyuncs.com";
            client = new com.aliyun.cs20151215.Client(config);
            CacheUtil.put(cacheKey, client, 24 * 60 * 60);
        } catch (Exception e) {
            log.error("初始化阿里CSClient异常", e);
        }
        return client;
    }

}
