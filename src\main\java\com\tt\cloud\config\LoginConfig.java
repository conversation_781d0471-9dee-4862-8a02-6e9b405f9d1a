package com.tt.cloud.config;

import com.tt.cloud.interceptor.LoginInterceptor;
import com.tt.cloud.service.UserServiceImpl;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class LoginConfig implements WebMvcConfigurer {
  @Value("${sso.public_domain}")
  private String logOutUrl;
  @Value("${sso.client_secret}")
  private String clientSecret;
  @Resource
  private UserServiceImpl userService;

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    InterceptorRegistration registration =
            registry.addInterceptor(
                    new LoginInterceptor(logOutUrl, clientSecret, userService));
    registration.addPathPatterns("/**");
    registration.excludePathPatterns(
            "/rest/v1/oauth/access_token",
            "/rest/v1/verify/token",
            "/rest/v1/SAML/Consume",
            "/auth/sso-ticket-auth/",
            "/rest/v1/message",
            "/rest/v1/callback",
            "/rest/v1/idp/azure",
            "/**/*.html",
            "/**/*.js",
            "/**/*.css",
            "/**/*.woff",
            "/**/*.ttf",
            "/tt-op-script/error");
  }
}
