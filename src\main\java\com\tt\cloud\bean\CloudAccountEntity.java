package com.tt.cloud.bean;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/26 15:06
 */
@Getter
@Setter
@ToString
public class CloudAccountEntity {
    private String id;
    private String name;
    private String cloudType;
    private String secretId;
    private String secretKey;
    private String endpoint;
    private Date createTime;
    private String creatorId;
    private String creatorName;
    private String memo;
    private String domainId;
    private String destination;

    private boolean resourceManage;
    private boolean connectStatus;
    private boolean isInternational;
}
