package com.tt.cloud.controller.v2;

import com.aliyun.cs20151215.models.DescribeClustersForRegionRequest;
import com.aliyun.cs20151215.models.DescribeClustersForRegionResponse;
import com.aliyun.cs20151215.models.DescribeClustersForRegionResponseBody;
import com.huaweicloud.sdk.cce.v3.model.Cluster;
import com.huaweicloud.sdk.cce.v3.model.ListClustersRequest;
import com.huaweicloud.sdk.cce.v3.model.ListClustersResponse;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.tke.v20180525.models.DescribeClustersRequest;
import com.tencentcloudapi.tke.v20180525.models.DescribeClustersResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.ClusterEntity;
import com.tt.cloud.bean.CommonRequest;
import com.tt.cloud.bean.v2.CommonListResponse;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.config.HuaweiCloudHttpConfig;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.config.VolcEngineCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.CommonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.volcengine.ApiException;
import com.volcengine.vke.model.ItemForListClustersOutput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/1/18 15:29
 */
@Slf4j
@RestController
@RequestMapping("/rest/v2")
public class CloudClusterV2Controller {
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private HuaweiCloudHttpConfig huaweiCloudHttpConfig;
    @Resource
    private TencentCloudHttpConfig tencentCloudHttpConfig;
    @Resource
    private VolcEngineCloudHttpConfig volcEngineCloudHttpConfig;
    @Resource
    private AliCloudHttpConfig aliCloudHttpConfig;

    @GetMapping("/cluster/list")
    public CommonListResponse queryLBClusterList(CommonRequest commonRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(commonRequest.getCloud_id())
                    || StringUtils.isEmpty(commonRequest.getRegion_id())) {
                return commonListResponse;
            }
            CloudAccountEntity cloudAccountEntity =
                    cloudAccountDao.queryCloudAccountInfoByCloudId(commonRequest.getCloud_id());
            if (null == cloudAccountEntity) {
                return commonListResponse;
            } else if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getHuaweiCloudClusterList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.TENCENT_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getTencentClusterList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getVolcEngineClusterList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getAliCloudClusterList(commonRequest, commonListResponse, cloudAccountEntity);
            }
        } catch (Exception e) {
            log.error("查询云商集群列表异常", e);
            commonListResponse.getHeader().setErrorMsg("查询云商集群列表异常：" + e.getMessage());
        }
        return commonListResponse;
    }

    private void getAliCloudClusterList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                        CloudAccountEntity cloudAccountEntity) throws Exception {
        com.aliyun.cs20151215.Client client = aliCloudHttpConfig.getCSClient(cloudAccountEntity.getId(), commonRequest.getRegion_id());
        DescribeClustersForRegionRequest describeClustersForRegionRequest = new DescribeClustersForRegionRequest();
        describeClustersForRegionRequest.setPageSize(100L);
        DescribeClustersForRegionResponse describeClustersForRegionResponse =
                client.describeClustersForRegion("cn-beijing", describeClustersForRegionRequest);
        List<ClusterEntity> clusterEntityList = new ArrayList<>();
        for (DescribeClustersForRegionResponseBody.DescribeClustersForRegionResponseBodyClusters cluster : describeClustersForRegionResponse.getBody().clusters) {
            ClusterEntity clusterEntity = new ClusterEntity();
            clusterEntity.setCluster_id(cluster.getClusterId());
            clusterEntity.setCluster_name(cluster.getName());
            clusterEntityList.add(clusterEntity);
        }
        filterCluster(commonRequest, commonListResponse, clusterEntityList);
    }

    private void getVolcEngineClusterList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                          CloudAccountEntity cloudAccountEntity) throws ApiException {
        com.volcengine.vke.model.ListClustersResponse listClustersResponse =
                volcEngineCloudHttpConfig.getVolcEngineVkeApi(cloudAccountEntity.getId(), commonRequest.getRegion_id())
                        .listClusters(new com.volcengine.vke.model.ListClustersRequest());
        List<ClusterEntity> clusterEntityList = new ArrayList<>();
        for (ItemForListClustersOutput cluster : listClustersResponse.getItems()) {
            ClusterEntity clusterEntity = new ClusterEntity();
            clusterEntity.setCluster_id(cluster.getId());
            clusterEntity.setCluster_name(cluster.getName());
            clusterEntityList.add(clusterEntity);
        }
        filterCluster(commonRequest, commonListResponse, clusterEntityList);
    }

    private void getTencentClusterList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                       CloudAccountEntity cloudAccountEntity) throws TencentCloudSDKException {
        DescribeClustersResponse describeClustersResponse =
                tencentCloudHttpConfig.getTencentTkeClient(
                                cloudAccountEntity.getId(), commonRequest.getRegion_id())
                        .DescribeClusters(new DescribeClustersRequest());
        List<ClusterEntity> clusterEntityList = new ArrayList<>();
        for (com.tencentcloudapi.tke.v20180525.models.Cluster cluster : describeClustersResponse.getClusters()) {
            ClusterEntity clusterEntity = new ClusterEntity();
            clusterEntity.setCluster_id(cluster.getClusterId());
            clusterEntity.setCluster_name(cluster.getClusterName());
            clusterEntityList.add(clusterEntity);
        }
        filterCluster(commonRequest, commonListResponse, clusterEntityList);
    }

    private void filterCluster(CommonRequest commonRequest, CommonListResponse commonListResponse,
                               List<ClusterEntity> clusterEntityList) {
        if (StringUtils.isNotEmpty(commonRequest.getCluster_name())) {
            clusterEntityList = clusterEntityList.stream()
                    .filter(clusterEntity -> clusterEntity.getCluster_name().toLowerCase(
                                    Locale.ROOT)
                            .contains(commonRequest.getCluster_name().toLowerCase(Locale.ROOT)))
                    .collect(
                            Collectors.toList());
        }
        commonListResponse.setTotal(clusterEntityList.size());
        commonListResponse.setList(CommonUtil.getPage(clusterEntityList, commonRequest.getPageNo(),
                commonRequest.getPageSize()));
    }

    private void getHuaweiCloudClusterList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                           CloudAccountEntity cloudAccountEntity) {
        ListClustersResponse listClustersResponse =
                huaweiCloudHttpConfig.getHuaweiCceClient(cloudAccountEntity.getId(),
                        commonRequest.getRegion_id()).listClusters(new ListClustersRequest());
        List<ClusterEntity> clusterEntityList = new ArrayList<>();
        for (Cluster item : listClustersResponse.getItems()) {
            ClusterEntity clusterEntity = new ClusterEntity();
            clusterEntity.setCluster_id(item.getMetadata().getUid());
            clusterEntity.setCluster_name(item.getMetadata().getName());
            clusterEntityList.add(clusterEntity);
        }
        filterCluster(commonRequest, commonListResponse, clusterEntityList);
    }

}
