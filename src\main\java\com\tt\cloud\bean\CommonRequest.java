package com.tt.cloud.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 15:59
 */
@Getter
@Setter
@ToString
public class CommonRequest {
   private String email;
   private String cloud_id;
   private String region_id;
   private String zone_id;
   private List<String> zone_ids;
   private String slave_zone_id;
   private String vpc_id;
   private String lb_name;
   private String lb_type;
   private String project_name;
   private String region_name;
   private String flavor_type;
   private String vpc_name;
   private String cluster_name;
   private String charge_mode;
   private long pageNo = 1;
   private long pageSize = 10;
}
