package com.tt.cloud.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Redis 工具类
 * 
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2025-01-18
 */
@Slf4j
@Component
public class RedisUtil {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取字符串值
     *
     * @param key 键
     * @return 值
     */
    public String get(String key) {
        try {
            return stringRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("Redis获取数据异常, key: {}", key, e);
            return null;
        }
    }

    /**
     * 设置字符串值
     *
     * @param key   键
     * @param value 值
     * @return 是否成功
     */
    public boolean set(String key, String value) {
        try {
            stringRedisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("Redis设置数据异常, key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 设置字符串值并指定过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 过期时间（秒）
     * @return 是否成功
     */
    public boolean set(String key, String value, long timeout) {
        try {
            stringRedisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
            return true;
        } catch (Exception e) {
            log.error("Redis设置数据异常, key: {}, value: {}, timeout: {}", key, value, timeout, e);
            return false;
        }
    }

    /**
     * 删除键
     *
     * @param key 键
     * @return 是否成功
     */
    public boolean delete(String key) {
        try {
            return Boolean.TRUE.equals(stringRedisTemplate.delete(key));
        } catch (Exception e) {
            log.error("Redis删除数据异常, key: {}", key, e);
            return false;
        }
    }

    /**
     * 判断键是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public boolean exists(String key) {
        try {
            return Boolean.TRUE.equals(stringRedisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("Redis检查键是否存在异常, key: {}", key, e);
            return false;
        }
    }

    /**
     * 设置键的过期时间
     *
     * @param key     键
     * @param timeout 过期时间（秒）
     * @return 是否成功
     */
    public boolean expire(String key, long timeout) {
        try {
            return Boolean.TRUE.equals(stringRedisTemplate.expire(key, timeout, TimeUnit.SECONDS));
        } catch (Exception e) {
            log.error("Redis设置过期时间异常, key: {}, timeout: {}", key, timeout, e);
            return false;
        }
    }

    /**
     * 获取键的剩余过期时间
     *
     * @param key 键
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示键不存在
     */
    public long getExpire(String key) {
        try {
            Long expire = stringRedisTemplate.getExpire(key, TimeUnit.SECONDS);
            return expire != null ? expire : -2;
        } catch (Exception e) {
            log.error("Redis获取过期时间异常, key: {}", key, e);
            return -2;
        }
    }

    /**
     * 测试 Redis 连接
     *
     * @return 是否连接成功
     */
    public boolean testConnection() {
        try {
            String testKey = "redis_test_connection";
            String testValue = "test_value_" + System.currentTimeMillis();

            // 设置测试值
            boolean setResult = set(testKey, testValue, 60);
            if (!setResult) {
                log.error("Redis连接测试失败：无法设置测试值");
                return false;
            }

            // 获取测试值
            String getValue = get(testKey);
            if (!testValue.equals(getValue)) {
                log.error("Redis连接测试失败：获取的值与设置的值不匹配");
                return false;
            }

            // 删除测试值
            delete(testKey);

            log.info("Redis连接测试成功");
            return true;
        } catch (Exception e) {
            log.error("Redis连接测试异常", e);
            return false;
        }
    }
}
