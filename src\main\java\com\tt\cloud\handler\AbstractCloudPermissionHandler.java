package com.tt.cloud.handler;

import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.bean.ResponseHeader;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2023/11/24 11:10
 */
@Component
public abstract class AbstractCloudPermissionHandler {

    public abstract boolean supports(String cloudType);

    public abstract Object queryUserPermissions(PermissionEntity permissionEntity);


    public Object grantUserPermissions( PermissionEntity permissionEntity) {
        return getResult();
    }

    public abstract Object queryGroupPermissionList(PermissionEntity permissionEntity);

    public abstract Object queryPermissionList(PermissionEntity permissionEntity);

    public abstract Object grantGroupPermissions(PermissionEntity permissionEntity);

    public Object queryPermissionVersion(PermissionEntity permissionEntity) {
        return getResult();
    }

    private Map<String, Object> getResult() {
        Map<String, Object> result = new HashMap<>();
        ResponseHeader responseHeader = new ResponseHeader();
        responseHeader.setErrorMsg("此功能暂未开放");
        result.put("header", responseHeader);
        return result;
    }
}
