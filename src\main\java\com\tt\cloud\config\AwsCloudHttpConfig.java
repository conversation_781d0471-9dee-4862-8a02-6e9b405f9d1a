package com.tt.cloud.config;

import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.CacheUtil;
import com.tt.cloud.util.EncryptUtils;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.iam.IamClient;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 15:02
 */
@Slf4j
@Component
public class AwsCloudHttpConfig {
    @Resource
    private CloudAccountDao cloudAccountDao;

    /**
     * 创建awsClient
     *
     * @param accountId accountId
     * @return AwsIamClient
     */
    public IamClient getAWSIamClient(String accountId) {
        String cacheKey = "AWS_IamClient_" + accountId;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (IamClient) object;
        }
        CloudAccountEntity cloudAccountEntity
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        IamClient awsClient = IamClient.builder()
                .region(Region.AWS_GLOBAL)
                .credentialsProvider(() -> AwsBasicCredentials.create(cloudAccountEntity.getSecretId(),
                        EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccountEntity.getSecretKey())))
                .build();
        CacheUtil.put(cacheKey, awsClient, 24 * 60 * 60);
        return awsClient;
    }

}
