package com.tt.cloud.bean;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/26 10:32
 */
@Getter
@Setter
@ToString
public class PermissionVersion {
   private String Version;
   private List<Statement> Statement = new ArrayList<>();
}

@Getter
@Setter
@ToString
class Statement {
    private String Effect;
    private List<String> Action;
    private String Resource;
    private Object Condition;
}