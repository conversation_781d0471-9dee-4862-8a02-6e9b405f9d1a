package com.tt.cloud.operatelog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tt.cloud.bean.BusinessOperationLog;
import com.tt.cloud.bean.BusinessOperationRequest;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.service.BusinessOperationServiceImpl;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2021/12/22 10:20
 */
@Slf4j
@Aspect
@Component
public class OperateLogAspect {
    @Resource
    private BusinessOperationServiceImpl businessOperationService;

    /**
     * 设置操作日志切入点 记录操作日志 在注解的位置切入代码
     */
    @Pointcut("@annotation(OperateLog)")
    public void operateLogPointCut() {
    }

    /**
     * 设置操作异常切入点记录异常日志 扫描所有controller包下操作
     */
    @Pointcut("execution(* com.tt.cloud.controller..*.*(..))")
    public void operateExceptionLogPointCut() {

    }

    /**
     * 拦截用户操作日志
     *
     * @param joinPoint 切入点
     * @param keys 返回结果
     */
    @AfterReturning(value = "operateLogPointCut()", returning = "keys")
    public void saveOperateLog(JoinPoint joinPoint, Object keys) {
        try {
            Date endTime = new Date();
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (null == requestAttributes) {
                return;
            }
            HttpServletRequest request = (HttpServletRequest) requestAttributes
                    .resolveReference(RequestAttributes.REFERENCE_REQUEST);
            if (null == request) {
                return;
            }
            MethodSignature signature = (MethodSignature) joinPoint.getSignature(); // 从切面织入点处通过反射机制获取织入点处的方法
            if (null == signature) {
                return;
            }
            Method method = signature.getMethod(); // 获取切入点所在的方法
            OperateLog opLog = method.getAnnotation(OperateLog.class); // 获取操作
            if (null == opLog) {
                return;
            }
            BusinessOperationLog operationLog = new BusinessOperationLog();
            operationLog.setOperateDesc(opLog.operateDesc()); // 操作描述
            operationLog.setBusinessName(opLog.businessName());
            operationLog.setClassMethod(method.getName());
            getRequestParam(joinPoint, request, signature, operationLog);
            operationLog.setRequestMethod(request.getMethod());
            operationLog.setRequestUrl(request.getRequestURI());
            parseResponseHeader(keys, operationLog);
            operationLog.setResponseParam(JSON.toJSONString(keys)); // 返回结果
            operationLog.setUserId(UserContext.getCurrentUserId()); // 请求用户ID
            operationLog.setOperateTime(endTime); // 创建时间
            BusinessOperationRequest operationRequest = new BusinessOperationRequest();
            operationRequest.setBusinessOperationLog(operationLog);
            businessOperationService.addBusinessOperationInfo(operationRequest);
        } catch (Exception e) {
            log.error("记录操作日志异常，不影响业务", e);
        }
    }

    private void getRequestParam(JoinPoint joinPoint, HttpServletRequest request,
            MethodSignature signature, BusinessOperationLog operationLog) {
        Map<String, String> rtnMap = convertMap(request.getParameterMap());
        String params = JSON.toJSONString(rtnMap); // 将参数所在的数组转换成json
        operationLog.setRequestParam(params); // 请求参数
        String[] parameterNames = signature.getParameterNames();
        if (parameterNames.length > 0 ) {
            Object[] parameterValues = joinPoint.getArgs();
            Map<String,Object> paramMap = new HashMap<>();
            for (int i=0;i<parameterValues.length;i++) {
                try {
                    String s = JSON.toJSONString(parameterValues[i]);
                    paramMap.put(parameterNames[i],s);
                } catch (Exception e) {
                    log.debug("获取请求参数异常");
                }
            }
            operationLog.setRequestParam(JSON.toJSONString(paramMap)); // 请求参数
        }
    }

    private void parseResponseHeader(Object keys, BusinessOperationLog operationLog) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(keys));
        Object header = jsonObject.get("header");
        if (null != header) {
            JSONObject jsonHeader = JSON.parseObject(header.toString());
            Object code = jsonHeader.get("code");
            if (null != code) {
                if (!Constants.SUCCESS.equals(code.toString())) {
                    operationLog.setOperateState("0");
                }
            }
            Object msg = jsonHeader.get("msg");
            if (null != msg) {
                operationLog.setOperateMsg(msg.toString());
            }
        }
    }

    /**
     * 转换request 请求参数
     *
     * @param paramMap request获取的参数数组
     */
    public Map<String, String> convertMap(Map<String, String[]> paramMap) {
        Map<String, String> rtnMap = new HashMap<>();
        for (String key : paramMap.keySet()) {
            rtnMap.put(key, paramMap.get(key)[0]);
        }
        return rtnMap;
    }
}