package com.tt.cloud.sso.metadata;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.CertificateFactory;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import lombok.extern.slf4j.Slf4j;
import org.opensaml.Configuration;
import org.opensaml.saml2.metadata.ArtifactResolutionService;
import org.opensaml.saml2.metadata.AssertionConsumerService;
import org.opensaml.saml2.metadata.Endpoint;
import org.opensaml.saml2.metadata.EntityDescriptor;
import org.opensaml.saml2.metadata.KeyDescriptor;
import org.opensaml.saml2.metadata.ManageNameIDService;
import org.opensaml.saml2.metadata.NameIDFormat;
import org.opensaml.saml2.metadata.SPSSODescriptor;
import org.opensaml.saml2.metadata.SSODescriptor;
import org.opensaml.saml2.metadata.SingleLogoutService;
import org.opensaml.xml.XMLObject;
import org.opensaml.xml.io.Unmarshaller;
import org.opensaml.xml.io.UnmarshallerFactory;
import org.opensaml.xml.security.credential.UsageType;
import org.opensaml.xml.security.x509.BasicX509Credential;
import org.opensaml.xml.signature.X509Certificate;
import org.opensaml.xml.util.Base64;
import org.springframework.util.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

@Slf4j
public class MetadataBean {
    private String entityId;
    private SSODescriptor ssoDescriptor;
    private List<BasicX509Credential> signingCredList;
    private List<BasicX509Credential> encryptionCredList;
    private List<NameIDFormat> nameIDFormatList;
    private Map<String, List<Endpoint>> endpointMap;

    public String getEntityId() {
        return entityId;
    }

    public List<BasicX509Credential> getSigningCredList() {
        return signingCredList;
    }

    public MetadataBean() {

    }

    public MetadataBean(String filePath) {
        initMetadata(filePath);
    }

    public void initSPMetadata(String metadataContent) {
        if (StringUtils.isEmpty(metadataContent)) {
            log.error("error : metadataContent is empty");
            return;
        }
        EntityDescriptor entityDescriptor = loadEntityDescriptorFromString(metadataContent);

        if (null != entityDescriptor.getIDPSSODescriptor(MetadataConstants.SAML2_SUPPORTED_PROTOCOL)) {
            this.ssoDescriptor = entityDescriptor.getIDPSSODescriptor(MetadataConstants.SAML2_SUPPORTED_PROTOCOL);

            this.entityId = entityDescriptor.getEntityID();
            try {
                this.signingCredList = generateIDPCredList();
            } catch (Exception e) {
                log.error("generateIDPCredList error", e);
            }
            try {
                this.encryptionCredList = generateIDPCredList();
            } catch (Exception e) {
                log.error("generateIDPCredList error", e);
            }
        } else {
            this.ssoDescriptor = entityDescriptor.getSPSSODescriptor(MetadataConstants.SAML2_SUPPORTED_PROTOCOL);
            this.entityId = entityDescriptor.getEntityID();
            this.signingCredList = generateSPCredList(UsageType.SIGNING);
            this.encryptionCredList = generateSPCredList(UsageType.ENCRYPTION);
            this.nameIDFormatList = ssoDescriptor.getNameIDFormats();
            SPSSODescriptor spssoDescriptor = (SPSSODescriptor) this.ssoDescriptor;
            this.endpointMap = new HashMap<>();

            this.endpointMap.put(AssertionConsumerService.DEFAULT_ELEMENT_LOCAL_NAME,
                    covertToEndpoints(spssoDescriptor.getAssertionConsumerServices()));

            this.endpointMap.put(ArtifactResolutionService.DEFAULT_ELEMENT_LOCAL_NAME,
                    covertToEndpoints(spssoDescriptor.getArtifactResolutionServices()));

            this.endpointMap.put(SingleLogoutService.DEFAULT_ELEMENT_LOCAL_NAME,
                    covertToEndpoints(spssoDescriptor.getSingleLogoutServices()));

            this.endpointMap.put(ManageNameIDService.DEFAULT_ELEMENT_LOCAL_NAME,
                    covertToEndpoints(spssoDescriptor.getManageNameIDServices()));
        }
    }

    public void initMetadata(String filePath) {
        String metadataContent = getMetadataString(filePath);

        if (StringUtils.isEmpty(metadataContent)) {
            log.error("error : metadataContent is empty");
            return;
        }
        EntityDescriptor entityDescriptor = loadEntityDescriptorFromString(metadataContent);

        if (null != entityDescriptor.getIDPSSODescriptor(MetadataConstants.SAML2_SUPPORTED_PROTOCOL)) {
            this.ssoDescriptor = entityDescriptor.getIDPSSODescriptor(MetadataConstants.SAML2_SUPPORTED_PROTOCOL);

            this.entityId = entityDescriptor.getEntityID();
            try {
                this.signingCredList = generateIDPCredList();
            } catch (Exception e) {
                log.error("generateIDPCredList error", e);
            }
            try {
                this.encryptionCredList = generateIDPCredList();
            } catch (Exception e) {
                log.error("generateIDPCredList error", e);
            }
        } else {
            this.ssoDescriptor = entityDescriptor.getSPSSODescriptor(MetadataConstants.SAML2_SUPPORTED_PROTOCOL);
            this.entityId = entityDescriptor.getEntityID();
            this.signingCredList = generateSPCredList(UsageType.SIGNING);
            this.encryptionCredList = generateSPCredList(UsageType.ENCRYPTION);
            this.nameIDFormatList = ssoDescriptor.getNameIDFormats();
            SPSSODescriptor spssoDescriptor = (SPSSODescriptor) this.ssoDescriptor;
            this.endpointMap = new HashMap<>();

            this.endpointMap.put(AssertionConsumerService.DEFAULT_ELEMENT_LOCAL_NAME,
                    covertToEndpoints(spssoDescriptor.getAssertionConsumerServices()));

            this.endpointMap.put(ArtifactResolutionService.DEFAULT_ELEMENT_LOCAL_NAME,
                    covertToEndpoints(spssoDescriptor.getArtifactResolutionServices()));

            this.endpointMap.put(SingleLogoutService.DEFAULT_ELEMENT_LOCAL_NAME,
                    covertToEndpoints(spssoDescriptor.getSingleLogoutServices()));

            this.endpointMap.put(ManageNameIDService.DEFAULT_ELEMENT_LOCAL_NAME,
                    covertToEndpoints(spssoDescriptor.getManageNameIDServices()));
        }
    }

    private <T extends Endpoint> List<Endpoint> covertToEndpoints(List<T> services) {
        List<Endpoint> endpointList = new ArrayList<>();
        for (T t : services) {
            endpointList.add(t);
        }
        return endpointList;
    }

    private List<BasicX509Credential> generateSPCredList(UsageType usageType) {
        List<KeyDescriptor> keyDescriptors = ssoDescriptor.getKeyDescriptors();
        if (null == keyDescriptors) {
            throw new RuntimeException("sp keyDescriptors is null");
        }
        BasicX509Credential basicX509Credential = new BasicX509Credential();
        for (KeyDescriptor keyDescriptor : keyDescriptors) {
            if (keyDescriptor.getUse() == usageType) {
                X509Certificate x509Certificate = keyDescriptor.getKeyInfo().getX509Datas().get(0).getX509Certificates()
                        .get(0);
                java.security.cert.X509Certificate certificate = covertSamlCertToJavaCert(x509Certificate);
                basicX509Credential.setEntityCertificate(certificate);
                break;
            }
        }
        List<BasicX509Credential> basicX509Credentials = new ArrayList<>();
        basicX509Credentials.add(basicX509Credential);
        return basicX509Credentials;
    }

    private java.security.cert.X509Certificate covertSamlCertToJavaCert(X509Certificate x509Certificate) {
        CertificateFactory certificateFactory = null;
        try {
            certificateFactory = CertificateFactory.getInstance("X.509");
        } catch (Exception e) {
            log.error("getInstance X.509 error", e);
        }
        String certificateString = x509Certificate.getValue();
        byte[] bytes = Base64.decode(certificateString);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        java.security.cert.X509Certificate certificate = null;
        try {
            certificate = (java.security.cert.X509Certificate) certificateFactory.generateCertificate(byteArrayInputStream);
        } catch (Exception e) {
            log.error("certificateFactory.generateCertificate error", e);
        }
        return certificate;
    }

    private static List<BasicX509Credential> generateIDPCredList() {
        String fileName = IDPMetadata.keystorePath;
        char[] password = IDPMetadata.keystorePass.toCharArray();
        InputStream inputStream = null;
        java.security.cert.X509Certificate certificate = null;
        PrivateKey privateKey = null;
        try {
            KeyStore keystore = KeyStore.getInstance(KeyStore.getDefaultType());
            inputStream = new FileInputStream(fileName);
            keystore.load(inputStream, password);
            String aliasName = IDPMetadata.aliasName;
            certificate = (java.security.cert.X509Certificate) keystore.getCertificate(aliasName);
            privateKey = (PrivateKey) keystore.getKey(aliasName, password);

        } catch (Exception e) {
            log.error("getKey error", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.error("inputStream close error", e);
                }
            }
        }
        List<BasicX509Credential> basicX509Credentials = new ArrayList<>();
        BasicX509Credential basicX509Credential = new BasicX509Credential();
        basicX509Credential.setEntityCertificate(certificate);
        basicX509Credential.setPrivateKey(privateKey);
        basicX509Credentials.add(basicX509Credential);
        return basicX509Credentials;
    }


    private static String getMetadataString(String filePath) {
        String str = "";
        File spMetadataFile = new File(filePath);
        try {
            FileInputStream in = new FileInputStream(spMetadataFile);
            int size = in.available();
            byte[] buffer = new byte[size];
            in.read(buffer);
            in.close();
            str = new String(buffer, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("getMetadataString error", e);
        }
        return str;
    }

    private EntityDescriptor loadEntityDescriptorFromString(String content) {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(content.getBytes());
        return (EntityDescriptor) unmarshallXMLObject(byteArrayInputStream);
    }

    public static XMLObject unmarshallXMLObject(InputStream inputStream) {
        try {
            DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
            documentBuilderFactory.setNamespaceAware(true);
            DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
            Document document = documentBuilder.parse(inputStream);
            Element element = document.getDocumentElement();
            UnmarshallerFactory unmarshallerFactory = Configuration.getUnmarshallerFactory();
            Unmarshaller unmarshaller = unmarshallerFactory.getUnmarshaller(element);
            return unmarshaller.unmarshall(element);
        } catch (Exception e) {
            log.error("unmarshallXMLObject error", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    log.error("inputStream close error", e);
                }
            }
        }
        return null;
    }
}
