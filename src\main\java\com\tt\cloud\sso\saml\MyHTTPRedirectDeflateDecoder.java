package com.tt.cloud.sso.saml;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.zip.Inflater;
import java.util.zip.InflaterInputStream;
import org.opensaml.common.SAMLObject;
import org.opensaml.common.binding.SAMLMessageContext;
import org.opensaml.saml2.binding.decoding.BaseSAML2MessageDecoder;
import org.opensaml.ws.message.MessageContext;
import org.opensaml.ws.message.decoder.MessageDecodingException;
import org.opensaml.ws.transport.http.HTTPInTransport;
import org.opensaml.xml.parse.ParserPool;
import org.opensaml.xml.security.SecurityException;
import org.opensaml.xml.util.Base64;
import org.opensaml.xml.util.DatatypeHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/21 14:41
 */
public class MyHTTPRedirectDeflateDecoder extends BaseSAML2MessageDecoder {
    private final Logger log = LoggerFactory.getLogger(
            org.opensaml.saml2.binding.decoding.HTTPRedirectDeflateDecoder.class);

    public MyHTTPRedirectDeflateDecoder() {
    }

    public String getBindingURI() {
        return "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect";
    }

    protected boolean isIntendedDestinationEndpointURIRequired(SAMLMessageContext samlMsgCtx) {
        return this.isMessageSigned(samlMsgCtx);
    }

    protected void doDecode(MessageContext messageContext) throws MessageDecodingException {
        if (!(messageContext instanceof SAMLMessageContext)) {
            this.log.error("Invalid message context type, this decoder only support SAMLMessageContext");
            throw new MessageDecodingException("Invalid message context type, this decoder only support SAMLMessageContext");
        } else if (!(messageContext.getInboundMessageTransport() instanceof HTTPInTransport)) {
            this.log.error("Invalid inbound message transport type, this decoder only support HTTPInTransport");
            throw new MessageDecodingException("Invalid inbound message transport type, this decoder only support HTTPInTransport");
        } else {
            SAMLMessageContext samlMsgCtx = (SAMLMessageContext)messageContext;
            HTTPInTransport inTransport = (HTTPInTransport)samlMsgCtx.getInboundMessageTransport();
            String relayState = inTransport.getParameterValue("RelayState");
            samlMsgCtx.setRelayState(relayState);
            this.log.debug("Decoded RelayState: {}", relayState);
            InputStream samlMessageIns;
            if (!DatatypeHelper.isEmpty(inTransport.getParameterValue("SAMLRequest"))) {
                samlMessageIns = this.decodeMessage(inTransport.getParameterValue("SAMLRequest"));
            } else {
                if (DatatypeHelper.isEmpty(inTransport.getParameterValue("SAMLResponse"))) {
                    throw new MessageDecodingException("No SAMLRequest or SAMLResponse query path parameter, invalid SAML 2 HTTP Redirect message");
                }

                samlMessageIns = this.decodeMessage(inTransport.getParameterValue("SAMLResponse"));
            }

            SAMLObject samlMessage = (SAMLObject)this.unmarshallMessage(samlMessageIns);
            samlMsgCtx.setInboundSAMLMessage(samlMessage);
            samlMsgCtx.setInboundMessage(samlMessage);
            this.log.debug("Decoded SAML message");
            this.populateMessageContext(samlMsgCtx);
        }
    }

    @Override
    public void decode(MessageContext messageContext) throws MessageDecodingException, SecurityException {
        this.log.debug("Beginning to decode message from inbound transport of type: {}", messageContext.getInboundMessageTransport().getClass().getName());
        this.doDecode(messageContext);
        this.logDecodedMessage(messageContext);
        this.processSecurityPolicy(messageContext);
        this.log.debug("Successfully decoded message.");
        this.checkEndpointURI((SAMLMessageContext) messageContext);
    }

    protected void checkEndpointURI(SAMLMessageContext messageContext) throws SecurityException, MessageDecodingException {
        this.log.debug("Checking SAML message intended destination endpoint against receiver endpoint");
        String messageDestination = DatatypeHelper.safeTrimOrNullString(this.getIntendedDestinationEndpointURI(messageContext));
        boolean bindingRequires = this.isIntendedDestinationEndpointURIRequired(messageContext);
        if (messageDestination == null) {
            if (bindingRequires) {
                this.log.error("SAML message intended destination endpoint URI required by binding was empty");
                throw new SecurityException("SAML message intended destination (required by binding) was not present");
            } else {
                this.log.debug("SAML message intended destination endpoint in message was empty, not required by binding, skipping");
            }
        } else {
            String receiverEndpoint = DatatypeHelper.safeTrimOrNullString(this.getActualReceiverEndpointURI(messageContext));
            this.log.debug("Intended message destination endpoint: {}", messageDestination);
            this.log.debug("Actual message receiver endpoint: {}", receiverEndpoint);
            this.compareEndpointURIs(messageDestination, receiverEndpoint);
        }
    }

    protected boolean isMessageSigned(SAMLMessageContext messageContext) {
        HTTPInTransport inTransport = (HTTPInTransport)messageContext.getInboundMessageTransport();
        String sigParam = inTransport.getParameterValue("Signature");
        return !DatatypeHelper.isEmpty(sigParam) || super.isMessageSigned(messageContext);
    }

    protected InputStream decodeMessage(String message) throws MessageDecodingException {
        this.log.debug("Base64 decoding and inflating SAML message");
        byte[] decodedBytes = Base64.decode(message);
        if (decodedBytes == null) {
            this.log.error("Unable to Base64 decode incoming message");
            throw new MessageDecodingException("Unable to Base64 decode incoming message");
        } else {
            try {
                ByteArrayInputStream bytesIn = new ByteArrayInputStream(decodedBytes);
                return new InflaterInputStream(bytesIn, new Inflater(true));
            } catch (Exception var5) {
                this.log.error("Unable to Base64 decode and inflate SAML message", var5);
                throw new MessageDecodingException("Unable to Base64 decode and inflate SAML message", var5);
            }
        }
    }
}
