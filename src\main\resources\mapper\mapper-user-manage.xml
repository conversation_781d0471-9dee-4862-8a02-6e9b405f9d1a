<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<!--上面2行的是约束依赖，固定照抄就好-->
<!--下面的才是要自己编写的地方-->
<!--写mapper的配置文件第一步就是要写<mapper></mapper>标签-->
<!--<mapper></mapper>标签里包含着各个CURD操作的SQL语句-->
<mapper namespace="com.tt.cloud.dao.UserDao">


  <select id="queryUserInfo" resultType="com.tt.cloud.bean.UserInfo">
    select userid,
           username,
           issuperuser,
           email,
           issystemuser,
           usergroup,
           state,
           createtime
    from tt_op_user
    where state = 1
      and userid = #{userId}
  </select>

  <select id="queryUserByEmail" resultType="com.tt.cloud.bean.UserEntity">
    select userid, username, issuperuser, email, issystemuser, usergroup, createtime,
           isapiuser, state
    from tt_op_user where state = 1 and email = #{email}
  </select>

</mapper>
