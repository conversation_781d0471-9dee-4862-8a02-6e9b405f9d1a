package com.tt.cloud.bean;

import com.aliyun.ram20150501.models.ListPoliciesResponseBody.ListPoliciesResponseBodyPoliciesPolicy;
import com.aliyun.slb20140515.models.DescribeLoadBalancersResponseBody;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/18 9:48
 */
@Getter
@Setter
@ToString
public class AliCloudResponse {
    private ResponseHeader header = new ResponseHeader();
    private String userId;
    private UserEntity userEntity;
    private List<GroupEntity> groupList = new ArrayList<>();
    private List<UserEntity> userList;
    private GroupEntity groupEntity;
    private List<PermissionEntity> permissionList;

    private List<ListPoliciesResponseBodyPoliciesPolicy> strategyInfoList;

    private List<PermanentAccessKeyEntity> permanentAccessKeyList;

    private PermanentAccessKeyEntity permanentAccessKeyEntity;

    private String loginUrl;

    private List<DescribeLoadBalancersResponseBody.DescribeLoadBalancersResponseBodyLoadBalancersLoadBalancer> loadBalancers;
}
