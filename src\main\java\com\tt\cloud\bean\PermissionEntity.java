package com.tt.cloud.bean;

import software.amazon.awssdk.services.sts.model.PolicyDescriptorType;
import com.huaweicloud.sdk.iam.v3.model.RolePolicy;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/21 9:36
 */
@Getter
@Setter
@ToString
public class PermissionEntity {
   private String id;
   private String name;
   private String display_name;
   private String description;
   private String description_cn;
   private String catalog;
   private String type;
   private String belongId;
   private String belongName;
   private String belongType;
   private String belongDescription;
   private String permissionType;

   private List<String> globalPermissionIdList;
   private List<String> projectPermissionIdList;
   private List<String> allPermissionIdList;

   private String projectName;
   private String projectId;

   private String groupId;
   private String groupName;

   private List<String> projectIdList;

   private RolePolicy policy;

   private int page = 1;
   private int size = 10;

   private String userId;
   private String userIn;
   private String userName;

   private List<String> permissionIdList;

   private List<Long> policyIdList;
   private Long policyId;
   private String policyName;
   private Long createMode;
   private String policyType;
   private String remark;
   private String addTime;
   private String arn;
   private List<PermissionEntity> permissionList;
   private List<PolicyDescriptorType> policyArns;
   private List<String> policyArnList;
   private String versionId;
   private String policyVersion;
   private String accountId;
}
