package com.tt.cloud.controller.v2;

import com.alibaba.fastjson.JSON;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.dnspod.v20210323.DnspodClient;
import com.tencentcloudapi.dnspod.v20210323.models.CreateRecordRequest;
import com.tencentcloudapi.dnspod.v20210323.models.CreateRecordResponse;
import com.tencentcloudapi.dnspod.v20210323.models.DescribeRecordFilterListRequest;
import com.tencentcloudapi.dnspod.v20210323.models.DescribeRecordFilterListResponse;
import com.tencentcloudapi.dnspod.v20210323.models.DescribeRecordLineListRequest;
import com.tencentcloudapi.dnspod.v20210323.models.DescribeRecordLineListResponse;
import com.tencentcloudapi.dnspod.v20210323.models.DescribeRecordRequest;
import com.tencentcloudapi.dnspod.v20210323.models.DescribeRecordResponse;
import com.tencentcloudapi.dnspod.v20210323.models.DescribeRecordTypeRequest;
import com.tencentcloudapi.dnspod.v20210323.models.DescribeRecordTypeResponse;
import com.tencentcloudapi.dnspod.v20210323.models.LineInfo;
import com.tencentcloudapi.dnspod.v20210323.models.ModifyRecordRequest;
import com.tencentcloudapi.dnspod.v20210323.models.ModifyRecordResponse;
import com.tencentcloudapi.dnspod.v20210323.models.RecordInfo;
import com.tencentcloudapi.dnspod.v20210323.models.RecordListItem;
import com.tt.cloud.bean.CommonResponse;
import com.tt.cloud.bean.DomainRecordEntity;
import com.tt.cloud.bean.DomainRequest;
import com.tt.cloud.bean.DomainEntity;
import com.tt.cloud.bean.ResponseHeader;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.v2.BatchVerifyResponse;
import com.tt.cloud.bean.v2.CommonListResponse;
import com.tt.cloud.bean.v2.DNSRecordResponse;
import com.tt.cloud.bean.v2.DomainItem;
import com.tt.cloud.bean.v2.OperationTypeEnum;
import com.tt.cloud.bean.v2.ResourceChangeLog;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.DomainDao;
import com.tt.cloud.dao.ResourceChangeDao;
import com.tt.cloud.dao.UserDao;
import com.tt.cloud.exception.ServiceRuntimeException;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.CommonUtil;
import com.tt.cloud.util.Utils;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/1/18 15:29
 */
@Slf4j
@RestController
@RequestMapping("/rest/v2")
public class CloudDNSV2Controller {

    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat(Constants.DATA_FORMAT);
    @Value("${spring.profiles.active}")
    private String active;
    @Resource
    private DomainDao domainDao;
    @Resource
    private UserDao userDao;
    @Resource
    private ResourceChangeDao resourceChangeDao;
    @Resource
    private TencentCloudHttpConfig tencentCloudHttpConfig;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private FlyBookService flyBookService;

    @GetMapping("/domain/list")
    public CommonListResponse queryDomainList(DomainRequest domainRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            List<Map<String, String>> list = new ArrayList<>();
            List<DomainEntity> domainList = domainDao.queryDomainList();
            if (StringUtils.isNotEmpty(domainRequest.getDomain_name())) {
                domainList = domainList.stream()
                        .filter(domainEntity -> domainEntity.getDomainName().toLowerCase(
                                        Locale.ROOT)
                                .contains(domainRequest.getDomain_name().toLowerCase(Locale.ROOT)))
                        .collect(
                                Collectors.toList());
            }
            for (DomainEntity domainEntity : domainList) {
                Map<String, String> domainMap = new HashMap<>();
                domainMap.put("domain_id", domainEntity.getDomainId());
                domainMap.put("domain_name", domainEntity.getDomainName());
                list.add(domainMap);
            }
            commonListResponse.setTotal(list.size());
            commonListResponse.setList(CommonUtil.getPage(list, domainRequest.getPageNo(),
                    domainRequest.getPageSize()));
        } catch (Exception e) {
            CommonUtil.setErrorMsg(commonListResponse.getHeader(), e, "查询主域名列表异常，请联系管理员");
        }
        return commonListResponse;
    }

    @GetMapping("/subdomain/list")
    public CommonListResponse querySubDomainList(DomainRequest request) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(request.getDomain_id())) {
                return commonListResponse;
            }
            DomainEntity domainEntity = domainDao.queryDomainInfoById(request.getDomain_id());
            if (null == domainEntity) {
                return commonListResponse;
            }
            DescribeRecordFilterListRequest filterListRequest = new DescribeRecordFilterListRequest();
            filterListRequest.setDomain(domainEntity.getDomainName());
            filterListRequest.setDomainId(Long.parseLong(domainEntity.getDomainId()));
            filterListRequest.setSubDomain(request.getSub_domain());
            filterListRequest.setRecordValue(request.getRecord_value());
            String[] recordStatus = {"ENABLE"};
            filterListRequest.setRecordStatus(recordStatus);
            filterListRequest.setOffset(request.getPageNo() <= 0 ? 0
                    : (request.getPageNo() - 1) * request.getPageSize());
            filterListRequest.setLimit(request.getPageSize());
            filterListRequest.setIsExactSubDomain(true);
            DescribeRecordFilterListResponse filterListResponse =
                    tencentCloudHttpConfig.getTencentDnspodClient(domainEntity.getAccountId())
                            .DescribeRecordFilterList(filterListRequest);
            List<Map<String, Object>> list = new ArrayList<>();
            for (RecordListItem recordItem : filterListResponse.getRecordList()) {
                Map<String, Object> recordMap = new HashMap<>();
                recordMap.put("sub_domain_id", String.valueOf(recordItem.getRecordId()));
                recordMap.put("sub_domain", recordItem.getName());
                recordMap.put("record_type", recordItem.getType());
                recordMap.put("record_value", recordItem.getValue());
                recordMap.put("line_id", recordItem.getLineId());
                recordMap.put("line_name", recordItem.getLine());
                recordMap.put("ttl", recordItem.getTTL());
                recordMap.put("weight", recordItem.getWeight());
                recordMap.put("mx", recordItem.getMX());
                list.add(recordMap);
            }
            commonListResponse.setList(list);
            commonListResponse.setTotal(filterListResponse.getRecordCountInfo().getTotalCount());
        } catch (Exception e) {
            CommonUtil.setErrorMsg(commonListResponse.getHeader(), e, "查询主域名列表异常，请联系管理员");
        }
        return commonListResponse;
    }

    @GetMapping("/record/type/list")
    public CommonListResponse queryRecordTypeList(DomainRequest request) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(request.getDomain_id())) {
                return commonListResponse;
            }
            DomainEntity domainEntity = domainDao.queryDomainInfoById(request.getDomain_id());
            if (null == domainEntity) {
                return commonListResponse;
            }
            List<Map<String, String>> list = new ArrayList<>();
            DescribeRecordTypeRequest recordTypeRequest = new DescribeRecordTypeRequest();
            recordTypeRequest.setDomainGrade(domainEntity.getGrade());
            DescribeRecordTypeResponse recordTypeResponse =
                    tencentCloudHttpConfig.getTencentDnspodClient(domainEntity.getAccountId())
                            .DescribeRecordType(recordTypeRequest);
            for (String type : recordTypeResponse.getTypeList()) {
                Map<String, String> typeMap = new HashMap<>();
                typeMap.put("type_id", type);
                typeMap.put("type_name", type);
                list.add(typeMap);
            }
            commonListResponse.setTotal(list.size());
            commonListResponse.setList(
                    CommonUtil.getPage(list, request.getPageNo(), request.getPageSize()));
        } catch (Exception e) {
            CommonUtil.setErrorMsg(commonListResponse.getHeader(), e, "查询记录类型列表异常，请联系管理员");
        }
        return commonListResponse;
    }

    @GetMapping("/record/line/list")
    public CommonListResponse queryRecordLineList(DomainRequest request) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(request.getDomain_id())) {
                return commonListResponse;
            }
            DomainEntity domainEntity = domainDao.queryDomainInfoById(request.getDomain_id());
            if (null == domainEntity) {
                return commonListResponse;
            }
            List<Map<String, String>> list = new ArrayList<>();
            DescribeRecordLineListRequest lineListRequest = new DescribeRecordLineListRequest();
            lineListRequest.setDomainId(Long.parseLong(request.getDomain_id()));
            lineListRequest.setDomain(domainEntity.getDomainName());
            lineListRequest.setDomainGrade(domainEntity.getGrade());
            DescribeRecordLineListResponse lineListResponse =
                    tencentCloudHttpConfig.getTencentDnspodClient(domainEntity.getAccountId())
                            .DescribeRecordLineList(lineListRequest);
            for (LineInfo lineInfo : lineListResponse.getLineList()) {
                Map<String, String> lineMap = new HashMap<>();
                lineMap.put("line_id", lineInfo.getLineId());
                lineMap.put("line_name", lineInfo.getName());
                list.add(lineMap);
            }
            commonListResponse.setTotal(list.size());
            commonListResponse.setList(
                    CommonUtil.getPage(list, request.getPageNo(), request.getPageSize()));
        } catch (Exception e) {
            CommonUtil.setErrorMsg(commonListResponse.getHeader(), e, "查询线路列表异常，请联系管理员");
        }
        return commonListResponse;
    }

    @PostMapping("/record/verify")
    public BatchVerifyResponse verifyRecordList(@RequestBody DomainRequest request) {
        BatchVerifyResponse batchVerifyResponse = new BatchVerifyResponse();
        try {
            if (request.getList().isEmpty()) {
                throw new ServiceRuntimeException("子域名变更异常，变更数据为空");
            }
            for (DomainItem domainItem : request.getList()) {
                CommonResponse commonResponse = verifyRecord(domainItem);
                batchVerifyResponse.getVerify_results().add(commonResponse.getHeader());
            }
        } catch (Exception e) {
            ResponseHeader header = new ResponseHeader();
            CommonUtil.setErrorMsg(header, e, "校验子域名变更参数异常，请联系管理员");
            batchVerifyResponse.getVerify_results().add(header);
        }
        return batchVerifyResponse;
    }

    public CommonResponse verifyRecord(DomainItem domainItem) {
        CommonResponse commonResponse = new CommonResponse();
        try {
            log.info("校验域名变更参数：{}", JSON.toJSONString(domainItem));
            if (StringUtils.isEmpty(domainItem.getEmail())) {
                throw new ServiceRuntimeException("子域名变更异常，用户邮箱为空");
            }
            UserEntity userEntity = userDao.queryUserByEmail(domainItem.getEmail());
            if (userEntity == null) {
                throw new ServiceRuntimeException("子域名变更异常，用户不存在");
            }
            if (StringUtils.isEmpty(domainItem.getOperation_type())) {
                throw new ServiceRuntimeException("校验不通过，操作类型为空");
            }
            if (!OperationTypeEnum.ADD.getType().equals(domainItem.getOperation_type())
                    && !OperationTypeEnum.DELETE.getType().equals(domainItem.getOperation_type())
                    && !OperationTypeEnum.EDIT.getType().equals(domainItem.getOperation_type())) {
                throw new ServiceRuntimeException("校验不通过，操作类型非法");
            }
            if (StringUtils.isEmpty(domainItem.getDomain_id())) {
                throw new ServiceRuntimeException("校验不通过，主域名ID为空");
            }
            DomainEntity domainEntity = domainDao.queryDomainInfoById(domainItem.getDomain_id());
            if (null == domainEntity) {
                throw new ServiceRuntimeException("校验不通过，主域名不存在");
            }
            if (OperationTypeEnum.EDIT.getType().equals(domainItem.getOperation_type())
                    || OperationTypeEnum.DELETE.getType().equals(domainItem.getOperation_type())) {
                if (StringUtils.isEmpty(domainItem.getSub_domain_id())) {
                    throw new ServiceRuntimeException("校验不通过，子域名ID不存在");
                }
                boolean exists = editRecordIsExists(domainItem, domainEntity);
                if (!exists) {
                    throw new ServiceRuntimeException("校验不通过，解析记录不存在");
                }
            }
            if (OperationTypeEnum.ADD.getType().equals(domainItem.getOperation_type())
                    || OperationTypeEnum.EDIT.getType().equals(domainItem.getOperation_type())) {
                if (StringUtils.isEmpty(domainItem.getRecord_value())) {
                    throw new ServiceRuntimeException("校验不通过，记录值为空");
                }
                if (StringUtils.isEmpty(domainItem.getRecord_type())) {
                    throw new ServiceRuntimeException("校验不通过，记录类型为空");
                }
                if (domainItem.getSub_domain().toLowerCase(Locale.ROOT)
                        .endsWith(domainEntity.getDomainName().toLowerCase(Locale.ROOT))) {
                    throw new ServiceRuntimeException("校验不通过，子域名禁止拼接主域名");
                }
                log.debug("校验记录类型是否存在");
                verifyRecordType(domainItem, domainEntity);
                log.debug("判断是否存在相同子域名解析值");
                if (StringUtils.isEmpty(domainItem.getSub_domain())) {
                    throw new ServiceRuntimeException("校验不通过，子域名为空");
                }
                DescribeRecordFilterListRequest describeRecordFilterListRequest = new DescribeRecordFilterListRequest();
                describeRecordFilterListRequest.setDomain(domainEntity.getDomainName());
                describeRecordFilterListRequest.setDomainId(Long.parseLong(domainEntity.getDomainId()));
                describeRecordFilterListRequest.setSubDomain(domainItem.getSub_domain());
                describeRecordFilterListRequest.setRecordValue(domainItem.getRecord_value());
                describeRecordFilterListRequest.setIsExactSubDomain(true);
                DescribeRecordFilterListResponse recordFilterListResponse =
                        tencentCloudHttpConfig.getTencentDnspodClient(domainEntity.getAccountId())
                                .DescribeRecordFilterList(describeRecordFilterListRequest);
                boolean exists;
                if (StringUtils.isNotEmpty(domainItem.getSub_domain_id())) {
                    exists = Arrays.stream(recordFilterListResponse.getRecordList())
                            .anyMatch(item -> item.getStatus().equals("ENABLE")
                                    && item.getRecordId() != Long.parseLong(domainItem.getSub_domain_id()));
                } else {
                    exists = Arrays.stream(recordFilterListResponse.getRecordList())
                            .anyMatch(item -> item.getStatus().equals("ENABLE")
                                    && domainItem.getSub_domain().equals(item.getName()));
                }
                if (exists) {
                    throw new ServiceRuntimeException("校验不通过，存在相同的解析记录");
                }
            }
            if (OperationTypeEnum.ADD.getType().equals(domainItem.getOperation_type())
                    || OperationTypeEnum.EDIT.getType().equals(domainItem.getOperation_type())) {
                if (StringUtils.isEmpty(domainItem.getLine_id())) {
                    throw new ServiceRuntimeException("校验不通过，线路ID为空");
                }
                log.debug("校验线路是否合法");
                DescribeRecordLineListRequest describeRecordLineListRequest = new DescribeRecordLineListRequest();
                describeRecordLineListRequest.setDomain(domainEntity.getDomainName());
                describeRecordLineListRequest.setDomainId(
                        Long.parseLong(domainEntity.getDomainId()));
                describeRecordLineListRequest.setDomainGrade(domainEntity.getGrade());
                DescribeRecordLineListResponse recordLineListResponse =
                        tencentCloudHttpConfig.getTencentDnspodClient(domainEntity.getAccountId())
                                .DescribeRecordLineList(describeRecordLineListRequest);
                Optional<LineInfo> lineInfoOptional = Arrays.stream(
                                recordLineListResponse.getLineList())
                        .filter(item -> item.getLineId().equals(domainItem.getLine_id())).findAny();
                if (!lineInfoOptional.isPresent()) {
                    throw new ServiceRuntimeException("校验不通过，线路ID不合法");
                }
                domainItem.setRecord_line(lineInfoOptional.get().getName());
                if (domainItem.getTtl() < 1L || domainItem.getTtl() > 604800L) {
                    throw new ServiceRuntimeException("校验不通过，ttl范围[1-604800]");
                }
                if (domainItem.getRecord_type().equals("A")
                        || domainItem.getRecord_type().equals("CNAME")
                        || domainItem.getRecord_type().equals("AAAA")) {
                    if (domainItem.getWeight() < 0L || domainItem.getWeight() > 100L) {
                        throw new ServiceRuntimeException("校验不通过，权重范围[0-100]");
                    }
                }
                if (domainItem.getRecord_type().equals("MX")) {
                    if (domainItem.getMx() == 0L) {
                        throw new ServiceRuntimeException("校验不通过，MX类型必填优先级");
                    }
                    if (domainItem.getMx() < 1L || domainItem.getMx() > 20L) {
                        throw new ServiceRuntimeException("校验不通过，MX类型必填优先级，优先级范围[1-20]");
                    }
                }
                if (domainItem.getRecord_type().equals("HTTPS")
                        || domainItem.getRecord_type().equals("SVCB")) {
                    if (domainItem.getMx() < 0L || domainItem.getMx() > 20L) {
                        throw new ServiceRuntimeException("校验不通过，优先级范围[1-20]");
                    }
                }
                if (domainItem.getRecord_type().equals("MX") || domainItem.getRecord_type()
                        .equals("HTTPS")
                        || domainItem.getRecord_type().equals("SVCB")) {
                    domainItem.setWeight(0);
                } else {
                    domainItem.setMx(0);
                }
            }
        } catch (Exception e) {
            CommonUtil.setErrorMsg(commonResponse.getHeader(), e, "校验子域名变更参数异常，请联系管理员");
        }
        return commonResponse;
    }

    @PostMapping("/record/alter")
    public DNSRecordResponse alterRecord(@RequestBody DomainItem domainItem) {
        DNSRecordResponse dnsRecordResponse = new DNSRecordResponse();
        CommonResponse commonResponse = verifyRecord(domainItem);
        if (!Constants.SUCCESS.equals(commonResponse.getHeader().getCode())) {
            dnsRecordResponse.setHeader(commonResponse.getHeader());
            return dnsRecordResponse;
        }
        if ("test".equals(active)) {
            dnsRecordResponse.getHeader().setErrorMsg("测试环境禁止创建资源");
            return dnsRecordResponse;
        }
        try {
            UserEntity userEntity = userDao.queryUserByEmail(domainItem.getEmail());
            alter(domainItem, dnsRecordResponse, userEntity);
        } catch (Exception e) {
            CommonUtil.setErrorMsg(dnsRecordResponse.getHeader(), e, "子域名变更异常，请联系管理员");
        }
        return dnsRecordResponse;
    }

    private void alter(DomainItem domainItem, DNSRecordResponse dnsRecordResponse,
            UserEntity userEntity) {
        DomainEntity domainEntity = domainDao.queryDomainInfoById(domainItem.getDomain_id());
        DnspodClient dnspodClient = tencentCloudHttpConfig.getTencentDnspodClient(
                domainEntity.getAccountId());
        log.debug("新增子域名");
        if (OperationTypeEnum.ADD.getType().equals(domainItem.getOperation_type())) {
            try {
                addDnsRecord(domainItem, dnsRecordResponse, domainEntity, dnspodClient, userEntity);
            } catch (Exception e) {
                sendErrorFlyMessage(domainItem, userEntity, domainEntity, dnspodClient, e, "新增");
                throw new ServiceRuntimeException("创建子域名解析记录异常：" + e.getMessage());
            }
        }
        log.debug("编辑子域名");
        if (OperationTypeEnum.EDIT.getType().equals(domainItem.getOperation_type())) {
            try {
                editDnsRecord(domainItem, dnsRecordResponse, domainEntity, dnspodClient,
                        userEntity);
            } catch (Exception e) {
                sendErrorFlyMessage(domainItem, userEntity, domainEntity, dnspodClient, e, "修改");
                throw new ServiceRuntimeException("修改子域名解析记录异常：" + e.getMessage());
            }
        }
        log.debug("删除子域名");
        if (OperationTypeEnum.DELETE.getType().equals(domainItem.getOperation_type())) {
            try {
                deleteDnsRecord(domainItem, dnsRecordResponse, domainEntity, dnspodClient, userEntity);
            } catch (Exception e) {
                sendErrorFlyMessage(domainItem, userEntity, domainEntity, dnspodClient, e, "删除");
                throw new ServiceRuntimeException("删除子域名解析记录异常：" + e.getMessage());
            }
        }
    }

    private void sendErrorFlyMessage(DomainItem domainItem, UserEntity userEntity,
            DomainEntity domainEntity,
            DnspodClient dnspodClient, Exception e, String changeType) {
        try {
            DescribeRecordResponse recordResponse = getRecordResponse(domainEntity, dnspodClient,
                    domainItem);
            Map<String, Object> messageParam = new HashMap<>();
            messageParam.put("changeType", changeType);
            messageParam.put("domain",
                    domainEntity.getDomainId() + "/" + domainEntity.getDomainName());
            if (null != recordResponse.getRecordInfo().getId()) {
                messageParam.put("subDomain",
                        recordResponse.getRecordInfo().getId() + "/" + recordResponse.getRecordInfo().getSubDomain());
            } else {
                messageParam.put("subDomain", recordResponse.getRecordInfo().getSubDomain());
            }
            messageParam.put("recordValue", recordResponse.getRecordInfo().getValue());
            messageParam.put("recordType", recordResponse.getRecordInfo().getRecordType());
            messageParam.put("ttl", recordResponse.getRecordInfo().getTTL());
            messageParam.put("weight", recordResponse.getRecordInfo().getWeight());
            messageParam.put("mx", recordResponse.getRecordInfo().getMX());
            messageParam.put("lineType", recordResponse.getRecordInfo().getRecordLine());
            messageParam.put("createTime", SIMPLE_DATE_FORMAT.format(new Date()));
            messageParam.put("userName", userEntity.getUserName());
            messageParam.put("error", "变更解析记录异常：" + e.getMessage());
            String content = Utils.readTxtFile("/fly_book_template/create_sub_domain_error.txt");
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(userEntity.getEmail(), content);
        } catch (Exception ex) {
            log.error("发送消息通知异常", e);
        }
    }

    private ResourceChangeLog getResourceChangeLog(UserEntity userEntity) {
        ResourceChangeLog resourceChangeLog = new ResourceChangeLog();
        resourceChangeLog.setSource("API");
        resourceChangeLog.setChangeId(UUID.randomUUID().toString());
        resourceChangeLog.setChangeTime(new Date());
        resourceChangeLog.setResourceType("sub_domain");
        resourceChangeLog.setCreatorId(userEntity.getUserId());
        return resourceChangeLog;
    }

    private void deleteDnsRecord(DomainItem domainItem, DNSRecordResponse dnsRecordResponse,
            DomainEntity domainEntity, DnspodClient dnspodClient, UserEntity userEntity)
            throws TencentCloudSDKException {
        log.debug("查询变更前记录");
        DescribeRecordResponse recordResponse = getRecordResponse(domainEntity, dnspodClient, domainItem);
        DescribeRecordRequest describeRecordRequest = new DescribeRecordRequest();
        describeRecordRequest.setDomain(domainEntity.getDomainName());
        describeRecordRequest.setDomainId(Long.parseLong(domainEntity.getDomainId()));
        describeRecordRequest.setRecordId(Long.parseLong(domainItem.getSub_domain_id()));
        DescribeRecordResponse describeRecordResponse = dnspodClient.DescribeRecord(
                describeRecordRequest);
        RecordInfo recordInfo = describeRecordResponse.getRecordInfo();
        ModifyRecordRequest modifyRecordRequest = new ModifyRecordRequest();
        modifyRecordRequest.setDomain(domainEntity.getDomainName());
        modifyRecordRequest.setDomainId(recordInfo.getDomainId());
        modifyRecordRequest.setSubDomain(recordInfo.getSubDomain());
        modifyRecordRequest.setRecordId(recordInfo.getId());
        modifyRecordRequest.setRecordType(recordInfo.getRecordType());
        modifyRecordRequest.setRecordLineId(recordInfo.getRecordLineId());
        modifyRecordRequest.setRecordLine(recordInfo.getRecordLine());
        modifyRecordRequest.setValue(recordInfo.getValue());
        modifyRecordRequest.setMX(recordInfo.getMX());
        modifyRecordRequest.setTTL(recordInfo.getTTL());
        modifyRecordRequest.setWeight(recordInfo.getWeight());
        modifyRecordRequest.setStatus("DISABLE");
        ModifyRecordResponse modifyRecordResponse = dnspodClient.ModifyRecord(modifyRecordRequest);
        dnsRecordResponse.setRecordId(String.valueOf(modifyRecordResponse.getRecordId()));
        try {
            DomainRecordEntity recordEntity = domainDao.queryRecordInfo(
                    dnsRecordResponse.getRecordId());
            if (null != recordEntity) {
                log.debug("更新本地DNS解析记录状态");
                recordEntity.setRecordId(modifyRecordRequest.getRecordId().toString());
                recordEntity.setStatus("DISABLE");
                domainDao.updateRecordStatus(recordEntity);
                log.info("刪除子域名：{} {}", recordEntity, userEntity.getUserName());
            }
            ResourceChangeLog resourceChangeLog = getResourceChangeLog(userEntity);
            resourceChangeLog.setResourceId(modifyRecordRequest.getRecordId().toString());
            resourceChangeLog.setCloudId(domainEntity.getAccountId());
            resourceChangeLog.setChangeType("DELETE");
            resourceChangeLog.setResourceName(modifyRecordRequest.getSubDomain());
            resourceChangeLog.setBefore(JSON.toJSONString(recordResponse.getRecordInfo()));
            recordResponse.getRecordInfo().setMonitorStatus("DISABLE");
            resourceChangeLog.setAfter(JSON.toJSONString(recordResponse.getRecordInfo()));
            resourceChangeDao.insertResourceChange(resourceChangeLog);
        } catch (Exception e) {
            log.error("更新本地DNS解析记录状态异常", e);
        }
        try {
            Map<String, Object> messageParam = new HashMap<>();
            messageParam.put("changeType", "删除");
            messageParam.put("domain", domainEntity.getDomainId() + "/" + domainEntity.getDomainName());
            messageParam.put("subDomain", recordResponse.getRecordInfo().getId() + "/" + recordResponse.getRecordInfo().getSubDomain());
            messageParam.put("recordType", recordResponse.getRecordInfo().getRecordType());
            messageParam.put("recordValue", recordResponse.getRecordInfo().getValue());
            messageParam.put("lineType", recordResponse.getRecordInfo().getRecordLine());
            messageParam.put("ttl", recordResponse.getRecordInfo().getTTL());
            messageParam.put("weight", recordResponse.getRecordInfo().getWeight());
            messageParam.put("mx", recordResponse.getRecordInfo().getMX());
            messageParam.put("createTime", SIMPLE_DATE_FORMAT.format(new Date()));
            messageParam.put("userName", userEntity.getUserName());
            String content = Utils.readTxtFile("/fly_book_template/create_sub_domain_success.txt");
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(userEntity.getEmail(), content);
        } catch (Exception e) {
            log.error("发送消息通知异常", e);
        }
    }

    private void editDnsRecord(DomainItem domainItem, DNSRecordResponse dnsRecordResponse,
            DomainEntity domainEntity, DnspodClient dnspodClient, UserEntity userEntity)
            throws TencentCloudSDKException {
        log.debug("查询更新前记录");
        DescribeRecordResponse recordResponse = getRecordResponse(domainEntity, dnspodClient, domainItem);
        log.debug("更新记录");
        ModifyRecordRequest modifyRecordRequest = new ModifyRecordRequest();
        modifyRecordRequest.setDomain(domainEntity.getDomainName());
        modifyRecordRequest.setDomainId(Long.parseLong(domainEntity.getDomainId()));
        modifyRecordRequest.setSubDomain(domainItem.getSub_domain());
        modifyRecordRequest.setRecordId(Long.parseLong(domainItem.getSub_domain_id()));
        modifyRecordRequest.setRecordType(domainItem.getRecord_type());
        modifyRecordRequest.setRecordLineId(domainItem.getLine_id());
        modifyRecordRequest.setRecordLine(domainItem.getRecord_line());
        modifyRecordRequest.setValue(domainItem.getRecord_value());
        modifyRecordRequest.setMX(domainItem.getMx());
        modifyRecordRequest.setTTL(domainItem.getTtl());
        modifyRecordRequest.setWeight(domainItem.getWeight());
        ModifyRecordResponse modifyRecordResponse = dnspodClient.ModifyRecord(modifyRecordRequest);
        dnsRecordResponse.setRecordId(String.valueOf(modifyRecordResponse.getRecordId()));
        try {
            DomainRecordEntity recordEntity = domainDao.queryRecordInfo(
                    dnsRecordResponse.getRecordId());
            if (null != recordEntity) {
                log.debug("更新本地DNS解析记录");
                recordEntity.setRecordId(modifyRecordRequest.getRecordId().toString());
                recordEntity.setRecordName(modifyRecordRequest.getSubDomain());
                recordEntity.setDnsId(modifyRecordRequest.getDomainId().toString());
                recordEntity.setLineId(modifyRecordRequest.getRecordLineId());
                recordEntity.setTtl(modifyRecordRequest.getTTL());
                recordEntity.setType(modifyRecordRequest.getRecordType());
                recordEntity.setUpdateTime(new Date());
                recordEntity.setValue(modifyRecordRequest.getValue());
                recordEntity.setStatus("ENABLE");
                recordEntity.setWeight(modifyRecordRequest.getWeight());
                recordEntity.setMx(modifyRecordRequest.getMX());
                domainDao.updateRecord(recordEntity);
                log.info("更新子域名：{} {}", recordEntity, userEntity.getUserName());
            }
            log.debug("新增资源变更日志");
            ResourceChangeLog resourceChangeLog = getResourceChangeLog(userEntity);
            resourceChangeLog.setCloudId(domainEntity.getAccountId());
            resourceChangeLog.setResourceId(modifyRecordRequest.getRecordId().toString());
            resourceChangeLog.setChangeType("EDIT");
            resourceChangeLog.setResourceName(modifyRecordRequest.getSubDomain());
            resourceChangeLog.setBefore(JSON.toJSONString(recordResponse.getRecordInfo()));
            resourceChangeLog.setAfter(JSON.toJSONString(modifyRecordRequest));
            resourceChangeDao.insertResourceChange(resourceChangeLog);
            getLineName(domainItem, domainEntity, dnspodClient);
        } catch (Exception e) {
            log.error("更新本地DNS解析记录异常", e);
        }
        sendSubDomainAddMessage(domainItem, dnsRecordResponse, domainEntity, userEntity, "修改");
    }

    private DescribeRecordResponse getRecordResponse(DomainEntity domainEntity,
            DnspodClient dnspodClient, DomainItem domainItem) throws TencentCloudSDKException {
        if (StringUtils.isNotEmpty(domainItem.getSub_domain_id())) {
            DescribeRecordRequest describeRecordRequest = new DescribeRecordRequest();
            describeRecordRequest.setDomain(domainEntity.getDomainName());
            describeRecordRequest.setDomainId(Long.parseLong(domainEntity.getDomainId()));
            describeRecordRequest.setRecordId(Long.parseLong(domainItem.getSub_domain_id()));
            return dnspodClient.DescribeRecord(describeRecordRequest);
        } else {
            getLineName(domainItem, domainEntity, dnspodClient);
            DescribeRecordResponse describeRecordResponse = new DescribeRecordResponse();
            RecordInfo recordInfo = new RecordInfo();
            recordInfo.setSubDomain(domainItem.getSub_domain());
            recordInfo.setMX(domainItem.getMx());
            recordInfo.setRecordLine(domainItem.getRecord_line());
            recordInfo.setRecordLineId(domainItem.getLine_id());
            recordInfo.setValue(domainItem.getRecord_value());
            recordInfo.setTTL(domainItem.getTtl());
            recordInfo.setRecordType(domainItem.getRecord_type());
            recordInfo.setWeight(domainItem.getWeight());
            describeRecordResponse.setRecordInfo(recordInfo);
            return describeRecordResponse;
        }
    }

    private void addDnsRecord(DomainItem domainItem, DNSRecordResponse dnsRecordResponse,
            DomainEntity domainEntity, DnspodClient dnspodClient, UserEntity userEntity)
            throws TencentCloudSDKException {
        CreateRecordRequest createRecordRequest = new CreateRecordRequest();
        createRecordRequest.setDomain(domainEntity.getDomainName());
        createRecordRequest.setDomainId(Long.parseLong(domainEntity.getDomainId()));
        createRecordRequest.setSubDomain(domainItem.getSub_domain());
        createRecordRequest.setTTL(domainItem.getTtl());
        createRecordRequest.setRecordType(domainItem.getRecord_type());
        createRecordRequest.setRecordLineId(domainItem.getLine_id());
        createRecordRequest.setRecordLine(domainItem.getRecord_line());
        createRecordRequest.setValue(domainItem.getRecord_value());
        createRecordRequest.setMX(domainItem.getMx());
        createRecordRequest.setWeight(domainItem.getWeight());
        createRecordRequest.setStatus("ENABLE");
        createRecordRequest.setRemark("灵犀工单自动化创建");
        CreateRecordResponse createRecordResponse = dnspodClient.CreateRecord(createRecordRequest);
        dnsRecordResponse.setRecordId(String.valueOf(createRecordResponse.getRecordId()));
        try {
            log.debug("新增一条进入本地数据库");
            DomainRecordEntity dnsRecord = new DomainRecordEntity();
            dnsRecord.setRecordId(createRecordResponse.getRecordId().toString());
            dnsRecord.setRecordName(createRecordRequest.getSubDomain());
            dnsRecord.setDnsId(domainEntity.getDomainId());
            dnsRecord.setLineId(createRecordRequest.getRecordLineId());
            dnsRecord.setRemark(createRecordRequest.getRemark());
            dnsRecord.setType(createRecordRequest.getRecordType());
            dnsRecord.setUpdateTime(new Date());
            dnsRecord.setTtl(createRecordRequest.getTTL());
            dnsRecord.setValue(createRecordRequest.getValue());
            dnsRecord.setStatus(createRecordRequest.getStatus());
            dnsRecord.setWeight(createRecordRequest.getWeight());
            dnsRecord.setMx(createRecordRequest.getMX());
            dnsRecord.setRemark("灵犀工单自动化创建");
            domainDao.insertRecord(dnsRecord);
            log.info("新增子域名：{} {}", dnsRecord, userEntity.getUserName());
            ResourceChangeLog resourceChangeLog = getResourceChangeLog(userEntity);
            resourceChangeLog.setCloudId(domainEntity.getAccountId());
            resourceChangeLog.setResourceId(createRecordResponse.getRecordId().toString());
            resourceChangeLog.setChangeType("ADD");
            resourceChangeLog.setResourceName(createRecordRequest.getSubDomain());
            resourceChangeLog.setAfter(JSON.toJSONString(dnsRecord));
            resourceChangeDao.insertResourceChange(resourceChangeLog);
            getLineName(domainItem, domainEntity, dnspodClient);
        } catch (Exception e) {
            log.error("Add Local DNS Record Error", e);
        }
        log.info("发送创建成功消息");
        sendSubDomainAddMessage(domainItem, dnsRecordResponse, domainEntity, userEntity, "新增");
    }

    private void getLineName(DomainItem domainItem, DomainEntity domainEntity,
            DnspodClient dnspodClient) throws TencentCloudSDKException {
        DescribeRecordLineListRequest lineListRequest = new DescribeRecordLineListRequest();
        lineListRequest.setDomain(domainEntity.getDomainName());
        lineListRequest.setDomainId(Long.parseLong(domainEntity.getDomainId()));
        lineListRequest.setDomainGrade(domainEntity.getGrade());
        Optional<LineInfo> lineInfoOptional = Arrays.stream(dnspodClient.DescribeRecordLineList(lineListRequest)
                .getLineList()).filter(lineInfo -> lineInfo.getLineId().equals(domainItem.getLine_id())).findFirst();
        lineInfoOptional.ifPresent(lineInfo -> domainItem.setRecord_line(lineInfo.getName()));
    }

    private void sendSubDomainAddMessage(DomainItem domainItem, DNSRecordResponse dnsRecordResponse,
            DomainEntity domainEntity, UserEntity userEntity, String changeType) {
        try {
            Map<String, Object> messageParam = new HashMap<>();
            messageParam.put("changeType", changeType);
            messageParam.put("domain", domainEntity.getDomainId() + "/" + domainEntity.getDomainName());
            messageParam.put("subDomain", dnsRecordResponse.getRecordId() + "/" + domainItem.getSub_domain());
            messageParam.put("recordType", domainItem.getRecord_type());
            messageParam.put("recordValue", domainItem.getRecord_value());
            messageParam.put("lineType", domainItem.getRecord_line());
            messageParam.put("ttl", domainItem.getTtl());
            messageParam.put("weight", domainItem.getWeight());
            messageParam.put("mx", domainItem.getMx());
            messageParam.put("createTime", SIMPLE_DATE_FORMAT.format(new Date()));
            messageParam.put("userName", userEntity.getUserName());
            String content = Utils.readTxtFile("/fly_book_template/create_sub_domain_success.txt");
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(userEntity.getEmail(), content);
        } catch (Exception e) {
            log.error("发送消息通知异常", e);
        }
    }

    private void verifyRecordType(DomainItem domainItem, DomainEntity domainEntity)
            throws TencentCloudSDKException {
        DescribeRecordTypeRequest recordTypeRequest = new DescribeRecordTypeRequest();
        recordTypeRequest.setDomainGrade(domainEntity.getGrade());
        DescribeRecordTypeResponse recordTypeResponse =
                tencentCloudHttpConfig.getTencentDnspodClient(domainEntity.getAccountId())
                        .DescribeRecordType(recordTypeRequest);
        boolean noneMatchType = Arrays.stream(recordTypeResponse.getTypeList())
                .noneMatch(item -> item.equals(domainItem.getRecord_type()));
        if (noneMatchType) {
            throw new ServiceRuntimeException("校验不通过，记录类型不合法");
        }
        if ("A".equals(domainItem.getRecord_type()) && !isIpAddress(domainItem.getRecord_value())) {
            throw new ServiceRuntimeException("校验不通过，A类型，记录值必须为IPV4");
        }
        if ("AAAA".equals(domainItem.getRecord_type()) && !isIpv6Address(
                domainItem.getRecord_value())) {
            throw new ServiceRuntimeException("校验不通过，AAAA类型，记录值必须为IPV6");
        }
        if ("CNAME".equals(domainItem.getRecord_type()) && !isDomainName(
                domainItem.getRecord_value())) {
            throw new ServiceRuntimeException("校验不通过，CNAME类型，记录值必须为域名");
        }
    }

    private boolean editRecordIsExists(DomainItem domainItem, DomainEntity domainEntity) {
        DescribeRecordRequest describeRecordRequest = new DescribeRecordRequest();
        describeRecordRequest.setDomain(domainEntity.getDomainName());
        describeRecordRequest.setDomainId(Long.parseLong(domainEntity.getDomainId()));
        describeRecordRequest.setRecordId(Long.parseLong(domainItem.getSub_domain_id()));
        DescribeRecordResponse describeRecordResponse;
        try {
            describeRecordResponse = tencentCloudHttpConfig.getTencentDnspodClient(
                            domainEntity.getAccountId())
                    .DescribeRecord(describeRecordRequest);
        } catch (TencentCloudSDKException e) {
            log.debug("查询解析记录异常", e);
            throw new ServiceRuntimeException(
                    "校验失败，" + (e.getMessage().contains("记录编号错误") ? "子域名ID错误" : e.getMessage()));
        }
        if (null == describeRecordResponse.getRecordInfo()) {
            throw new ServiceRuntimeException("校验失败，子域名记录不存在");
        }
        if (describeRecordResponse.getRecordInfo().getEnabled() == 0) {
            throw new ServiceRuntimeException("校验失败，子域名记录已失效");
        }
        return true;
    }

    private boolean isIpAddress(String input) {
        boolean isValidIpAddress = false;
        String[] ipAddressInArray = input.split("\\.");

        if (ipAddressInArray.length == 4) {
            isValidIpAddress = true;
            for (String ipAddress : ipAddressInArray) {
                int octet = Integer.parseInt(ipAddress);
                if (octet < 0 || octet > 255) {
                    isValidIpAddress = false;
                    break;
                }
            }
        }
        return isValidIpAddress;
    }

    private boolean isIpv6Address(String input) {
        boolean isValidIpv6Address = false;
        String[] ipAddressInArray = input.split(":");
        if (ipAddressInArray.length == 8) {
            isValidIpv6Address = true;
            for (String ipAddress : ipAddressInArray) {
                if (ipAddress.isEmpty()) {
                    isValidIpv6Address = false;
                    break;
                }
                int hextet;
                try {
                    hextet = Integer.parseInt(ipAddress, 16);
                } catch (NumberFormatException e) {
                    isValidIpv6Address = false;
                    break;
                }
                if (hextet < 0 || hextet > 0xFFFF) {
                    isValidIpv6Address = false;
                    break;
                }
            }
        }
        return isValidIpv6Address;
    }

    private boolean isDomainName(String input) {
        boolean containsDomainName = false;
        String domainNamePattern = "((?!-)[A-Za-z0-9-]{1,63}(?<!-)\\.)+[A-Za-z]{2,6}";
        Pattern pattern = Pattern.compile(domainNamePattern);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            containsDomainName = true;
        }
        return containsDomainName;
    }

}
