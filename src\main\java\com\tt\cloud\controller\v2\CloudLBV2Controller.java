package com.tt.cloud.controller.v2;

import com.alibaba.fastjson.JSON;
import com.aliyun.resourcemanager20200331.models.ListResourceGroupsRequest;
import com.aliyun.resourcemanager20200331.models.ListResourceGroupsResponse;
import com.aliyun.resourcemanager20200331.models.ListResourceGroupsResponseBody;
import com.aliyun.slb20140515.models.DescribeZonesResponseBody;
import com.aliyun.vpc20160428.Client;
import com.aliyun.vpc20160428.models.DescribeVSwitchesResponseBody;
import com.huaweicloud.sdk.cce.v3.model.ListClustersRequest;
import com.huaweicloud.sdk.cce.v3.model.ListClustersResponse;
import com.huaweicloud.sdk.eip.v2.model.*;
import com.huaweicloud.sdk.eip.v2.model.CreatePublicipBandwidthOption.ShareTypeEnum;
import com.huaweicloud.sdk.eip.v2.model.CreatePublicipOption.IpVersionEnum;
import com.huaweicloud.sdk.eip.v3.model.AssociatePublicipsOption;
import com.huaweicloud.sdk.eip.v3.model.AssociatePublicipsOption.AssociateInstanceTypeEnum;
import com.huaweicloud.sdk.eip.v3.model.AssociatePublicipsRequest;
import com.huaweicloud.sdk.eip.v3.model.AssociatePublicipsRequestBody;
import com.huaweicloud.sdk.eip.v3.model.AssociatePublicipsResponse;
import com.huaweicloud.sdk.elb.v2.model.*;
import com.huaweicloud.sdk.elb.v3.ElbClient;
import com.huaweicloud.sdk.elb.v3.model.CreateLoadBalancerRequest;
import com.huaweicloud.sdk.elb.v3.model.CreateLoadBalancerResponse;
import com.huaweicloud.sdk.elb.v3.model.LoadBalancer;
import com.huaweicloud.sdk.elb.v3.model.*;
import com.huaweicloud.sdk.eps.v1.EpsClient;
import com.huaweicloud.sdk.eps.v1.model.ListEnterpriseProjectRequest;
import com.huaweicloud.sdk.eps.v1.model.ListEnterpriseProjectResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListRegionsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListRegionsResponse;
import com.huaweicloud.sdk.vpc.v2.VpcClient;
import com.huaweicloud.sdk.vpc.v2.model.ListSubnetsRequest;
import com.huaweicloud.sdk.vpc.v2.model.ListSubnetsResponse;
import com.huaweicloud.sdk.vpc.v2.model.ListVpcsRequest;
import com.huaweicloud.sdk.vpc.v2.model.ListVpcsResponse;
import com.tencentcloudapi.clb.v20180317.ClbClient;
import com.tencentcloudapi.clb.v20180317.models.*;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.dcdb.v20180411.DcdbClient;
import com.tencentcloudapi.dcdb.v20180411.models.DescribeProjectsRequest;
import com.tencentcloudapi.dcdb.v20180411.models.DescribeProjectsResponse;
import com.tencentcloudapi.region.v20220627.models.DescribeRegionsRequest;
import com.tencentcloudapi.region.v20220627.models.DescribeRegionsResponse;
import com.tencentcloudapi.tke.v20180525.models.DescribeClustersRequest;
import com.tencentcloudapi.tke.v20180525.models.DescribeClustersResponse;
import com.tencentcloudapi.vpc.v20170312.models.Filter;
import com.tencentcloudapi.vpc.v20170312.models.*;
import com.tt.cloud.bean.*;
import com.tt.cloud.bean.v2.LBItem;
import com.tt.cloud.bean.v2.*;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.config.HuaweiCloudHttpConfig;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.config.VolcEngineCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.*;
import com.tt.cloud.exception.ServiceRuntimeException;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.thread.AliLBCreateNoticeThread;
import com.tt.cloud.thread.HuaweiLBCreateNoticeThread;
import com.tt.cloud.thread.TencentLBCreateNoticeThread;
import com.tt.cloud.thread.VolcEngineLBCreateNoticeThread;
import com.tt.cloud.util.CommonUtil;
import com.tt.cloud.util.HttpUtils;
import com.tt.cloud.util.Utils;
import com.volcengine.ApiException;
import com.volcengine.alb.AlbApi;
import com.volcengine.alb.model.ZoneMappingForCreateLoadBalancerInput;
import com.volcengine.clb.ClbApi;
import com.volcengine.clb.model.DescribeLoadBalancerAttributesRequest;
import com.volcengine.clb.model.LoadBalancerForDescribeLoadBalancersOutput;
import com.volcengine.iam20210801.model.ListProjectsRequest;
import com.volcengine.iam20210801.model.ListProjectsResponse;
import com.volcengine.vke.model.FilterForListClustersInput;
import com.volcengine.vpc.model.DescribeVpcAttributesRequest;
import com.volcengine.vpc.model.DescribeVpcAttributesResponse;
import com.volcengine.vpc.model.SubnetForDescribeSubnetsOutput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * LB自动化创建接口
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/1/16 10:28
 */
@Slf4j
@RestController
@RequestMapping("/rest/v2/lb")
public class CloudLBV2Controller {
    @Value("${spring.profiles.active}")
    private String active;
    @Value("${cmdb.sync_url}")
    private String cmdbSyncUrl;
    @Value("${cmdb.Authorization}")
    private String authorization;
    @Resource
    private UserDao userDao;
    @Resource
    private ResourceChangeDao resourceChangeDao;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private HuaweiCloudHttpConfig huaweiCloudHttpConfig;
    @Resource
    private TencentCloudHttpConfig tencentCloudHttpConfig;
    @Resource
    private VolcEngineCloudHttpConfig volcEngineCloudHttpConfig;
    @Resource
    private AliCloudHttpConfig aliCloudHttpConfig;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private ExecutorService executorService;
    @Resource
    private HttpUtils httpUtils;
    @Resource
    private TerraformDao terraformDao;
    @Resource
    private LBDao lbDao;

    @PostMapping("/create/verify")
    public BatchVerifyResponse verifyLBList(@RequestBody LBCreateRequest lbCreateRequest) {
        BatchVerifyResponse batchVerifyResponse = new BatchVerifyResponse();
        try {
            if (lbCreateRequest.getList().isEmpty()) {
                throw new ServiceRuntimeException("校验不通过，变更数据为空");
            }
            for (LBItem lbItem : lbCreateRequest.getList()) {
                lbItem.setLb_name(lbItem.getLb_name().trim());
                CommonResponse commonResponse = verifyLB(lbItem);
                batchVerifyResponse.getVerify_results().add(commonResponse.getHeader());
            }
        } catch (Exception e) {
            ResponseHeader header = new ResponseHeader();
            CommonUtil.setErrorMsg(header, e, "LB创建参数校验异常，请联系管理员");
            batchVerifyResponse.getVerify_results().add(header);
        }
        return batchVerifyResponse;
    }

    @PostMapping("/create")
    public LBCreateResponse create(@RequestBody LBItem lbItem) {
        LBCreateResponse lbCreateResponse = new LBCreateResponse();
        CommonResponse commonResponse = verifyLB(lbItem);
        if (!Constants.SUCCESS.equals(commonResponse.getHeader().getCode())) {
            lbCreateResponse.setHeader(commonResponse.getHeader());
            return lbCreateResponse;
        }
        CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoByCloudId(lbItem.getCloud_id());
        lbItem.setLb_name(lbItem.getLb_name().trim());
        try {
            UserEntity userEntity = userDao.queryUserByEmail(lbItem.getEmail());
            if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                dealHuawei(lbItem, lbCreateResponse, cloudAccountEntity, userEntity);
            } else if (Constants.TENCENT_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                dealTencent(lbItem, lbCreateResponse, cloudAccountEntity, userEntity);
            } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                dealVolcEngine(lbItem, lbCreateResponse, cloudAccountEntity, userEntity);
            } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                dealAli(lbItem, lbCreateResponse, cloudAccountEntity, userEntity);
            }
        } catch (Exception e) {
            log.error("LB创建异常", e);
            CommonUtil.setErrorMsg(lbCreateResponse.getHeader(), e, "LB创建异常，请联系管理员");
            sendFlyBoolMessage(lbItem, cloudAccountEntity, Constants.FAIL, e.getMessage());
        }
        return lbCreateResponse;
    }

    private void dealAli(LBItem lbItem, LBCreateResponse lbCreateResponse, CloudAccountEntity cloudAccountEntity, UserEntity userEntity) throws Exception {
        ResourceChangeLog resourceChangeLog = getResourceChangeLog(cloudAccountEntity);
        Map<String, Object> afterChange = new HashMap<>();
        if ("clb".equals(lbItem.getLb_type())) {
            String lbId = createAliCloudClb(lbItem, cloudAccountEntity, afterChange, resourceChangeLog);
            lbCreateResponse.setLbId(lbId);
            sendFlyBoolMessage(lbItem, cloudAccountEntity, Constants.SUCCESS, StringUtils.EMPTY);
        } else if ("alb".equals(lbItem.getLb_type())) {
            String lbId = createAliCloudAlb(lbItem, cloudAccountEntity, afterChange, resourceChangeLog);
            lbCreateResponse.setLbId(lbId);
            sendFlyBoolMessage(lbItem, cloudAccountEntity, Constants.SUCCESS, StringUtils.EMPTY);
        } else if ("nlb".equals(lbItem.getLb_type())) {
            String lbId = createAliCloudNlb(lbItem, cloudAccountEntity, afterChange, resourceChangeLog);
            lbCreateResponse.setLbId(lbId);
            sendFlyBoolMessage(lbItem, cloudAccountEntity, Constants.SUCCESS, StringUtils.EMPTY);
        }
        sync(lbItem, cloudAccountEntity, userEntity, resourceChangeLog, afterChange);
    }

    private String createAliCloudNlb(LBItem lbItem, CloudAccountEntity cloudAccountEntity, Map<String, Object> afterChange, ResourceChangeLog resourceChangeLog) throws Exception {
        com.aliyun.nlb20220430.models.CreateLoadBalancerRequest request = new com.aliyun.nlb20220430.models.CreateLoadBalancerRequest();
        request.setLoadBalancerType("network");
        request.setLoadBalancerName(lbItem.getLb_name());
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            request.setAddressType("internet");
        } else if (NetTypeEnum.INTERNAL.getNetType().equals(lbItem.getNet_type())) {
            request.setAddressType("intranet");
        }
        request.setAddressIpVersion("ipv4");
        request.setVpcId(lbItem.getVpc_id());
        request.setZoneMappings(setAliCloudALBZoneMappings(lbItem, cloudAccountEntity, com.aliyun.nlb20220430.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestZoneMappings.class));
        com.aliyun.nlb20220430.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestLoadBalancerBillingConfig loadBalancerBillingConfig = new com.aliyun.nlb20220430.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestLoadBalancerBillingConfig();
        loadBalancerBillingConfig.setPayType("PostPay");
        request.setLoadBalancerBillingConfig(loadBalancerBillingConfig);
        request.setResourceGroupId(lbItem.getProject_id());
        request.setRegionId(lbItem.getRegion_id());
        com.aliyun.nlb20220430.models.CreateLoadBalancerResponse createLoadBalancerResponse =
                aliCloudHttpConfig.getNLBClient(cloudAccountEntity.getId(), lbItem.getRegion_id()).createLoadBalancer(request);
        afterChange.put("LB", JSON.toJSONString(createLoadBalancerResponse.body));
        resourceChangeLog.setResourceId(createLoadBalancerResponse.body.loadbalancerId);
        resourceChangeLog.setResourceName(lbItem.getLb_name());
        lbItem.setLb_id(createLoadBalancerResponse.body.loadbalancerId);
        return createLoadBalancerResponse.body.loadbalancerId;
    }

    private String createAliCloudAlb(LBItem lbItem, CloudAccountEntity cloudAccountEntity, Map<String, Object> afterChange, ResourceChangeLog resourceChangeLog) throws Exception {
        com.aliyun.alb20200616.models.CreateLoadBalancerRequest request = new com.aliyun.alb20200616.models.CreateLoadBalancerRequest();
        request.setVpcId(lbItem.getVpc_id());
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            request.setAddressType("internet");
        } else if (NetTypeEnum.INTERNAL.getNetType().equals(lbItem.getNet_type())) {
            request.setAddressType("intranet");
        }
        request.setLoadBalancerName(lbItem.getLb_name());
        request.setZoneMappings(setAliCloudALBZoneMappings(lbItem, cloudAccountEntity, com.aliyun.alb20200616.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestZoneMappings.class));
        request.setAddressAllocatedMode(lbItem.getIp_mode());
        request.setResourceGroupId(lbItem.getProject_id());
        request.setLoadBalancerEdition(lbItem.getFlavor_id());
        com.aliyun.alb20200616.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestLoadBalancerBillingConfig loadBalancerBillingConfig =
                new com.aliyun.alb20200616.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestLoadBalancerBillingConfig();
        loadBalancerBillingConfig.setPayType("PostPay");
        request.setLoadBalancerBillingConfig(loadBalancerBillingConfig);
        request.setAddressIpVersion("IPv4");
        com.aliyun.alb20200616.models.CreateLoadBalancerResponse createLoadBalancerResponse =
                aliCloudHttpConfig.getALBClient(cloudAccountEntity.getId(), lbItem.getRegion_id()).createLoadBalancer(request);
        afterChange.put("LB", JSON.toJSONString(createLoadBalancerResponse.body));
        resourceChangeLog.setResourceId(createLoadBalancerResponse.body.loadBalancerId);
        resourceChangeLog.setResourceName(lbItem.getLb_name());
        lbItem.setLb_id(createLoadBalancerResponse.body.loadBalancerId);
        return createLoadBalancerResponse.body.loadBalancerId;
    }

    private <T> List<T> setAliCloudALBZoneMappings(LBItem lbItem, CloudAccountEntity cloudAccountEntity, Class<T> clazz) throws Exception {
        List<T> zoneMappings = new ArrayList<>();
        // Get the VPC client
        Client vpvClient = aliCloudHttpConfig.getVPCClient(cloudAccountEntity.getId(), lbItem.getRegion_id());
        // Get the list of vSwitches
        List<DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch> vSwitches = getDescribeVSwitchesResponseBodyVSwitchesVSwitches(lbItem, vpvClient);
        // Filter the vSwitches based on the subnet IDs
        vSwitches = vSwitches.stream()
                .filter(vSwitch -> lbItem.getSubnet_ids().contains(vSwitch.getVSwitchId()))
                .collect(Collectors.toList());
        // Create the appropriate CreateLoadBalancerRequestZoneMappings object depending on the class type
        for (DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch vSwitch : vSwitches) {
            T balancerRequestZoneMappings = clazz.getDeclaredConstructor().newInstance();
            // Set the zone and vSwitch IDs
            if (balancerRequestZoneMappings instanceof com.aliyun.alb20200616.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestZoneMappings) {
                com.aliyun.alb20200616.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestZoneMappings zoneMapping = (com.aliyun.alb20200616.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestZoneMappings) balancerRequestZoneMappings;
                zoneMapping.setZoneId(vSwitch.getZoneId());
                zoneMapping.setVSwitchId(vSwitch.getVSwitchId());
            } else if (balancerRequestZoneMappings instanceof com.aliyun.nlb20220430.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestZoneMappings) {
                com.aliyun.nlb20220430.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestZoneMappings zoneMapping = (com.aliyun.nlb20220430.models.CreateLoadBalancerRequest.CreateLoadBalancerRequestZoneMappings) balancerRequestZoneMappings;
                zoneMapping.setZoneId(vSwitch.getZoneId());
                zoneMapping.setVSwitchId(vSwitch.getVSwitchId());
            }
            zoneMappings.add(balancerRequestZoneMappings);
        }
        return zoneMappings;
    }


    private String createAliCloudClb(LBItem lbItem, CloudAccountEntity cloudAccountEntity, Map<String, Object> afterChange, ResourceChangeLog resourceChangeLog) throws Exception {
        com.aliyun.slb20140515.models.CreateLoadBalancerRequest request = new com.aliyun.slb20140515.models.CreateLoadBalancerRequest();
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            request.setAddressType("internet");
            request.setInternetChargeType("paybytraffic");
        } else if (NetTypeEnum.INTERNAL.getNetType().equals(lbItem.getNet_type())) {
            request.setAddressType("intranet");
            request.setVpcId(lbItem.getVpc_id());
            request.setVSwitchId(lbItem.getSubnet_id());
        }
        request.setRegionId(lbItem.getRegion_id());
        request.setLoadBalancerName(lbItem.getLb_name());
        request.setMasterZoneId(lbItem.getZone_id());
        request.setSlaveZoneId(lbItem.getSlave_zone_id());
        request.setInstanceChargeType(lbItem.getCharge_mode());
        if (lbItem.getCharge_mode().equals("PayBySpec")) {
            request.setLoadBalancerSpec(lbItem.getFlavor_id());
        }
        request.setResourceGroupId(lbItem.getProject_id());
        request.setAddressIPVersion("ipv4");
        com.aliyun.slb20140515.models.CreateLoadBalancerResponse createLoadBalancerResponse = aliCloudHttpConfig.getSLBClient(cloudAccountEntity.getId(), lbItem.getRegion_id()).createLoadBalancer(request);
        afterChange.put("LB", JSON.toJSONString(createLoadBalancerResponse.body));
        resourceChangeLog.setResourceId(createLoadBalancerResponse.body.loadBalancerId);
        resourceChangeLog.setResourceName(lbItem.getLb_name());
        lbItem.setLb_id(createLoadBalancerResponse.body.loadBalancerId);
        return createLoadBalancerResponse.body.loadBalancerId;
    }

    private void dealHuawei(LBItem lbItem, LBCreateResponse lbCreateResponse, CloudAccountEntity cloudAccountEntity, UserEntity userEntity) {
        ResourceChangeLog resourceChangeLog = getResourceChangeLog(cloudAccountEntity);
        Map<String, Object> afterChange = new HashMap<>();
        log.debug("华为云创建独享型LB");
        if (LbTypeEnum.EXCLUSIVE.getLBType().equals(lbItem.getLb_type())) {
            LoadBalancer loadBalancer = createV3ELB(lbItem, cloudAccountEntity, lbCreateResponse);
            afterChange.put("LB", loadBalancer);
            lbItem.setLb_id(loadBalancer.getId());
            resourceChangeLog.setResourceId(loadBalancer.getId());
            resourceChangeLog.setResourceName(loadBalancer.getName());
            sendFlyBoolMessage(lbItem, cloudAccountEntity, Constants.SUCCESS, StringUtils.EMPTY);
        } else if (LbTypeEnum.SHARED.getLBType().equals(lbItem.getLb_type())) {
            LoadbalancerResp loadbalancerResp = createV2ELB(lbItem, lbCreateResponse, cloudAccountEntity);
            afterChange.put("LB", loadbalancerResp);
            lbItem.setLb_id(loadbalancerResp.getId());
            resourceChangeLog.setResourceId(loadbalancerResp.getId());
            resourceChangeLog.setResourceName(loadbalancerResp.getName());
            sendFlyBoolMessage(lbItem, cloudAccountEntity, Constants.SUCCESS, StringUtils.EMPTY);
        }
        PublicipCreateResp publicipCreateResp = createV2EIP(lbItem, cloudAccountEntity, lbCreateResponse);
        if (null != publicipCreateResp) {
            afterChange.put("EIP", publicipCreateResp);
        }
        sync(lbItem, cloudAccountEntity, userEntity, resourceChangeLog, afterChange);
    }

    private String addTerraform(LBItem lbItem, CloudAccountEntity cloudAccountEntity, UserEntity userEntity) {
        try {
            TerraformEntity terraformEntity = new TerraformEntity();
            terraformEntity.setTrackId(UUID.randomUUID().toString());
            terraformEntity.setName(lbItem.getLb_name());
            terraformEntity.setCloud(cloudAccountEntity.getCloudType());
            terraformEntity.setAccountId(cloudAccountEntity.getId());
            terraformEntity.setAction("create");
            terraformEntity.setCreateTime(new Date());
            terraformEntity.setCreatorId(userEntity.getUserId());
            terraformEntity.setLastModifyTime(new Date());
            terraformEntity.setLastModifyUserId(userEntity.getUserId());
            terraformEntity.setType("host-LB");
            terraformEntity.setEndTime(new Date());
            terraformEntity.setProjectId(lbItem.getProject_id());
            terraformEntity.setRegion(lbItem.getRegion_id());
            terraformEntity.setResource_id(lbItem.getLb_id());
            terraformEntity.setSource("API");
            terraformEntity.setStatus("3");
            terraformDao.insertTerraformInfo(terraformEntity);
            CloudLoadBalanceInfo cloudLoadBalanceInfo = new CloudLoadBalanceInfo();
            if (null != lbItem.getZone_ids()) {
                cloudLoadBalanceInfo.setAz(JSON.toJSONString(lbItem.getZone_ids()));
            }
            cloudLoadBalanceInfo.setProjectId(lbItem.getProject_id());
            cloudLoadBalanceInfo.setBandwidth_size(lbItem.getBandwidth_size());
            cloudLoadBalanceInfo.setLoadBalance_type(lbItem.getLb_type());
            cloudLoadBalanceInfo.setL7_flavor_id(lbItem.getL7_flavor_id());
            cloudLoadBalanceInfo.setL4_flavor_id(lbItem.getL4_flavor_id());
            cloudLoadBalanceInfo.setLoadbalancer_name(lbItem.getLb_name());
            cloudLoadBalanceInfo.setNet_type(lbItem.getNet_type());

            if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
                    cloudLoadBalanceInfo.setNet_type("public");
                } else {
                    cloudLoadBalanceInfo.setNet_type("intranet");
                }
            }
            if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
                    cloudLoadBalanceInfo.setNet_type("public");
                } else {
                    cloudLoadBalanceInfo.setNet_type("private");
                }
            }
            cloudLoadBalanceInfo.setTrackId(terraformEntity.getTrackId());
            cloudLoadBalanceInfo.setVpc_id(lbItem.getVpc_id());
            cloudLoadBalanceInfo.setSubnet_id(lbItem.getSubnet_id());
            if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                if ("alb".equals(lbItem.getLb_type())) {
                    cloudLoadBalanceInfo.setSubnet_id(JSON.toJSONString(lbItem.getSubnet_ids()));
                }
                cloudLoadBalanceInfo.setL7_flavor_id(lbItem.getFlavor_id());
            }
            if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                if ("alb".equals(lbItem.getLb_type()) || "clb".equals(lbItem.getLb_type())) {
                    cloudLoadBalanceInfo.setSubnet_id(JSON.toJSONString(lbItem.getSubnet_ids()));
                }
            }
            lbDao.insertCloudLoadBalance(cloudLoadBalanceInfo);
            return terraformEntity.getTrackId();
        } catch (Exception e) {
            log.error("导入云泽系统异常", e);
        }
        return StringUtils.EMPTY;
    }

    private void syncCmdb(LBItem lbItem, CloudAccountEntity cloudAccountEntity) {
        try {
            if (StringUtils.isNotEmpty(lbItem.getLb_id())) {
                String syncParams = "{\n" + "  \"id\": 0,\n" + "  \"jsonrpc\": \"2.0\",\n" + "  \"method\": \"sync_specific_resource\",\n" + "  \"params\": {\n" + "    \"cloud\": \"" + cloudAccountEntity.getCloudType() + "\",\n" + "    \"account\": \"" + lbItem.getCloud_id() + "\",\n" + "    \"resource_type\":\"load_balancer\",\n" + "    \"region_id\":\"" + lbItem.getRegion_id() + "\",\n" + "    \"instance_ids\":[\"" + lbItem.getLb_id() + "\"]\n" + "  }\n" + "}";
                List<Header> headerList = new ArrayList<>();
                Header authHeader = new BasicHeader("Authorization", authorization);
                Header typeHeader = new BasicHeader("Content-Type", "application/json");
                headerList.add(authHeader);
                headerList.add(typeHeader);
                String result = httpUtils.doPost(cmdbSyncUrl, syncParams, headerList);
                log.info("同步LB至CMDB结果:{}", result);
            }
        } catch (Exception e) {
            log.error("同步LB至CMDB异常", e);
        }
    }

    private void sendFlyBoolMessage(LBItem lbItem, CloudAccountEntity cloudAccountEntity, String resultCode, String resultMsg) {
        lbItem.setResultCode(resultCode);
        lbItem.setResultMsg(resultMsg);
        lbItem.setUsername(userDao.queryUserByEmail(lbItem.getEmail()).getUserName());
        if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
            HuaweiLBCreateNoticeThread huaweiLBCreateSuccessNoticeThread = new HuaweiLBCreateNoticeThread(lbItem, cloudAccountEntity, flyBookService, applicationContext, huaweiCloudHttpConfig);
            executorService.submit(huaweiLBCreateSuccessNoticeThread);
        } else if (Constants.TENCENT_CLOUD.equals(cloudAccountEntity.getCloudType())) {
            TencentLBCreateNoticeThread tencentLBCreateNoticeThread = new TencentLBCreateNoticeThread(lbItem, cloudAccountEntity, flyBookService, applicationContext, tencentCloudHttpConfig);
            executorService.submit(tencentLBCreateNoticeThread);
        } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
            VolcEngineLBCreateNoticeThread volcEngineLBCreateNoticeThread = new VolcEngineLBCreateNoticeThread(lbItem, cloudAccountEntity, flyBookService, applicationContext, volcEngineCloudHttpConfig);
            executorService.submit(volcEngineLBCreateNoticeThread);
        } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
            AliLBCreateNoticeThread aliLBCreateNoticeThread = new AliLBCreateNoticeThread(lbItem, cloudAccountEntity, flyBookService, applicationContext, aliCloudHttpConfig);
            executorService.submit(aliLBCreateNoticeThread);
        }
    }

    private void dealVolcEngine(LBItem lbItem, LBCreateResponse lbCreateResponse, CloudAccountEntity cloudAccountEntity, UserEntity userEntity) throws ApiException {
        ResourceChangeLog resourceChangeLog = getResourceChangeLog(cloudAccountEntity);
        Map<String, Object> afterChange = new HashMap<>();
        if ("clb".equals(lbItem.getLb_type())) {
            log.debug("火山云创建clb");
            createVolcEngineClb(lbItem, cloudAccountEntity, afterChange, resourceChangeLog, lbCreateResponse);
        }
        if ("alb".equals(lbItem.getLb_type())) {
            log.debug("火山云创建alb");
            createVolcEngineAlb(lbItem, cloudAccountEntity, afterChange, resourceChangeLog, lbCreateResponse);
        }
        sync(lbItem, cloudAccountEntity, userEntity, resourceChangeLog, afterChange);
    }

    private void createVolcEngineClb(LBItem lbItem, CloudAccountEntity cloudAccountEntity, Map<String, Object> afterChange, ResourceChangeLog resourceChangeLog, LBCreateResponse lbCreateResponse) throws ApiException {
        ClbApi clbApi = volcEngineCloudHttpConfig.getVolcEngineClbApi(cloudAccountEntity.getId(), lbItem.getRegion_id());
        com.volcengine.clb.model.CreateLoadBalancerRequest createLoadBalancerRequest = new com.volcengine.clb.model.CreateLoadBalancerRequest();
        createLoadBalancerRequest.setLoadBalancerName(lbItem.getLb_name());
        createLoadBalancerRequest.setDescription("灵犀平台工单-自动化创建");
        createLoadBalancerRequest.setProjectName(lbItem.getProject_id());
        createLoadBalancerRequest.setVpcId(lbItem.getVpc_id());
        createLoadBalancerRequest.setSubnetId(lbItem.getSubnet_id());
        createLoadBalancerRequest.setRegionId(lbItem.getRegion_id());
        createLoadBalancerRequest.setLoadBalancerBillingType(2);
        createLoadBalancerRequest.setLoadBalancerSpec(lbItem.getFlavor_id());

        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            createLoadBalancerRequest.setType("public");
            com.volcengine.clb.model.EipBillingConfigForCreateLoadBalancerInput eipBillingConfig = new com.volcengine.clb.model.EipBillingConfigForCreateLoadBalancerInput();
            eipBillingConfig.setBandwidth(lbItem.getBandwidth_size());
            eipBillingConfig.setEipBillingType(3);
            eipBillingConfig.setISP("BGP");
            createLoadBalancerRequest.setEipBillingConfig(eipBillingConfig);
        } else {
            createLoadBalancerRequest.setType("private");
        }
        com.volcengine.clb.model.CreateLoadBalancerResponse createLoadBalancerResponse = clbApi.createLoadBalancer(createLoadBalancerRequest);
        DescribeLoadBalancerAttributesRequest loadBalancerAttributesRequest = new DescribeLoadBalancerAttributesRequest();
        loadBalancerAttributesRequest.setLoadBalancerId(createLoadBalancerResponse.getLoadBalancerId());
        com.volcengine.clb.model.DescribeLoadBalancerAttributesResponse loadBalancerAttributesResponse = clbApi.describeLoadBalancerAttributes(loadBalancerAttributesRequest);

        lbCreateResponse.setLbId(loadBalancerAttributesResponse.getLoadBalancerId());

        afterChange.put("LB", loadBalancerAttributesResponse);
        lbItem.setLb_id(loadBalancerAttributesResponse.getLoadBalancerId());
        resourceChangeLog.setResourceId(loadBalancerAttributesResponse.getLoadBalancerId());
        resourceChangeLog.setResourceName(loadBalancerAttributesResponse.getLoadBalancerName());
        sendFlyBoolMessage(lbItem, cloudAccountEntity, Constants.SUCCESS, StringUtils.EMPTY);
    }

    private void createVolcEngineAlb(LBItem lbItem, CloudAccountEntity cloudAccountEntity, Map<String, Object> afterChange, ResourceChangeLog resourceChangeLog, LBCreateResponse lbCreateResponse) throws ApiException {
        AlbApi albApi = volcEngineCloudHttpConfig.getVolcEngineAlbApi(cloudAccountEntity.getId(), lbItem.getRegion_id());
        com.volcengine.alb.model.CreateLoadBalancerRequest createLoadBalancerRequest = new com.volcengine.alb.model.CreateLoadBalancerRequest();
        createLoadBalancerRequest.setLoadBalancerName(lbItem.getLb_name());
        createLoadBalancerRequest.setDescription("灵犀平台工单-自动化创建");
        createLoadBalancerRequest.setProjectName(lbItem.getProject_id());
        createLoadBalancerRequest.setVpcId(lbItem.getVpc_id());
        createLoadBalancerRequest.setRegionId(lbItem.getRegion_id());
        createLoadBalancerRequest.setLoadBalancerBillingType(1);

        List<ZoneMappingForCreateLoadBalancerInput> zoneMappings = new ArrayList<>();

        com.volcengine.vpc.model.DescribeSubnetsResponse describeSubnetsResponse = getSubnetsResponse(lbItem, cloudAccountEntity);
        List<SubnetForDescribeSubnetsOutput> subnets = describeSubnetsResponse.getSubnets().stream().filter(subnetForDescribeSubnetsOutput -> lbItem.getSubnet_ids().contains(subnetForDescribeSubnetsOutput.getSubnetId())).collect(Collectors.toList());
        for (SubnetForDescribeSubnetsOutput subnet : subnets) {
            ZoneMappingForCreateLoadBalancerInput zoneMapping = new ZoneMappingForCreateLoadBalancerInput();
            zoneMapping.setSubnetId(subnet.getSubnetId());
            zoneMapping.setZoneId(subnet.getZoneId());
            zoneMappings.add(zoneMapping);
        }
        createLoadBalancerRequest.setZoneMappings(zoneMappings);

        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            createLoadBalancerRequest.setType("public");
            com.volcengine.alb.model.EipBillingConfigForCreateLoadBalancerInput eipBillingConfig = new com.volcengine.alb.model.EipBillingConfigForCreateLoadBalancerInput();
            eipBillingConfig.setBandwidth(lbItem.getBandwidth_size());
            eipBillingConfig.setEipBillingType(3);
            eipBillingConfig.setISP("BGP");
            createLoadBalancerRequest.setEipBillingConfig(eipBillingConfig);
        } else {
            createLoadBalancerRequest.setType("private");
        }
        com.volcengine.alb.model.CreateLoadBalancerResponse createLoadBalancerResponse = albApi.createLoadBalancer(createLoadBalancerRequest);
        com.volcengine.alb.model.DescribeLoadBalancerAttributesRequest loadBalancerAttributesRequest = new com.volcengine.alb.model.DescribeLoadBalancerAttributesRequest();
        loadBalancerAttributesRequest.setLoadBalancerId(createLoadBalancerResponse.getLoadBalancerId());
        com.volcengine.alb.model.DescribeLoadBalancerAttributesResponse loadBalancerAttributesResponse = albApi.describeLoadBalancerAttributes(loadBalancerAttributesRequest);

        lbCreateResponse.setLbId(loadBalancerAttributesResponse.getLoadBalancerId());

        afterChange.put("LB", loadBalancerAttributesResponse);
        lbItem.setLb_id(loadBalancerAttributesResponse.getLoadBalancerId());
        resourceChangeLog.setResourceId(loadBalancerAttributesResponse.getLoadBalancerId());
        resourceChangeLog.setResourceName(loadBalancerAttributesResponse.getLoadBalancerName());
        sendFlyBoolMessage(lbItem, cloudAccountEntity, Constants.SUCCESS, StringUtils.EMPTY);
    }

    private void sync(LBItem lbItem, CloudAccountEntity cloudAccountEntity, UserEntity userEntity, ResourceChangeLog resourceChangeLog, Map<String, Object> afterChange) {
        syncCmdb(lbItem, cloudAccountEntity);
        String trackId = addTerraform(lbItem, cloudAccountEntity, userEntity);
        try {
            resourceChangeLog.setAfter(JSON.toJSONString(afterChange));
            resourceChangeLog.setCreatorId(userEntity.getUserId());
            resourceChangeLog.setChangeTime(new Date());
            resourceChangeLog.setResourceType("LB");
            resourceChangeLog.setTrackId(trackId);
            resourceChangeDao.insertResourceChange(resourceChangeLog);
        } catch (Exception e) {
            log.error("资源变更日志新增异常", e);
        }
    }

    private ResourceChangeLog getResourceChangeLog(CloudAccountEntity cloudAccountEntity) {
        ResourceChangeLog resourceChangeLog = new ResourceChangeLog();
        resourceChangeLog.setChangeId(UUID.randomUUID().toString());
        resourceChangeLog.setCloudId(cloudAccountEntity.getId());
        resourceChangeLog.setChangeType("ADD");
        resourceChangeLog.setSource("API");
        return resourceChangeLog;
    }

    private void dealTencent(LBItem lbItem, LBCreateResponse lbCreateResponse, CloudAccountEntity cloudAccountEntity, UserEntity userEntity) throws TencentCloudSDKException {
        ResourceChangeLog resourceChangeLog = getResourceChangeLog(cloudAccountEntity);
        Map<String, Object> afterChange = new HashMap<>();
        log.debug("腾讯云创建LB");
        com.tencentcloudapi.clb.v20180317.models.CreateLoadBalancerRequest createLoadBalancerRequest = getCreateTencentCloudLoadBalancerRequest(lbItem);
        ClbClient clbClient = tencentCloudHttpConfig.getTencentClbClient(cloudAccountEntity.getId(), lbItem.getRegion_id());
        com.tencentcloudapi.clb.v20180317.models.CreateLoadBalancerResponse createLoadBalancer = clbClient.CreateLoadBalancer(createLoadBalancerRequest);
        log.info("腾讯云LB创建完毕：{}", JSON.toJSONString(createLoadBalancer.getLoadBalancerIds()));
        lbCreateResponse.setLbId(StringUtils.join(createLoadBalancer.getLoadBalancerIds(), ","));
        DescribeLoadBalancersRequest loadBalancersRequest = new DescribeLoadBalancersRequest();
        loadBalancersRequest.setLoadBalancerIds(createLoadBalancer.getLoadBalancerIds());
        DescribeLoadBalancersResponse loadBalancersResponse = clbClient.DescribeLoadBalancers(loadBalancersRequest);
        lbItem.setLb_id(loadBalancersResponse.getLoadBalancerSet()[0].getLoadBalancerId());
        syncCmdb(lbItem, cloudAccountEntity);
        String trackId = addTerraform(lbItem, cloudAccountEntity, userEntity);
        try {
            afterChange.put("LB", loadBalancersResponse.getLoadBalancerSet());
            resourceChangeLog.setAfter(JSON.toJSONString(afterChange));
            resourceChangeLog.setCreatorId(userEntity.getUserId());
            resourceChangeLog.setChangeTime(new Date());
            resourceChangeLog.setResourceType("LB");
            resourceChangeLog.setTrackId(trackId);
            resourceChangeLog.setResourceId(loadBalancersResponse.getLoadBalancerSet()[0].getLoadBalancerId());
            resourceChangeLog.setResourceName(loadBalancersResponse.getLoadBalancerSet()[0].getLoadBalancerName());
            resourceChangeDao.insertResourceChange(resourceChangeLog);
        } catch (Exception e) {
            log.error("新增资源变更日志异常", e);
        }
        sendFlyBoolMessage(lbItem, cloudAccountEntity, Constants.SUCCESS, StringUtils.EMPTY);
    }

    private com.tencentcloudapi.clb.v20180317.models.CreateLoadBalancerRequest getCreateTencentCloudLoadBalancerRequest(LBItem lbItem) {
        com.tencentcloudapi.clb.v20180317.models.CreateLoadBalancerRequest createLoadBalancerRequest = new com.tencentcloudapi.clb.v20180317.models.CreateLoadBalancerRequest();
        createLoadBalancerRequest.setLoadBalancerName(lbItem.getLb_name());
        createLoadBalancerRequest.setLoadBalancerType(lbItem.getNet_type());
        createLoadBalancerRequest.setProjectId(Long.parseLong(lbItem.getProject_id()));
        createLoadBalancerRequest.setVpcId(lbItem.getVpc_id());
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            createLoadBalancerRequest.setZoneId(lbItem.getZone_id());
            InternetAccessible internetAccessible = new InternetAccessible();
            internetAccessible.setInternetChargeType("TRAFFIC_POSTPAID_BY_HOUR");
            internetAccessible.setInternetMaxBandwidthOut((long) lbItem.getBandwidth_size());
            createLoadBalancerRequest.setInternetAccessible(internetAccessible);
            if (lbItem.getIp_version() == 6) {
                createLoadBalancerRequest.setAddressIPVersion("IPv6FullChain");
            } else {
                createLoadBalancerRequest.setAddressIPVersion("IPV4");
            }
        } else {
            createLoadBalancerRequest.setSubnetId(lbItem.getSubnet_id());
        }
        return createLoadBalancerRequest;
    }

    private LoadbalancerResp createV2ELB(LBItem lbItem, LBCreateResponse lbCreateResponse, CloudAccountEntity cloudAccountEntity) {
        CreateLoadbalancerRequest createLoadbalancerRequest = new CreateLoadbalancerRequest();
        CreateLoadbalancerRequestBody loadbalancerRequestBody = new CreateLoadbalancerRequestBody();
        CreateLoadbalancerReq loadbalancerReq = new CreateLoadbalancerReq();
        loadbalancerReq.setName(lbItem.getLb_name());
        loadbalancerReq.setDescription("灵犀平台工单-自动化创建");
        loadbalancerReq.setVipSubnetId(lbItem.getSubnet_id());
        if (StringUtils.isNotEmpty(lbItem.getProject_id()) && !"0".equals(lbItem.getProject_id())) {
            loadbalancerReq.setEnterpriseProjectId(lbItem.getProject_id());
        }
        loadbalancerRequestBody.setLoadbalancer(loadbalancerReq);
        createLoadbalancerRequest.setBody(loadbalancerRequestBody);
        CreateLoadbalancerResponse createLoadbalancerResponse = huaweiCloudHttpConfig.getHuaweiElbV2Client(cloudAccountEntity.getId(), lbItem.getRegion_id()).createLoadbalancer(createLoadbalancerRequest);
        log.info("华为云共享型LB创建成功：{}", JSON.toJSONString(createLoadbalancerResponse.getLoadbalancer()));
        lbCreateResponse.setLbId(createLoadbalancerResponse.getLoadbalancer().getId());
        return createLoadbalancerResponse.getLoadbalancer();
    }

    private void bindELBAndEIP(LBItem lbItem, LBCreateResponse lbCreateResponse, CloudAccountEntity cloudAccountEntity) {
        log.info("LB与EIP进行绑定");
        AssociatePublicipsRequest associatePublicipsRequest = new AssociatePublicipsRequest();
        associatePublicipsRequest.setPublicipId(lbCreateResponse.getEipId());
        AssociatePublicipsRequestBody associatePublicipsRequestBody = new AssociatePublicipsRequestBody();
        AssociatePublicipsOption associatePublicipsOption = new AssociatePublicipsOption();
        associatePublicipsOption.setAssociateInstanceId(lbCreateResponse.getLbId());
        associatePublicipsOption.setAssociateInstanceType(AssociateInstanceTypeEnum.ELB);
        associatePublicipsRequestBody.setPublicip(associatePublicipsOption);
        associatePublicipsRequest.setBody(associatePublicipsRequestBody);
        AssociatePublicipsResponse associatePublicipsResponse = huaweiCloudHttpConfig.getHuaweiEipV3Client(cloudAccountEntity.getId(), lbItem.getRegion_id()).associatePublicips(associatePublicipsRequest);
        log.info("LB与EIP绑定成功：{}", JSON.toJSONString(associatePublicipsResponse.getPublicip()));
    }

    private CommonResponse verifyLB(LBItem lbItem) {
        CommonResponse commonResponse = new CommonResponse();
        try {
            log.info("校验LB自动化创建参数：{}", JSON.toJSONString(lbItem));
            CloudAccountEntity cloudAccountEntity = baseVerify(lbItem);
            if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                log.debug("华为云参数校验");
                verifyHuaweiCloudLBCreateParameters(lbItem, cloudAccountEntity);
            } else if (Constants.TENCENT_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                log.debug("腾讯云参数校验");
                verifyTencentCloudLBCreateParameters(lbItem, cloudAccountEntity);
            } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                log.debug("火山云参数校验");
                verifyVolcEngineLBCreateParameters(lbItem, cloudAccountEntity);
            } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                log.debug("阿里云参数校验");
                verifyAliCloudLBCreateParameters(lbItem, cloudAccountEntity);
            }
        } catch (Exception e) {
            CommonUtil.setErrorMsg(commonResponse.getHeader(), e, "LB创建参数校验异常，请联系管理员");
        }
        return commonResponse;
    }

    private void verifyAliCloudLBCreateParameters(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws Exception {
        verifyAliLBBase(lbItem);
        verifyNetType(lbItem);
        verifyAliCloudProject(lbItem, cloudAccountEntity);
        if ("clb".equals(lbItem.getLb_type())) {
            verifyAliCloudClb(lbItem, cloudAccountEntity);
            com.aliyun.slb20140515.models.DescribeLoadBalancersRequest describeLoadBalancersRequest = new com.aliyun.slb20140515.models.DescribeLoadBalancersRequest();
            describeLoadBalancersRequest.setLoadBalancerName(lbItem.getLb_name());
            describeLoadBalancersRequest.setRegionId(lbItem.getRegion_id());
            com.aliyun.slb20140515.models.DescribeLoadBalancersResponse describeLoadBalancersResponse = aliCloudHttpConfig.getSLBClient(cloudAccountEntity.getId(), lbItem.getRegion_id()).describeLoadBalancers(describeLoadBalancersRequest);
            if (describeLoadBalancersResponse.body.loadBalancers.loadBalancer.stream().anyMatch(loadBalancer -> loadBalancer.loadBalancerName.equals(lbItem.getLb_name()))) {
                throw new ServiceRuntimeException("校验不通过，LB名称已存在");
            }
        } else if ("alb".equals(lbItem.getLb_type())) {
            if (StringUtils.isEmpty(lbItem.getFlavor_id())) {
                throw new ServiceRuntimeException("校验不通过，请选择规格");
            }
            verifyAlbOrNlb(lbItem, cloudAccountEntity);
            com.aliyun.alb20200616.models.ListLoadBalancersRequest listLoadBalancersRequest = new com.aliyun.alb20200616.models.ListLoadBalancersRequest();
            listLoadBalancersRequest.setLoadBalancerNames(Collections.singletonList(lbItem.getLb_name()));
            com.aliyun.alb20200616.models.ListLoadBalancersResponse listLoadBalancersResponse = aliCloudHttpConfig.getALBClient(cloudAccountEntity.getId(), lbItem.getRegion_id()).listLoadBalancers(listLoadBalancersRequest);
            if (listLoadBalancersResponse.body.loadBalancers.stream().anyMatch(loadBalancer -> loadBalancer.loadBalancerName.equals(lbItem.getLb_name()))) {
                throw new ServiceRuntimeException("校验不通过，LB名称已存在");
            }
        } else if ("nlb".equals(lbItem.getLb_type())) {
            verifyAlbOrNlb(lbItem, cloudAccountEntity);
            com.aliyun.nlb20220430.models.ListLoadBalancersRequest listLoadBalancersRequest = new com.aliyun.nlb20220430.models.ListLoadBalancersRequest();
            listLoadBalancersRequest.setLoadBalancerNames(Collections.singletonList(lbItem.getLb_name()));
            listLoadBalancersRequest.setRegionId(lbItem.getRegion_id());
            com.aliyun.nlb20220430.models.ListLoadBalancersResponse listLoadBalancersResponse = aliCloudHttpConfig.getNLBClient(cloudAccountEntity.getId(), lbItem.getRegion_id()).listLoadBalancers(listLoadBalancersRequest);
            if (listLoadBalancersResponse.body.loadBalancers.stream().anyMatch(loadBalancer -> loadBalancer.loadBalancerName.equals(lbItem.getLb_name()))) {
                throw new ServiceRuntimeException("校验不通过，LB名称已存在");
            }
        }
    }

    private void verifyAliCloudClb(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws Exception {
        if (StringUtils.isEmpty(lbItem.getZone_id())) {
            throw new ServiceRuntimeException("校验不通过，请选择可用区");
        }
        if (StringUtils.isEmpty(lbItem.getSlave_zone_id())) {
            throw new ServiceRuntimeException("校验不通过，请选择从可用区");
        }

        if (StringUtils.isEmpty(lbItem.getCharge_mode())) {
            throw new ServiceRuntimeException("校验不通过，请选择计费方式");
        }
        if (StringUtils.isEmpty(lbItem.getCharge_mode())) {
            throw new ServiceRuntimeException("校验不通过，请选择计费方式");
        }
        if ("PayBySpec".equals(lbItem.getCharge_mode())) {
            if (StringUtils.isEmpty(lbItem.getFlavor_id())) {
                throw new ServiceRuntimeException("校验不通过，请选择规格");
            }
        }
        List<DescribeZonesResponseBody.DescribeZonesResponseBodyZonesZone> zoneList = getZones(lbItem.getRegion_id(), cloudAccountEntity);
        Optional<com.aliyun.slb20140515.models.DescribeZonesResponseBody.DescribeZonesResponseBodyZonesZone> zoneOptional = zoneList.stream().filter(zoneResource -> zoneResource.getZoneId().equals(lbItem.getZone_id())).findFirst();
        if (!zoneOptional.isPresent()) {
            throw new ServiceRuntimeException("校验不通过，可用区不合法");
        }
        if (zoneOptional.get().getSlaveZones().slaveZone.stream().noneMatch(slaveZone -> slaveZone.getZoneId().equals(lbItem.getSlave_zone_id()))) {
            throw new ServiceRuntimeException("校验不通过，从可用区不合法");
        }
        if (NetTypeEnum.INTERNAL.getNetType().equals(lbItem.getNet_type())) {
            if (StringUtils.isEmpty(lbItem.getVpc_id())) {
                throw new ServiceRuntimeException("校验不通过，请选择VPC");
            }
            if (StringUtils.isEmpty(lbItem.getSubnet_id())) {
                throw new ServiceRuntimeException("校验不通过，请选择子网");
            }
            Client vpvClient = verifyAliCloudVpc(lbItem, cloudAccountEntity);
            com.aliyun.vpc20160428.models.DescribeVSwitchesRequest describeVSwitchesRequest = new com.aliyun.vpc20160428.models.DescribeVSwitchesRequest();
            describeVSwitchesRequest.setVpcId(lbItem.getVpc_id());
            com.aliyun.vpc20160428.models.DescribeVSwitchesResponse describeVSwitchesResponse = vpvClient.describeVSwitches(describeVSwitchesRequest);
            Optional<com.aliyun.vpc20160428.models.DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch> describeVSwitchesResponseBodyVSwitchesVSwitchOptional =
                    describeVSwitchesResponse.body.vSwitches.vSwitch.stream().filter(vSwitch -> vSwitch.getVSwitchId().equals(lbItem.getSubnet_id())).findFirst();
            if (!describeVSwitchesResponseBodyVSwitchesVSwitchOptional.isPresent()) {
                throw new ServiceRuntimeException("校验不通过，子网不存在");
            }
            if (!describeVSwitchesResponseBodyVSwitchesVSwitchOptional.get().zoneId.equals(lbItem.getZone_id())) {
                throw new ServiceRuntimeException("校验不通过，子网可用区与主可用区不一致");
            }
            if (describeVSwitchesResponse.body.vSwitches.vSwitch.stream().noneMatch(vSwitch -> vSwitch.getZoneId().equals(lbItem.getSlave_zone_id()))) {
                throw new ServiceRuntimeException("校验不通过，当前主备可用区组合，IPv4私网VPC网络实例资源不足，请尝试选择其他主备可用区组合购买。");
            }

        }
    }

    private void verifyAlbOrNlb(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws Exception {
        if (StringUtils.isEmpty(lbItem.getVpc_id())) {
            throw new ServiceRuntimeException("校验不通过，请选择VPC");
        }
        if (null == lbItem.getSubnet_ids() || lbItem.getSubnet_ids().isEmpty()) {
            throw new ServiceRuntimeException("校验不通过，请选择子网");
        }
        if (null == lbItem.getZone_ids() || lbItem.getZone_ids().isEmpty()) {
            throw new ServiceRuntimeException("校验不通过，请选择可用区");
        }
        if (lbItem.getZone_ids().size() != lbItem.getSubnet_ids().size()) {
            throw new ServiceRuntimeException("校验不通过，子网数量与可用区数量不一致");
        }
        if (StringUtils.isEmpty(lbItem.getIp_mode())) {
            throw new ServiceRuntimeException("校验不通过，请选择IP模式");
        }
        Client vpvClient = verifyAliCloudVpc(lbItem, cloudAccountEntity);
        List<DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch> vSwitches = getDescribeVSwitchesResponseBodyVSwitchesVSwitches(lbItem, vpvClient);

        // 检查所有用户提供的子网ID是否存在于查询结果中
        if (!lbItem.getSubnet_ids().stream().allMatch(subnetId -> vSwitches.stream().anyMatch(vSwitch -> vSwitch.getVSwitchId().equals(subnetId)))) {
            throw new ServiceRuntimeException("校验不通过，部分子网不存在");
        }

        // 检查所有用户提供的可用区ID是否存在于查询结果中，并且每个可用区仅能选择一个子网
        Map<String, List<DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch>> groupByZoneId = vSwitches.stream().filter(vSwitch -> lbItem.getSubnet_ids().contains(vSwitch.getVSwitchId())).collect(Collectors.groupingBy(DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch::getZoneId));

        if (!groupByZoneId.keySet().containsAll(lbItem.getZone_ids())) {
            throw new ServiceRuntimeException("校验不通过，部分可用区未选择对应子网");
        }

        groupByZoneId.forEach((zoneId, vSwitchList) -> {
            if (vSwitchList.size() != 1) {
                throw new ServiceRuntimeException("校验不通过，每个可用区仅能选择一个子网");
            }
            String subnetId = vSwitchList.get(0).getVSwitchId();
            if (!lbItem.getSubnet_ids().contains(subnetId)) {
                throw new ServiceRuntimeException("校验不通过，子网与可用区不匹配");
            }
        });
        Set<String> usedSubnets = new HashSet<>();
        groupByZoneId.values().forEach(vSwitchList -> {
            String subnetId = vSwitchList.get(0).getVSwitchId();
            if (!usedSubnets.add(subnetId)) {
                throw new ServiceRuntimeException("校验不通过，一个子网不能对应多个可用区");
            }
        });

    }

    private List<DescribeVSwitchesResponseBody.DescribeVSwitchesResponseBodyVSwitchesVSwitch> getDescribeVSwitchesResponseBodyVSwitchesVSwitches(LBItem lbItem, Client vpvClient) throws Exception {
        com.aliyun.vpc20160428.models.DescribeVSwitchesRequest describeVSwitchesRequest = new com.aliyun.vpc20160428.models.DescribeVSwitchesRequest();
        describeVSwitchesRequest.setVpcId(lbItem.getVpc_id());
        com.aliyun.vpc20160428.models.DescribeVSwitchesResponse describeVSwitchesResponse = vpvClient.describeVSwitches(describeVSwitchesRequest);

        return describeVSwitchesResponse.body.vSwitches.vSwitch;
    }

    private Client verifyAliCloudVpc(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws Exception {
        Client vpvClient = aliCloudHttpConfig.getVPCClient(cloudAccountEntity.getId(), lbItem.getRegion_id());
        com.aliyun.vpc20160428.models.DescribeVpcsRequest describeVpcsRequest = new com.aliyun.vpc20160428.models.DescribeVpcsRequest();
        describeVpcsRequest.setVpcId(lbItem.getVpc_id());
        com.aliyun.vpc20160428.models.DescribeVpcsResponse describeVpcsResponse = vpvClient.describeVpcs(describeVpcsRequest);
        if (0 == describeVpcsResponse.body.totalCount) {
            throw new ServiceRuntimeException("校验不通过，VPC不存在");
        }
        return vpvClient;
    }

    private void verifyAliCloudProject(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws Exception {
        ListResourceGroupsRequest listResourceGroupsRequest = new ListResourceGroupsRequest();
        listResourceGroupsRequest.setStatus("OK");
        listResourceGroupsRequest.setPageSize(100);
        listResourceGroupsRequest.setResourceGroupId(lbItem.getProject_id());
        ListResourceGroupsResponse listResourceGroupsResponse = aliCloudHttpConfig.getResourceClient(cloudAccountEntity.getId()).listResourceGroups(listResourceGroupsRequest);
        Optional<ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup> optional = listResourceGroupsResponse.body.getResourceGroups().resourceGroup.stream().filter(resourceGroup -> resourceGroup.id.equals(lbItem.getProject_id())).findFirst();
        if (!optional.isPresent()) {
            throw new ServiceRuntimeException("校验不通过，企业项目不存在");
        }
    }

    private void verifyAliLBBase(LBItem lbItem) {
        if (StringUtils.isEmpty(lbItem.getLb_type())) {
            throw new ServiceRuntimeException("校验不通过，LB类型为空");
        }
        if (!"alb".equals(lbItem.getLb_type()) && !"clb".equals(lbItem.getLb_type()) && !"nlb".equals(lbItem.getLb_type())) {
            throw new ServiceRuntimeException("校验不通过，LB类型不合法");
        }
        if (lbItem.getLb_name().trim().length() > 80) {
            throw new ServiceRuntimeException("校验不通过，LB名称最大长度80");
        }
        if (!isValidField(lbItem.getLb_name().trim())) {
            throw new ServiceRuntimeException("校验不通过，LB名称不合法");
        }
    }

    public static boolean isValidField(String field) {
        if (field == null || field.isEmpty() || field.length() > 80) {
            return false;
        }
        String regex = "^[a-zA-Z\\p{L}][a-zA-Z\\p{L}0-9._-]{0,79}$";
        return field.matches(regex);
    }

    private void verifyVolcEngineLBCreateParameters(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws ApiException {
        verifyVolcEngineRegionProject(lbItem, cloudAccountEntity);
        verifyVolcEngineBase(lbItem);
        if ("clb".equals(lbItem.getLb_type())) {
            verifyVolcEngineClbName(lbItem, cloudAccountEntity);
            verifyVolcengineLBFlavor(lbItem);
            verifyNetType(lbItem);
            verifyVolcEngineVpc(lbItem, cloudAccountEntity);
            if (StringUtils.isEmpty(lbItem.getSubnet_id())) {
                throw new ServiceRuntimeException("校验不通过，子网ID为空");
            }
            com.volcengine.vpc.model.DescribeSubnetsResponse describeSubnetsResponse = getSubnetsResponse(lbItem, cloudAccountEntity);
            if (describeSubnetsResponse.getSubnets().stream().noneMatch(subnetForDescribeSubnetsOutput -> subnetForDescribeSubnetsOutput.getSubnetId().equals(lbItem.getSubnet_id()))) {
                throw new ServiceRuntimeException("校验不通过，子网不存在");
            }
        }

        if ("alb".equals(lbItem.getLb_type())) {
            verifyVolcEngineAlbName(lbItem, cloudAccountEntity);
            verifyNetType(lbItem);
            verifyVolcEngineVpc(lbItem, cloudAccountEntity);
            if (null == lbItem.getSubnet_ids()) {
                throw new ServiceRuntimeException("校验不通过，子网为空");
            }
            if (lbItem.getSubnet_ids().size() < 2) {
                throw new ServiceRuntimeException("校验不通过，最少选择两个不同可用区子网");
            }
            com.volcengine.vpc.model.DescribeSubnetsResponse describeSubnetsResponse = getSubnetsResponse(lbItem, cloudAccountEntity);

            List<SubnetForDescribeSubnetsOutput> subnets = describeSubnetsResponse.getSubnets().stream().filter(subnetForDescribeSubnetsOutput -> lbItem.getSubnet_ids().contains(subnetForDescribeSubnetsOutput.getSubnetId())).collect(Collectors.toList());
            if (subnets.isEmpty()) {
                throw new ServiceRuntimeException("校验不通过，子网不存在");
            }
            if (subnets.size() < lbItem.getSubnet_ids().size()) {
                throw new ServiceRuntimeException("校验不通过，部分子网不存在");
            }
            if (subnets.size() > subnets.stream().map(SubnetForDescribeSubnetsOutput::getZoneId).distinct().count()) {
                throw new ServiceRuntimeException("校验不通过，同一可用区存在多个子网");
            }
        }

    }

    private com.volcengine.vpc.model.DescribeSubnetsResponse getSubnetsResponse(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws ApiException {
        com.volcengine.vpc.model.DescribeSubnetsRequest describeSubnetsRequest = new com.volcengine.vpc.model.DescribeSubnetsRequest();
        describeSubnetsRequest.setVpcId(lbItem.getVpc_id());
        com.volcengine.vpc.model.DescribeSubnetsResponse describeSubnetsResponse = volcEngineCloudHttpConfig.getVolcEngineVpcApi(cloudAccountEntity.getId(), lbItem.getRegion_id()).describeSubnets(describeSubnetsRequest);
        if (null == describeSubnetsResponse.getSubnets()) {
            throw new ServiceRuntimeException("校验不通过，子网不存在");
        }
        return describeSubnetsResponse;
    }

    private void verifyVolcEngineClbName(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws ApiException {
        ClbApi clbApi = volcEngineCloudHttpConfig.getVolcEngineClbApi(cloudAccountEntity.getId(), lbItem.getRegion_id());
        com.volcengine.clb.model.DescribeLoadBalancersRequest loadBalancersRequest = new com.volcengine.clb.model.DescribeLoadBalancersRequest();
        loadBalancersRequest.setLoadBalancerName(lbItem.getLb_name().trim());
        com.volcengine.clb.model.DescribeLoadBalancersResponse loadBalancersResponse = clbApi.describeLoadBalancers(loadBalancersRequest);
        if (null != loadBalancersResponse.getLoadBalancers()) {
            for (LoadBalancerForDescribeLoadBalancersOutput loadBalancer : loadBalancersResponse.getLoadBalancers()) {
                if (loadBalancer.getLoadBalancerName().equals(lbItem.getLb_name().trim())) {
                    throw new ServiceRuntimeException("校验不通过，LB名称重复");
                }
            }
        }
    }

    private void verifyVolcEngineAlbName(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws ApiException {
        AlbApi albApi = volcEngineCloudHttpConfig.getVolcEngineAlbApi(cloudAccountEntity.getId(), lbItem.getRegion_id());
        com.volcengine.alb.model.DescribeLoadBalancersRequest loadBalancersRequest = new com.volcengine.alb.model.DescribeLoadBalancersRequest();
        loadBalancersRequest.setLoadBalancerName(lbItem.getLb_name().trim());
        com.volcengine.alb.model.DescribeLoadBalancersResponse loadBalancersResponse = albApi.describeLoadBalancers(loadBalancersRequest);
        if (null != loadBalancersResponse.getLoadBalancers()) {
            for (com.volcengine.alb.model.LoadBalancerForDescribeLoadBalancersOutput loadBalancer : loadBalancersResponse.getLoadBalancers()) {
                if (loadBalancer.getLoadBalancerName().equals(lbItem.getLb_name().trim())) {
                    throw new ServiceRuntimeException("校验不通过，LB名称重复");
                }
            }
        }
    }

    private void verifyVolcEngineBase(LBItem lbItem) {
        if (StringUtils.isEmpty(lbItem.getLb_type())) {
            throw new ServiceRuntimeException("校验不通过，LB类型为空，请选择负载均衡/应用型负载均衡");
        }
        if (!"alb".equals(lbItem.getLb_type()) && !"clb".equals(lbItem.getLb_type())) {
            throw new ServiceRuntimeException("校验不通过，LB类型不合法");
        }
        if (lbItem.getLb_name().trim().length() > 128) {
            throw new ServiceRuntimeException("校验不通过，LB名称最大长度128");
        }
    }

    private void verifyVolcengineLBFlavor(LBItem lbItem) {
        if (StringUtils.isEmpty(lbItem.getFlavor_id())) {
            throw new ServiceRuntimeException("校验不通过，规格为空");
        }

        if (Constants.VOLE_ENGINE_CLB_FLAVOR_LIST.stream().noneMatch(cloudFlavorInfo -> cloudFlavorInfo.getFlavor_id().equals(lbItem.getFlavor_id()))) {
            throw new ServiceRuntimeException("校验不通过，规格不合法");
        }
    }

    private void verifyVolcEngineVpc(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws ApiException {
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            if (lbItem.getBandwidth_size() < 1 || lbItem.getBandwidth_size() > 200) {
                throw new ServiceRuntimeException("校验不通过，LB带宽范围[1Mbps-200Mbps]");
            }
        }
        if (StringUtils.isEmpty(lbItem.getVpc_id())) {
            throw new ServiceRuntimeException("校验不通过，VPC_ID为空");
        }
        DescribeVpcAttributesRequest describeVpcAttributesRequest = new DescribeVpcAttributesRequest();
        describeVpcAttributesRequest.setVpcId(lbItem.getVpc_id());
        DescribeVpcAttributesResponse describeVpcAttributesResponse = volcEngineCloudHttpConfig.getVolcEngineVpcApi(cloudAccountEntity.getId(), lbItem.getRegion_id()).describeVpcAttributes(describeVpcAttributesRequest);
        if (StringUtils.isEmpty(describeVpcAttributesResponse.getVpcId())) {
            throw new ServiceRuntimeException("校验不通过，VPC不存在");
        }
    }

    private void verifyVolcEngineRegionProject(LBItem lbItem, CloudAccountEntity cloudAccountEntity) {
        boolean matchRegion;
        try {
            com.volcengine.ecs.model.DescribeRegionsRequest describeRegionsRequest = new com.volcengine.ecs.model.DescribeRegionsRequest();
            describeRegionsRequest.setMaxResults(100);
            com.volcengine.ecs.model.DescribeRegionsResponse describeRegionsResponse = volcEngineCloudHttpConfig.getVolcEngineEcsApi(cloudAccountEntity.getId(), "").describeRegions(new com.volcengine.ecs.model.DescribeRegionsRequest());
            matchRegion = describeRegionsResponse.getRegions().stream().anyMatch(regionInfo -> regionInfo.getRegionId().equals(lbItem.getRegion_id()));
        } catch (ApiException e) {
            throw new ServiceRuntimeException("云泽服务异常，查询火山云地区列表失败，请联系云泽平台管理员");
        }
        if (!matchRegion) {
            throw new ServiceRuntimeException("校验不通过，区域不合法");
        }

        ListProjectsResponse listProjectsResponse;
        try {
            ListProjectsRequest listProjectsRequest = new ListProjectsRequest();
            listProjectsRequest.setLimit(100);
            listProjectsResponse = volcEngineCloudHttpConfig.getVolcEngineIam20210801Api(cloudAccountEntity.getId(), lbItem.getRegion_id()).listProjects(listProjectsRequest);
        } catch (ApiException e) {
            throw new ServiceRuntimeException("云泽服务异常，查询火山云地区列表失败，请联系云泽平台管理员");
        }

        boolean matchProject = listProjectsResponse.getProjects().stream().anyMatch(project -> project.getProjectName().equals(lbItem.getProject_id()));
        if (!matchProject) {
            throw new ServiceRuntimeException("校验不通过，项目不存在");
        }
    }

    private LoadBalancer createV3ELB(LBItem lbItem, CloudAccountEntity cloudAccountEntity, LBCreateResponse lbCreateResponse) {
        CreateLoadBalancerRequest createLoadBalancerRequest = new CreateLoadBalancerRequest();
        CreateLoadBalancerRequestBody createLoadBalancerRequestBody = new CreateLoadBalancerRequestBody();
        CreateLoadBalancerOption loadBalancer = new CreateLoadBalancerOption();
        loadBalancer.setName(lbItem.getLb_name());
        if (StringUtils.isNotEmpty(lbItem.getProject_id()) && !"0".equals(lbItem.getProject_id())) {
            loadBalancer.setEnterpriseProjectId(lbItem.getProject_id());
        }
        loadBalancer.setDescription("灵犀平台工单-自动化创建");
        loadBalancer.setVipSubnetCidrId(lbItem.getSubnet_id());
        if (StringUtils.isNotEmpty(lbItem.getL4_flavor_id())) {
            loadBalancer.setL4FlavorId(lbItem.getL4_flavor_id());
        }
        if (StringUtils.isNotEmpty(lbItem.getL7_flavor_id())) {
            loadBalancer.setL7FlavorId(lbItem.getL7_flavor_id());
        }
        loadBalancer.setVpcId(lbItem.getVpc_id());
        loadBalancer.setAvailabilityZoneList(lbItem.getZone_ids());
        PrepaidCreateOption prepaidOptions = new PrepaidCreateOption();
        prepaidOptions.setAutoPay(true);
        prepaidOptions.setAutoRenew(true);
        prepaidOptions.setPeriodNum(1);
        prepaidOptions.setPeriodType(PrepaidCreateOption.PeriodTypeEnum.MONTH);
        loadBalancer.setPrepaidOptions(prepaidOptions);
        createLoadBalancerRequestBody.setLoadbalancer(loadBalancer);
        createLoadBalancerRequest.setBody(createLoadBalancerRequestBody);
        ElbClient elbClient = huaweiCloudHttpConfig.getHuaweiElbV3Client(cloudAccountEntity.getId(), lbItem.getRegion_id());
        CreateLoadBalancerResponse createLoadBalancerResponse = elbClient.createLoadBalancer(createLoadBalancerRequest);
        if (null == createLoadBalancerResponse.getLoadbalancer() || StringUtils.isEmpty(createLoadBalancerResponse.getLoadbalancer().getId())) {
            log.info("华为云LB创建完毕：{}", createLoadBalancerResponse.getLoadbalancerId());
            lbCreateResponse.setLbId(createLoadBalancerResponse.getLoadbalancerId());
        } else {
            log.info("华为云LB创建完毕：{}", JSON.toJSONString(createLoadBalancerResponse.getLoadbalancer()));
            lbCreateResponse.setLbId(createLoadBalancerResponse.getLoadbalancer().getId());
        }
        try {
            Thread.sleep(5000L);
        } catch (InterruptedException ignored) {

        }
        ShowLoadBalancerRequest showLoadBalancerRequest = new ShowLoadBalancerRequest();
        showLoadBalancerRequest.setLoadbalancerId(lbCreateResponse.getLbId());
        ShowLoadBalancerResponse showLoadBalancerResponse = elbClient.showLoadBalancer(showLoadBalancerRequest);
        return showLoadBalancerResponse.getLoadbalancer();
    }

    private PublicipCreateResp createV2EIP(LBItem lbItem, CloudAccountEntity cloudAccountEntity, LBCreateResponse lbCreateResponse) {
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            log.info("公网LB为LB创建EIP");
            com.huaweicloud.sdk.eip.v2.EipClient eipClient = huaweiCloudHttpConfig.getHuaweiEipV2Client(cloudAccountEntity.getId(), lbItem.getRegion_id());
            CreatePublicipRequest createPublicipRequest = new CreatePublicipRequest();
            CreatePublicipRequestBody createPublicipRequestBody = new CreatePublicipRequestBody();
            if (StringUtils.isNotEmpty(lbItem.getProject_id()) && !"0".equals(lbItem.getProject_id())) {
                createPublicipRequestBody.setEnterpriseProjectId(lbItem.getProject_id());
            }
            createPublicipRequestBody.setBandwidth(getBandwidthOption(lbItem));
            createPublicipRequestBody.setPublicip(getPublicIpOption(lbItem));
            createPublicipRequest.setBody(createPublicipRequestBody);
            CreatePublicipResponse response = eipClient.createPublicip(createPublicipRequest);
            log.info("EIP创建完毕：{}", JSON.toJSONString(response.getPublicip()));
            lbCreateResponse.setEipId(response.getPublicip().getId());
            bindELBAndEIP(lbItem, lbCreateResponse, cloudAccountEntity);
            return response.getPublicip();
        }
        return null;
    }

    private CreatePublicipOption getPublicIpOption(LBItem lbItem) {
        CreatePublicipOption createPublicipOption = new CreatePublicipOption();
        createPublicipOption.setType("5_bgp");
        createPublicipOption.setIpVersion(IpVersionEnum.fromValue(lbItem.getIp_version()));
        return createPublicipOption;
    }

    private CreatePublicipBandwidthOption getBandwidthOption(LBItem lbItem) {
        CreatePublicipBandwidthOption bandwidthOption = new CreatePublicipBandwidthOption();
        bandwidthOption.setName(lbItem.getLb_name() + "-eip");
        bandwidthOption.setChargeMode(CreatePublicipBandwidthOption.ChargeModeEnum.TRAFFIC);
        bandwidthOption.setSize(lbItem.getBandwidth_size());
        bandwidthOption.setShareType(ShareTypeEnum.PER);
        return bandwidthOption;
    }

    private void verifyTencentCloudLBCreateParameters(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws TencentCloudSDKException {
        verifyTencentCloudRegionAndProject(lbItem, cloudAccountEntity);
        ClbClient clbClient = tencentCloudHttpConfig.getTencentClbClient(cloudAccountEntity.getId(), lbItem.getRegion_id());
        if (lbItem.getLb_name().trim().length() > 60) {
            throw new ServiceRuntimeException("校验不通过，LB名称最大长度60");
        }
        DescribeLoadBalancersRequest loadBalancersRequest = new DescribeLoadBalancersRequest();
        loadBalancersRequest.setLoadBalancerName(lbItem.getLb_name().trim());
        DescribeLoadBalancersResponse loadBalancersResponse = clbClient.DescribeLoadBalancers(loadBalancersRequest);
        for (com.tencentcloudapi.clb.v20180317.models.LoadBalancer loadBalancer : loadBalancersResponse.getLoadBalancerSet()) {
            if (loadBalancer.getLoadBalancerName().equals(lbItem.getLb_name().trim())) {
                throw new ServiceRuntimeException("校验不通过，LB名称重复");
            }
        }
        verifyNetType(lbItem);
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            if (lbItem.getBandwidth_size() < 1 || lbItem.getBandwidth_size() > 2048) {
                throw new ServiceRuntimeException("校验不通过，LB带宽范围[1Mbps-2048Mbps]");
            }
            if (StringUtils.isEmpty(lbItem.getZone_id())) {
                throw new ServiceRuntimeException("校验不通过，可用区为空");
            }
            DescribeResourcesResponse describeResourcesResponse = tencentCloudHttpConfig.getTencentClbClient(cloudAccountEntity.getId(), lbItem.getRegion_id()).DescribeResources(new DescribeResourcesRequest());
            List<ZoneResource> azList = getZoneResourceList(describeResourcesResponse);
            boolean matchZone = azList.stream().anyMatch(zoneResource -> zoneResource.getMasterZone().equals(lbItem.getZone_id()));
            if (!matchZone) {
                throw new ServiceRuntimeException("校验不通过，可用区不合法");
            }
        }
        if (StringUtils.isEmpty(lbItem.getVpc_id())) {
            throw new ServiceRuntimeException("校验不通过，VPC_ID为空");
        }
        com.tencentcloudapi.vpc.v20170312.VpcClient vpcClient = tencentCloudHttpConfig.getTencentVpcClient(cloudAccountEntity.getId(), lbItem.getRegion_id());
        DescribeVpcsResponse describeVpcsResponse = vpcClient.DescribeVpcs(new DescribeVpcsRequest());
        boolean matchVpc = Arrays.stream(describeVpcsResponse.getVpcSet()).anyMatch(vpc -> vpc.getVpcId().equals(lbItem.getVpc_id()));
        if (!matchVpc) {
            throw new ServiceRuntimeException("校验不通过，VPC_ID不合法");
        }
        if (StringUtils.isEmpty(lbItem.getSubnet_id())) {
            throw new ServiceRuntimeException("校验不通过，子网ID为空");
        }
        DescribeSubnetsRequest describeSubnetsRequest = new DescribeSubnetsRequest();
        Filter filter = new Filter();
        filter.setName("vpc-id");
        filter.setValues(new String[]{lbItem.getVpc_id()});
        describeSubnetsRequest.setFilters(new Filter[]{filter});
        DescribeSubnetsResponse describeSubnetsResponse = vpcClient.DescribeSubnets(describeSubnetsRequest);
        boolean matchSubnet = Arrays.stream(describeSubnetsResponse.getSubnetSet()).anyMatch(subnet -> subnet.getSubnetId().equals(lbItem.getSubnet_id()));
        if (!matchSubnet) {
            throw new ServiceRuntimeException("校验不通过，子网ID不合法");
        }
    }

    private void verifyTencentCloudRegionAndProject(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws TencentCloudSDKException {
        DescribeRegionsRequest describeRegionsRequest = new DescribeRegionsRequest();
        describeRegionsRequest.setProduct("clb");
        DescribeRegionsResponse describeRegionsResponse = tencentCloudHttpConfig.getTencentRegionClient(cloudAccountEntity.getId()).DescribeRegions(describeRegionsRequest);
        boolean matchRegion = Arrays.stream(describeRegionsResponse.getRegionSet()).anyMatch(regionInfo -> regionInfo.getRegion().equals(lbItem.getRegion_id()));
        if (!matchRegion) {
            throw new ServiceRuntimeException("校验不通过，区域不合法");
        }
        DcdbClient dcdbClient = tencentCloudHttpConfig.getTencentDcdbClient(cloudAccountEntity.getId(), lbItem.getRegion_id());
        DescribeProjectsResponse describeProjectsResponse = dcdbClient.DescribeProjects(new DescribeProjectsRequest());
        boolean matchProject = Arrays.stream(describeProjectsResponse.getProjects()).anyMatch(project -> String.valueOf(project.getProjectId()).equals(lbItem.getProject_id()));
        if (!matchProject) {
            throw new ServiceRuntimeException("校验不通过，企业项目不合法");
        }
    }

    private void verifyHuaweiCloudLBCreateParameters(LBItem lbItem, CloudAccountEntity cloudAccountEntity) {
        verifyHuaweiCloudRegionAndProject(lbItem, cloudAccountEntity);
        if (StringUtils.isEmpty(lbItem.getLb_type())) {
            throw new ServiceRuntimeException("校验不通过，LB类型为空");
        }
        if (!LbTypeEnum.EXCLUSIVE.getLBType().equals(lbItem.getLb_type()) && !LbTypeEnum.SHARED.getLBType().equals(lbItem.getLb_type())) {
            throw new ServiceRuntimeException("校验不通过，LB类型不合法");
        }
        ElbClient elbClient = huaweiCloudHttpConfig.getHuaweiElbV3Client(cloudAccountEntity.getId(), lbItem.getRegion_id());
        log.debug("独享LB，校验可用区，规格数据");
        if (LbTypeEnum.EXCLUSIVE.getLBType().equals(lbItem.getLb_type())) {
            verifyLBZone(lbItem, elbClient);
            verifyLBFlavor(lbItem, elbClient);
        }
        verifyLBNet(lbItem);
        verifyLBVPC(lbItem, cloudAccountEntity);
        verifyLBName(lbItem, cloudAccountEntity);
    }

    private void verifyHuaweiCloudRegionAndProject(LBItem lbItem, CloudAccountEntity cloudAccountEntity) {
        KeystoneListRegionsResponse keystoneListRegionsResponse = huaweiCloudHttpConfig.getHuaweiIamClient(cloudAccountEntity.getId()).keystoneListRegions(new KeystoneListRegionsRequest());
        boolean regionMatch = keystoneListRegionsResponse.getRegions().stream().anyMatch(region -> region.getId().equals(lbItem.getRegion_id()));
        if (!regionMatch) {
            throw new ServiceRuntimeException("校验不通过，区域ID不合法");
        }
        EpsClient epsClient = huaweiCloudHttpConfig.getHuaweiEpsClient(cloudAccountEntity.getId());
        ListEnterpriseProjectResponse listEnterpriseProjectResponse = epsClient.listEnterpriseProject(new ListEnterpriseProjectRequest());
        boolean matchProject = listEnterpriseProjectResponse.getEnterpriseProjects().stream().anyMatch(project -> project.getId().equals(lbItem.getProject_id()));
        if (!matchProject) {
            throw new ServiceRuntimeException("校验不通过，项目ID不合法");
        }
    }

    private void verifyLBName(LBItem lbItem, CloudAccountEntity cloudAccountEntity) {
        if (lbItem.getLb_name().trim().length() > 255) {
            throw new ServiceRuntimeException("校验不通过，LB名称最大长度255");
        }
        ListLoadBalancersRequest balancersRequest = new ListLoadBalancersRequest();
        List<String> nameList = new ArrayList<>();
        nameList.add(lbItem.getLb_name().trim());
        balancersRequest.setName(nameList);
        ListLoadBalancersResponse listLoadBalancersResponse = huaweiCloudHttpConfig.getHuaweiElbV3Client(cloudAccountEntity.getId(), lbItem.getRegion_id()).listLoadBalancers(balancersRequest);
        for (LoadBalancer loadbalancer : listLoadBalancersResponse.getLoadbalancers()) {
            if (loadbalancer.getName().equals(lbItem.getLb_name().trim())) {
                throw new ServiceRuntimeException("校验不通过，LB名称重复");
            }
        }
    }

    private void verifyLBVPC(LBItem lbItem, CloudAccountEntity cloudAccountEntity) {
        VpcClient vpcClient = huaweiCloudHttpConfig.getHuaweiVpcClient(cloudAccountEntity.getId(), lbItem.getRegion_id());
        if (StringUtils.isEmpty(lbItem.getVpc_id())) {
            throw new ServiceRuntimeException("校验不通过，VPC为空");
        }
        ListVpcsRequest listVpcsRequest = new ListVpcsRequest();
        listVpcsRequest.setId(lbItem.getVpc_id());
        ListVpcsResponse listVpcsResponse = vpcClient.listVpcs(listVpcsRequest);

        if (listVpcsResponse.getVpcs().isEmpty()) {
            throw new ServiceRuntimeException("校验不通过，VPC不存在");
        }
        if (StringUtils.isEmpty(lbItem.getSubnet_id())) {
            throw new ServiceRuntimeException("校验不通过，子网为空");
        }
        ListSubnetsRequest listSubnetsRequest = new ListSubnetsRequest();
        listSubnetsRequest.setVpcId(lbItem.getVpc_id());
        ListSubnetsResponse listSubnets = vpcClient.listSubnets(listSubnetsRequest);
        if (listSubnets.getSubnets().isEmpty()) {
            throw new ServiceRuntimeException("校验不通过，子网不存在");
        }
        boolean match = listSubnets.getSubnets().stream().anyMatch(subnet -> subnet.getNeutronSubnetId().equals(lbItem.getSubnet_id()));
        if (!match) {
            throw new ServiceRuntimeException("校验不通过，子网不存在");
        }
    }

    private void verifyLBNet(LBItem lbItem) {
        verifyNetType(lbItem);
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            if (lbItem.getBandwidth_size() == 0) {
                throw new ServiceRuntimeException("校验不通过，网络带宽为空");
            }
            if (lbItem.getBandwidth_size() < 0 || lbItem.getBandwidth_size() > 4000) {
                throw new ServiceRuntimeException("校验不通过，网络带宽不合法");
            }
        }
    }

    private void verifyNetType(LBItem lbItem) {
        if (StringUtils.isEmpty(lbItem.getNet_type())) {
            throw new ServiceRuntimeException("校验不通过，网络类型为空");
        }
        if (!NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type()) && !NetTypeEnum.INTERNAL.getNetType().equals(lbItem.getNet_type())) {
            throw new ServiceRuntimeException("校验不通过，网络类型不合法");
        }
    }

    private void verifyLBZone(LBItem lbItem, ElbClient elbClient) {
        if (lbItem.getZone_ids().isEmpty()) {
            throw new ServiceRuntimeException("校验不通过，LB可用区为空");
        }
        ListAvailabilityZonesResponse zonesResponse = elbClient.listAvailabilityZones(new ListAvailabilityZonesRequest());
        boolean match = new HashSet<>(zonesResponse.getAvailabilityZones().stream().flatMap(Collection::stream).map(AvailabilityZone::getCode).collect(Collectors.toList())).containsAll(lbItem.getZone_ids());
        if (!match) {
            throw new ServiceRuntimeException("校验不通过，LB可用区不存在");
        }
    }

    private void verifyLBFlavor(LBItem lbItem, ElbClient elbClient) {
        if (StringUtils.isEmpty(lbItem.getL4_flavor_id()) && StringUtils.isEmpty(lbItem.getL7_flavor_id())) {
            throw new ServiceRuntimeException("校验不通过，LB规格应用型、网络型至少选择一种");
        }
        ListFlavorsResponse listFlavorsResponse = elbClient.listFlavors(new ListFlavorsRequest());
        if (StringUtils.isNotEmpty(lbItem.getL4_flavor_id())) {
            boolean match = listFlavorsResponse.getFlavors().stream().anyMatch(flavor -> !flavor.getFlavorSoldOut() && flavor.getId().equals(lbItem.getL4_flavor_id()));
            if (!match) {
                throw new ServiceRuntimeException("校验不通过，LB网络型规格不存在");
            }
        }
        if (StringUtils.isNotEmpty(lbItem.getL7_flavor_id())) {
            boolean match = listFlavorsResponse.getFlavors().stream().anyMatch(flavor -> !flavor.getFlavorSoldOut() && flavor.getId().equals(lbItem.getL7_flavor_id()));
            if (!match) {
                throw new ServiceRuntimeException("校验不通过，LB应用型规格不存在");
            }
        }
    }

    private CloudAccountEntity baseVerify(LBItem lbItem) throws TencentCloudSDKException, ApiException {
        if (StringUtils.isEmpty(lbItem.getEmail())) {
            throw new ServiceRuntimeException("LB创建失败，创建人为空");
        }
        UserEntity userEntity = userDao.queryUserByEmail(lbItem.getEmail());
        if (userEntity == null) {
            throw new ServiceRuntimeException("子域名变更异常，创建人不存在");
        }
        if (StringUtils.isEmpty(lbItem.getCloud_id())) {
            throw new ServiceRuntimeException("校验不通过，云商ID为空");
        }
        if (StringUtils.isEmpty(lbItem.getRegion_id())) {
            throw new ServiceRuntimeException("校验不通过，地区ID为空");
        }
        if (StringUtils.isEmpty(lbItem.getProject_id())) {
            throw new ServiceRuntimeException("校验不通过，项目ID为空");
        }
        if (StringUtils.isEmpty(lbItem.getLb_name())) {
            throw new ServiceRuntimeException("校验不通过，LB名称为空");
        }
        if (StringUtils.isEmpty(lbItem.getResource_type())) {
            throw new ServiceRuntimeException("校验不通过，资源类型为空");
        }
        if (!ResourceTypeEnum.HOST_LB.getResourceType().equals(lbItem.getResource_type()) && !ResourceTypeEnum.K8S_LB.getResourceType().equals(lbItem.getResource_type())) {
            throw new ServiceRuntimeException("校验不通过，资源类型不合法");
        }
        CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoByCloudId(lbItem.getCloud_id());
        if (null == cloudAccountEntity) {
            throw new ServiceRuntimeException("校验不通过，云商不存在");
        }
        verifyK8sLB(lbItem, cloudAccountEntity);
        return cloudAccountEntity;
    }

    private void verifyK8sLB(LBItem lbItem, CloudAccountEntity cloudAccountEntity) throws TencentCloudSDKException, ApiException {
        if (ResourceTypeEnum.K8S_LB.getResourceType().equals(lbItem.getResource_type())) {
            if (StringUtils.isEmpty(lbItem.getCluster_id())) {
                throw new ServiceRuntimeException("校验不通过，集群ID为空");
            }
            if (StringUtils.isEmpty(lbItem.getIstio_net())) {
                throw new ServiceRuntimeException("校验不通过，是否istio网关为空");
            }
            if (cloudAccountEntity.getCloudType().equals(Constants.HUAWEI_CLOUD)) {
                ListClustersResponse listClustersResponse = huaweiCloudHttpConfig.getHuaweiCceClient(cloudAccountEntity.getId(), lbItem.getRegion_id()).listClusters(new ListClustersRequest());
                boolean matchCluster = listClustersResponse.getItems().stream().anyMatch(cluster -> cluster.getMetadata().getUid().equals(lbItem.getCluster_id()));
                if (!matchCluster) {
                    throw new ServiceRuntimeException("校验不通过，集群ID不合法或不存在");
                }
            }
            if (cloudAccountEntity.getCloudType().equals(Constants.TENCENT_CLOUD)) {
                DescribeClustersResponse describeClustersResponse = tencentCloudHttpConfig.getTencentTkeClient(cloudAccountEntity.getId(), lbItem.getRegion_id()).DescribeClusters(new DescribeClustersRequest());
                boolean matchCluster = Arrays.stream(describeClustersResponse.getClusters()).anyMatch(cluster -> cluster.getClusterId().equals(lbItem.getCluster_id()));
                if (!matchCluster) {
                    throw new ServiceRuntimeException("校验不通过，集群ID不合法或不存在");
                }
            }
            if (cloudAccountEntity.getCloudType().equals(Constants.VOLC_ENGINE_CLOUD)) {
                com.volcengine.vke.model.ListClustersRequest clustersRequest = new com.volcengine.vke.model.ListClustersRequest();
                FilterForListClustersInput filterForListClustersInput = new FilterForListClustersInput();
                filterForListClustersInput.setIds(Collections.singletonList(lbItem.getCluster_id()));
                clustersRequest.setFilter(filterForListClustersInput);
                com.volcengine.vke.model.ListClustersResponse listClustersResponse = volcEngineCloudHttpConfig.getVolcEngineVkeApi(cloudAccountEntity.getId(), lbItem.getRegion_id()).listClusters(clustersRequest);
                if (listClustersResponse.getTotalCount() == 0) {
                    throw new ServiceRuntimeException("校验不通过，集群ID不合法或不存在");
                }
            }
        }
    }

    @GetMapping("/flavor/list")
    public CommonListResponse queryLBFlavorList(CommonRequest commonRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(commonRequest.getCloud_id()) || StringUtils.isEmpty(commonRequest.getRegion_id())) {
                return commonListResponse;
            }
            CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoByCloudId(commonRequest.getCloud_id());
            if (null == cloudAccountEntity) {
                return commonListResponse;
            } else if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getHuaweiCloudLBFlavorList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getVolcEngineLBFlavorList(commonRequest, commonListResponse);
            } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                if ("clb".equals(commonRequest.getLb_type())) {
                    List<CloudFlavorInfo> flavorList = Constants.ALI_CLB_FLAVOR_LIST;
                    commonListResponse.setTotal(flavorList.size());
                    commonListResponse.setList(CommonUtil.getPage(flavorList, commonRequest.getPageNo(), commonRequest.getPageSize()));
                } else if ("alb".equals(commonRequest.getLb_type())) {
                    List<CloudFlavorInfo> flavorList = Constants.ALI_ALB_FLAVOR_LIST;
                    commonListResponse.setTotal(flavorList.size());
                    commonListResponse.setList(CommonUtil.getPage(flavorList, commonRequest.getPageNo(), commonRequest.getPageSize()));
                }
            }
        } catch (Exception e) {
            log.error("查询云商LB规格列表异常", e);
            commonListResponse.getHeader().setErrorMsg("查询云商LB规格列表异常：" + e.getMessage());
        }
        return commonListResponse;
    }

    private void getVolcEngineLBFlavorList(CommonRequest commonRequest, CommonListResponse commonListResponse) {
        List<CloudFlavorInfo> flavorList = Constants.VOLE_ENGINE_CLB_FLAVOR_LIST;
        commonListResponse.setTotal(flavorList.size());
        commonListResponse.setList(CommonUtil.getPage(flavorList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }

    private void getHuaweiCloudLBFlavorList(CommonRequest commonRequest, CommonListResponse commonListResponse, CloudAccountEntity cloudAccountEntity) {
        List<CloudFlavorInfo> l4FlavorList = new ArrayList<>();
        List<CloudFlavorInfo> l7FlavorList = new ArrayList<>();
        ElbClient elbClient = huaweiCloudHttpConfig.getHuaweiElbV3Client(cloudAccountEntity.getId(), commonRequest.getRegion_id());
        ListFlavorsResponse listFlavorsResponse = elbClient.listFlavors(new ListFlavorsRequest());
        List<Flavor> l4flavors = listFlavorsResponse.getFlavors().stream().filter(flavor -> !flavor.getFlavorSoldOut() && "L4".equals(flavor.getType()) && !flavor.getName().endsWith("extra-large")).collect(Collectors.toList());
        List<Flavor> l7flavors = listFlavorsResponse.getFlavors().stream().filter(flavor -> !flavor.getFlavorSoldOut() && "L7".equals(flavor.getType()) && !flavor.getName().endsWith("extra-large")).collect(Collectors.toList());
        if ("L4".equals(commonRequest.getFlavor_type())) {
            setFlavorList(l4FlavorList, l4flavors);
            commonListResponse.setTotal(l4FlavorList.size());
            commonListResponse.setList(CommonUtil.getPage(l4FlavorList, commonRequest.getPageNo(), commonRequest.getPageSize()));
        }
        if ("L7".equals(commonRequest.getFlavor_type())) {
            setFlavorList(l7FlavorList, l7flavors);
            commonListResponse.setTotal(l7FlavorList.size());
            commonListResponse.setList(CommonUtil.getPage(l7FlavorList, commonRequest.getPageNo(), commonRequest.getPageSize()));
        }
    }

    private void setFlavorList(List<CloudFlavorInfo> flavorList, List<Flavor> flavors) {
        List<Flavor> smallFlavors = flavors.stream().filter(flavor -> flavor.getName().endsWith(".small")).collect(Collectors.toList());
        List<Flavor> mediumFlavors = flavors.stream().filter(flavor -> flavor.getName().endsWith(".medium")).collect(Collectors.toList());
        List<Flavor> largeFlavors = flavors.stream().filter(flavor -> flavor.getName().endsWith(".large")).collect(Collectors.toList());
        addList(flavorList, smallFlavors);
        addList(flavorList, mediumFlavors);
        addList(flavorList, largeFlavors);
    }

    private void addList(List<CloudFlavorInfo> flavorList, List<Flavor> smallFlavors) {
        smallFlavors.sort(Comparator.comparing(Flavor::getName));
        for (Flavor l4flavor : smallFlavors) {
            CloudFlavorInfo cloudFlavorInfo = new CloudFlavorInfo();
            cloudFlavorInfo.setFlavor_id(l4flavor.getId());
            cloudFlavorInfo.setFlavor_name(Utils.getFlavorName(l4flavor));
            flavorList.add(cloudFlavorInfo);
        }
    }

    @GetMapping("/charge/mode/list")
    public CommonListResponse queryChargeModeList(CommonRequest commonRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(commonRequest.getCloud_id())) {
                return commonListResponse;
            }
            CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoByCloudId(commonRequest.getCloud_id());
            if (null == cloudAccountEntity) {
                return commonListResponse;
            } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                List<Map<String, String>> chargeModeList = new ArrayList<>();
                chargeModeList.add(new HashMap<String, String>() {{
                    put("charge_mode", "PayByCLCU");
                    put("charge_mode_name", "按使用量计费");
                }});
                chargeModeList.add(new HashMap<String, String>() {{
                    put("charge_mode", "PayBySpec");
                    put("charge_mode_name", "按规格计费");
                }});
                commonListResponse.setList(chargeModeList);
                commonListResponse.setTotal(chargeModeList.size());
            }
        } catch (Exception e) {
            log.error("查询云商LB可用区列表异常", e);
            commonListResponse.getHeader().setErrorMsg("查询云商LB可用区列表异常：" + e.getMessage());
        }
        return commonListResponse;
    }

    @GetMapping("/zone/list")
    public CommonListResponse queryAvailabilityZones(CommonRequest commonRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(commonRequest.getCloud_id()) || StringUtils.isEmpty(commonRequest.getRegion_id())) {
                return commonListResponse;
            }
            CloudAccountEntity cloudAccountEntity = cloudAccountDao.queryCloudAccountInfoByCloudId(commonRequest.getCloud_id());
            if (null == cloudAccountEntity) {
                return commonListResponse;
            } else if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getHuaweiCloudElbZoneList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.TENCENT_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getTencentCloudLbZoneList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getAliCloudLbZoneList(commonRequest, commonListResponse, cloudAccountEntity);
            }
        } catch (Exception e) {
            log.error("查询云商LB可用区列表异常", e);
            commonListResponse.getHeader().setErrorMsg("查询云商LB可用区列表异常：" + e.getMessage());
        }
        return commonListResponse;
    }

    private void getAliCloudLbZoneList(CommonRequest commonRequest, CommonListResponse commonListResponse, CloudAccountEntity cloudAccountEntity) throws Exception {
        List<DescribeZonesResponseBody.DescribeZonesResponseBodyZonesZone> zoneList = getZones(commonRequest.getRegion_id(), cloudAccountEntity);
        List<AvailabilityZoneEntity> availabilityZoneList = new ArrayList<>();
        if (StringUtils.isEmpty(commonRequest.getZone_id())) {
            for (com.aliyun.slb20140515.models.DescribeZonesResponseBody.DescribeZonesResponseBodyZonesZone zoneResource : zoneList) {
                AvailabilityZoneEntity availabilityZone = new AvailabilityZoneEntity();
                availabilityZone.setZone_id(zoneResource.getZoneId());
                availabilityZone.setZone_name(zoneResource.getLocalName());
                availabilityZoneList.add(availabilityZone);
            }
        } else {
            Optional<com.aliyun.slb20140515.models.DescribeZonesResponseBody.DescribeZonesResponseBodyZonesZone> zoneOptional = zoneList.stream().filter(zoneResource -> zoneResource.getZoneId().equals(commonRequest.getZone_id())).findFirst();
            if (zoneOptional.isPresent()) {
                for (DescribeZonesResponseBody.DescribeZonesResponseBodyZonesZoneSlaveZonesSlaveZone describeZonesResponseBodyZonesZoneSlaveZonesSlaveZone : zoneOptional.get().getSlaveZones().slaveZone) {
                    AvailabilityZoneEntity availabilityZone = new AvailabilityZoneEntity();
                    availabilityZone.setZone_id(describeZonesResponseBodyZonesZoneSlaveZonesSlaveZone.getZoneId());
                    availabilityZone.setZone_name(describeZonesResponseBodyZonesZoneSlaveZonesSlaveZone.getLocalName());
                    availabilityZoneList.add(availabilityZone);
                }
            }
        }
        commonListResponse.setTotal(availabilityZoneList.size());
        commonListResponse.setList(CommonUtil.getPage(availabilityZoneList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }

    private List<DescribeZonesResponseBody.DescribeZonesResponseBodyZonesZone> getZones(String regionId, CloudAccountEntity cloudAccountEntity) throws Exception {
        com.aliyun.slb20140515.models.DescribeZonesRequest describeResourcesRequest = new com.aliyun.slb20140515.models.DescribeZonesRequest();
        describeResourcesRequest.setRegionId(regionId);
        com.aliyun.slb20140515.models.DescribeZonesResponse describeResourcesResponse = aliCloudHttpConfig.getSLBClient(cloudAccountEntity.getId(), regionId).describeZones(describeResourcesRequest);
        return describeResourcesResponse.getBody().getZones().getZone();
    }

    private void getTencentCloudLbZoneList(CommonRequest commonRequest, CommonListResponse commonListResponse, CloudAccountEntity cloudAccountEntity) throws TencentCloudSDKException {
        DescribeResourcesResponse describeResourcesResponse = tencentCloudHttpConfig.getTencentClbClient(cloudAccountEntity.getId(), commonRequest.getRegion_id()).DescribeResources(new DescribeResourcesRequest());
        List<ZoneResource> azList = getZoneResourceList(describeResourcesResponse);
        List<AvailabilityZoneEntity> availabilityZoneList = new ArrayList<>();
        for (ZoneResource zoneResource : azList) {
            AvailabilityZoneEntity availabilityZone = new AvailabilityZoneEntity();
            availabilityZone.setZone_id(zoneResource.getMasterZone());
            availabilityZone.setZone_name("可用区" + zoneResource.getMasterZone().split(commonRequest.getRegion_id() + "-")[1]);
            availabilityZoneList.add(availabilityZone);
        }
        commonListResponse.setTotal(availabilityZoneList.size());
        commonListResponse.setList(CommonUtil.getPage(availabilityZoneList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }

    private List<ZoneResource> getZoneResourceList(DescribeResourcesResponse describeResourcesResponse) {
        return Arrays.stream(describeResourcesResponse.getZoneResourceSet()).filter(zone -> zone.getIPVersion().equals("IPv4")).flatMap(zone -> Arrays.stream(zone.getResourceSet()).filter(resource -> resource.getIsp().equals("BGP")).flatMap(resource -> Arrays.stream(resource.getTypeSet()).filter(type -> type.getType().equals("BGP")).flatMap(type -> Arrays.stream(type.getSpecAvailabilitySet()).filter(specAvailability -> specAvailability.getSpecType().equals("shared") && specAvailability.getAvailability().equals("Available")).map(specAvailability -> zone)))).collect(Collectors.toList());
    }

    private void getHuaweiCloudElbZoneList(CommonRequest commonRequest, CommonListResponse commonListResponse, CloudAccountEntity cloudAccountEntity) {
        ElbClient elbClient = huaweiCloudHttpConfig.getHuaweiElbV3Client(cloudAccountEntity.getId(), commonRequest.getRegion_id());
        ListAvailabilityZonesResponse listAvailabilityZonesResponse = elbClient.listAvailabilityZones(new ListAvailabilityZonesRequest());
        List<AvailabilityZoneEntity> availabilityZoneList = new ArrayList<>();
        for (List<AvailabilityZone> availabilityZones : listAvailabilityZonesResponse.getAvailabilityZones()) {
            for (AvailabilityZone availabilityZone : availabilityZones) {
                AvailabilityZoneEntity zoneEntity = new AvailabilityZoneEntity();
                zoneEntity.setZone_id(availabilityZone.getCode());
                if (availabilityZone.getCode().startsWith(commonRequest.getRegion_id()) && (availabilityZone.getCode().length() - 1) == commonRequest.getRegion_id().length()) {
                    zoneEntity.setZone_name(CommonUtil.translateToChinese(availabilityZone.getCode().substring(commonRequest.getRegion_id().length())));
                } else {
                    zoneEntity.setZone_name(availabilityZone.getCode());
                }
                if (!availabilityZoneList.isEmpty()) {
                    boolean exists = availabilityZoneList.stream().anyMatch(item -> item.getZone_id().equals(zoneEntity.getZone_id()));
                    if (!exists) {
                        availabilityZoneList.add(zoneEntity);
                    }
                } else {
                    availabilityZoneList.add(zoneEntity);
                }
            }
        }
        availabilityZoneList = availabilityZoneList.stream().sorted(Comparator.comparing(AvailabilityZoneEntity::getZone_name)).collect(Collectors.toList());
        commonListResponse.setTotal(availabilityZoneList.size());
        commonListResponse.setList(CommonUtil.getPage(availabilityZoneList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }

}
