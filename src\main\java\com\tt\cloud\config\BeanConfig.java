package com.tt.cloud.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.lark.oapi.Client;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2021/9/2 13:59
 */
@EnableAsync
@Configuration
public class BeanConfig {
  @Value("${oapi.appId}")
  private String appId;
  @Value("${oapi.appSecret}")
  private String appSecret;

  /**
   * 自定义线程池
   *
   * @return 线程池
   */
  @Bean("executorService")
  public ExecutorService getExecutorService() {
    return new ThreadPoolExecutor(
            10,
            20,
            1L,
            TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(1000),
            new ThreadPoolExecutor.DiscardPolicy());
  }

  @Bean
  public Cache<String, Object> caffeineCache() {
    return Caffeine.newBuilder()
            // 设置最后一次写入或访问后两个小时后过期
            .expireAfterWrite(7200, TimeUnit.SECONDS)
            // 初始的缓存空间大小
            .initialCapacity(100)
            // 缓存的最大条数
            .maximumSize(1000)
            .build();
  }


  @Bean("flyBookClient")
  public Client getClient() {
    return Client.newBuilder(appId, appSecret).build();
  }

}
