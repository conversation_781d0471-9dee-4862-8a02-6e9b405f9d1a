package com.tt.cloud.util;

import com.huaweicloud.sdk.core.exception.ClientRequestException;
import com.huaweicloud.sdk.elb.v3.model.Flavor;
import com.tt.cloud.bean.HuaweiCloudResponse;
import com.tt.cloud.exception.HuaweiErrorCode;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.expression.BeanFactoryAccessor;
import org.springframework.core.io.ClassPathResource;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/25 14:10
 */
public class Utils {

    private Utils() {
    }

    public static String getFlavorName(Flavor l4flavor) {
        String flavorName = StringUtils.EMPTY;
        if (l4flavor.getName().endsWith(".small")) {
            flavorName += "小型";
        } else if (l4flavor.getName().endsWith(".medium")) {
            flavorName += "中型";
        } else if (l4flavor.getName().endsWith(".large")) {
            flavorName += "大型";
        }
        if (l4flavor.getName().contains("s1")) {
            flavorName += "I";
        }
        if (l4flavor.getName().contains("s2")) {
            flavorName += "II";
        }
        return flavorName;
    }

    public static String readTxtFile(String filePath) throws Exception {
        StringBuilder content = new StringBuilder();
        ClassPathResource resource = new ClassPathResource(filePath);
        try(InputStream stream = resource.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(stream))) {
            String lineTxt;
            while((lineTxt = reader.readLine()) != null){
                content.append(lineTxt).append("\n");
            }
        } catch (IOException e) {
            throw new Exception(e);
        }
        return content.toString();
    }

    public static void setHuaweiClodResponseErrorMsg(HuaweiCloudResponse response, Exception e,
            String errMsg) {
        if (e instanceof ClientRequestException) {
            String codeMsg = HuaweiErrorCode.getErrorMsg(
                    ((ClientRequestException) e).getErrorMsg());
            if (StringUtils.isEmpty(codeMsg)) {
                codeMsg = e.getMessage();
            }
            response.getHeader().setErrorMsg(errMsg + "：" + codeMsg );
        } else {
            response.getHeader().setErrorMsg(errMsg + "：" + e.getMessage());
        }
    }

    public static String getPassword() {
        char[] num = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
        char[] english = new char[]{'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};
        char[] ENGLISH = new char[]{'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};
        char[] special = new char[]{'-', '_', '#', '.', '@', '$', '^', '*', '!'};
        String password = "";

        password += getOne(ENGLISH);
        password += getOne(english);
        password += getOne(special);
        password += getOne(num);
        password += getOne(num);
        password += getOne(num);
        password += getOne(ENGLISH);
        password += getOne(english);
        password += getOne(special);
        password += getOne(num);
        return password;
    }

    public static char getOne(char[] num) {
        return num[(int) Math.floor(Math.random() * num.length)];
    }

    public static String getParseValue(ApplicationContext applicationContext,
            Map<String, Object> parameterMap, String content) {
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext standardEvaluationContext = new StandardEvaluationContext(applicationContext);
        standardEvaluationContext.addPropertyAccessor(new BeanFactoryAccessor());
        standardEvaluationContext.setVariables(parameterMap);
        Expression expression = parser.parseExpression(content, new TemplateParserContext());
        return expression.getValue(standardEvaluationContext, String.class);
    }
}

