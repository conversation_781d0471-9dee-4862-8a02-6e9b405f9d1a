package com.tt.cloud.bean.v2;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tt.cloud.bean.ResponseHeader;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/9/8 16:18
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LBCreateResponse {
    private ResponseHeader header = new ResponseHeader();
    private String lbId;
    private String eipId;
}
