package com.tt.cloud.handler.volcengine;

import com.tt.cloud.bean.*;
import com.tt.cloud.config.VolcEngineCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import com.volcengine.iam.model.*;
import com.alibaba.fastjson.JSON;

import lombok.var;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class VolcEngineCloudUserHandler {

    @Resource
    private VolcEngineCloudHttpConfig volcEngineCloudHttpConfig;

    @Resource
    private CloudAccountDao cloudAccountDao;

    @Resource
    private FlyBookService flyBookService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public VolacEngineCloudResponse queryUserExists(UserEntity userEntity) {
        VolacEngineCloudResponse volacEngineCloudResponse = new VolacEngineCloudResponse();
        try {
            UserAuthEntity userAuthEntity = getAuthEntity(userEntity);
            if (null != userAuthEntity) {
                UserEntity user = new UserEntity();
                user.setUserId(userAuthEntity.getUid());
                user.setUserName(userAuthEntity.getUname());
                volacEngineCloudResponse.setUserEntity(user);
            } else {
                volacEngineCloudResponse.setUserEntity(queryUser(userEntity));
            }
            if (null != volacEngineCloudResponse.getUserEntity()) {
                CloudAccountEntity cloudAccount
                        = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
                String loginUrl = "https://console.volcengine.com/auth/login/federation/?identity=" + cloudAccount.getDomainId();
                volacEngineCloudResponse.setLoginUrl(loginUrl);
            }
        } catch (Exception e) {
            log.error("火山云查询用户是否存在异常", e);
            volacEngineCloudResponse.getHeader().setErrorMsg("火山云查询用户是否存在异常：" + e.getMessage());
        }
        return volacEngineCloudResponse;
    }

    public UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        try {
            GetUserRequest getUserRequest = new GetUserRequest();
            getUserRequest.setUserName(userEntity.getUserName());
            GetUserResponse getUserResponse = volcEngineCloudHttpConfig.getVolcEngineIamClient(userEntity.getAccountId()).getUser(getUserRequest);
            if (null != getUserResponse.getUser()) {
                userInfo = new UserEntity();
                userInfo.setUserId(String.valueOf(getUserResponse.getUser().getId()));
                userInfo.setUserName(getUserResponse.getUser().getUserName());
            }
        } catch (Exception e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("Not Found")) {
                if (!userEntity.getUserName().contains("@52tt.com")) {
                    userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                    return queryUser(userEntity);
                }
            }
        }
        return userInfo;
    }

    private UserAuthEntity getAuthEntity(UserEntity userEntity) {
        UserAuthEntity userAuthInfo = new UserAuthEntity();
        userAuthInfo.setAccountId(userEntity.getAccountId());
        userAuthInfo.setUname(userEntity.getUserName());
        return cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
    }

    public List<UserGroupInfo> queryAllGroupList() throws Exception {
        List<UserGroupInfo> groupList = new ArrayList<>();
        List<UserGroupForListGroupsOutput> groups = new ArrayList<>();
        List<CloudAccountEntity> accountList = cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
        for (CloudAccountEntity cloudAccount : accountList) {
            if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccount.getCloudType())) {
                ListGroupsRequest listGroupsRequest = new ListGroupsRequest();
                listGroupsRequest.setLimit(1000);
                ListGroupsResponse listGroupsResponse = volcEngineCloudHttpConfig.getVolcEngineIamClient(cloudAccount.getId()).listGroups(listGroupsRequest);
                if (null != listGroupsResponse.getUserGroups() && !listGroupsResponse.getUserGroups().isEmpty()) {
                    if (groups.isEmpty()) {
                        groups.addAll(listGroupsResponse.getUserGroups());
                    } else {
                        groups = groups.stream().filter(item -> listGroupsResponse.getUserGroups()
                                .stream().anyMatch(item1 -> item.getUserGroupName()
                                        .equals(item1.getUserGroupName()))).collect(Collectors.toList());
                    }
                }
            }
        }
        for (UserGroupForListGroupsOutput listGroupsResponseBodyGroupsGroup : groups) {
            UserGroupInfo userGroupInfo = new UserGroupInfo();
            userGroupInfo.setGroupId(listGroupsResponseBodyGroupsGroup.getUserGroupName());
            userGroupInfo.setGroupName(listGroupsResponseBodyGroupsGroup.getUserGroupName());
            groupList.add(userGroupInfo);
        }
        return groupList;
    }

        /**
     * 查询用户组列表
     *
     * @param groupEntity groupEntity
     * @return VolacEngineCloudResponse
     */
    public VolacEngineCloudResponse queryGroupList(GroupEntity groupEntity) {
        VolacEngineCloudResponse aliCloudResponse = new VolacEngineCloudResponse();
        try {
            log.info("查询用户组列表");
            ListGroupsRequest listGroupsRequest = new ListGroupsRequest();
            listGroupsRequest.setLimit(1000);
            ListGroupsResponse listGroupsResponse = volcEngineCloudHttpConfig.getVolcEngineIamClient(groupEntity.getAccountId()).listGroups(listGroupsRequest);
            List<GroupEntity> groupList = getGroupList(listGroupsResponse);
            if (StringUtils.isNotEmpty(groupEntity.getGroupName())) {
                groupList = groupList.stream().filter(item -> item.getGroupName().contains(groupEntity.getGroupName())).collect(
                        Collectors.toList());
            }
            aliCloudResponse.setGroupList(groupList);
        } catch (Exception e) {
            log.error("查询用户组列表异常", e);
            aliCloudResponse.getHeader().setErrorMsg("查询用户组列表异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

     /**
     * 查詢用戶归属的用户组列表
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryListGroupsForUser(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("🌋云用户所属用户组列表:{}", groupEntity.getUserName());
            ListGroupsForUserRequest listGroupsForUserRequest = new ListGroupsForUserRequest();
            listGroupsForUserRequest.setUserName(groupEntity.getUserName());
            ListGroupsForUserResponse listGroupsForUserResponse = 
                    volcEngineCloudHttpConfig.getVolcEngineIamClient(groupEntity.getAccountId()).listGroupsForUser(listGroupsForUserRequest);
            List<GroupEntity> groupList = new ArrayList<>();
            for (UserGroupForListGroupsForUserOutput item : listGroupsForUserResponse.getUserGroups()) {
                GroupEntity group = new GroupEntity();
                group.setGroupId(item.getUserGroupName());
                group.setGroupName(item.getUserGroupName());
                group.setCreateTime(item.getJoinDate());
                group.setMemo(item.getDescription());
                groupList.add(group);
            }
            aliCloudResponse.setGroupList(groupList);
        } catch (Exception e) {
            log.error("🌋云用户所属用户组列表异常", e);
            aliCloudResponse.getHeader().setErrorMsg("🌋云用户所属用户组列表异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }


    private List<GroupEntity> getGroupList(ListGroupsResponse listGroupsResponse) {
        List<GroupEntity> groupList = new ArrayList<>();
        for (UserGroupForListGroupsOutput listGroupsResponseBodyGroupsGroup : listGroupsResponse.getUserGroups()) {
            GroupEntity group = new GroupEntity();
            group.setGroupId(listGroupsResponseBodyGroupsGroup.getUserGroupName());
            group.setGroupName(listGroupsResponseBodyGroupsGroup.getUserGroupName());
            group.setCreateTime(listGroupsResponseBodyGroupsGroup.getCreateDate());
            group.setMemo(listGroupsResponseBodyGroupsGroup.getDescription());
            groupList.add(group);
        }
        return groupList;
    }


     /**
     * 创建用户
     *
     * @param userEntity userEntity
     * @return VolacEngineCloudResponse
     */
    public VolacEngineCloudResponse createUser(UserEntity userEntity) {
        VolacEngineCloudResponse aliCloudResponse = new VolacEngineCloudResponse();
        try {
            log.info("🌋云新增用户：{}", userEntity);
            CreateUserRequest request = new CreateUserRequest();
            request.setUserName(userEntity.getUserName());
            request.setDisplayName(userEntity.getUserName());
            request.setEmail(userEntity.getEmail());
            request.setMobilePhone(userEntity.getPhone());
            request.setDescription(userEntity.getRemark());

            volcEngineCloudHttpConfig.getVolcEngineIamClient(userEntity.getAccountId()).createUser(request);
            log.info("🌋云新增用户完毕");
            if (null != userEntity.getGroupNameList() && !userEntity.getGroupNameList().isEmpty()) {
                log.info("将用户添加到用户组中：{}", userEntity.getGroupNameList());
                for (String groupName : userEntity.getGroupNameList()) {
                    AddUserToGroupRequest addUserToGroupRequest = new AddUserToGroupRequest();
                    addUserToGroupRequest.setUserName(userEntity.getUserName());
                    addUserToGroupRequest.setUserGroupName(groupName);
                   volcEngineCloudHttpConfig.getVolcEngineIamClient(userEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                }
            }
            if (!userEntity.isApiUser()) {
                CreateLoginProfileRequest createLoginProfileRequest = new CreateLoginProfileRequest();
                createLoginProfileRequest.setUserName(userEntity.getUserName());
                createLoginProfileRequest.setPassword(Utils.getPassword());
                // createLoginProfileRequest.setMFABindRequired(false);
                createLoginProfileRequest.setLoginAllowed(true);
                createLoginProfileRequest.setPasswordResetRequired(true);
                volcEngineCloudHttpConfig.getVolcEngineIamClient(userEntity.getAccountId()).createLoginProfile(createLoginProfileRequest);
                userEntity.setPassword(createLoginProfileRequest.getPassword());
                sendMessage(userEntity);
            }
            aliCloudResponse.setUserEntity(userEntity);
        } catch (Exception e) {
            log.error("🌋云新增用户异常", e);
            aliCloudResponse.getHeader().setErrorMsg("🌋云新增用户异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }


    private void sendMessage(UserEntity userEntity) throws Exception {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
        Map<String, Object> messageParam = new HashMap<>();
        messageParam.put("cloud", cloudAccount.getMemo() + "/" + cloudAccount.getName() + "/" + cloudAccount.getDomainId());
        messageParam.put("username", userEntity.getUserName());
        messageParam.put("password", userEntity.getPassword());
        messageParam.put("url", "*由于开启云商单点登录，关闭密码登录方式*\\n*请通过[运维平台](https://yw-sso.ttyuyin.com/index/dashboard)->"
                + "[云泽平台](https://yw-cloud.ttyuyin.com/tt-cloud/cloud/center)进行登录*");
        String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
        content = Utils.getParseValue(applicationContext, messageParam, content);
        flyBookService.sendMessage(userEntity.getEmail(), content);
    }


        /**
     * 用户加入用户组
     *
     * @param groupEntity groupEntity
     * @return VolacEngineCloudResponse
     */
    public VolacEngineCloudResponse addUserToGroup(GroupEntity groupEntity) {
        VolacEngineCloudResponse aliCloudResponse = new VolacEngineCloudResponse();
        try {
            log.info("🌋云用户加入用户组:{}", groupEntity);
            AddUserToGroupRequest addUserToGroupRequest = new AddUserToGroupRequest();
            if (null != groupEntity.getGroupNameList() && !groupEntity.getGroupNameList().isEmpty()) {
                for (String groupName : groupEntity.getGroupNameList()) {
                    addUserToGroupRequest.setUserGroupName(groupName);
                    addUserToGroupRequest.setUserName(groupEntity.getUserName());
                     volcEngineCloudHttpConfig.getVolcEngineIamClient(groupEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                }
            }
            if (null != groupEntity.getGroupName()) {
                if (null != groupEntity.getUserNameList() && !groupEntity.getUserNameList().isEmpty()) {
                    for (String userName : groupEntity.getUserNameList()) {
                        addUserToGroupRequest.setUserGroupName(groupEntity.getGroupName());
                        addUserToGroupRequest.setUserName(userName);
                        volcEngineCloudHttpConfig.getVolcEngineIamClient(groupEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                    }
                }
            }
        } catch (Exception e) {
            log.error("🌋云用户加入用户组异常", e);
            aliCloudResponse.getHeader().setErrorMsg("🌋云用户加入用户组异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    public CloudAllUserResponse queryUserListByAccount(CloudAccountEntity cloudAccount, String nextMarker) {
        CloudAllUserResponse userListRst = new CloudAllUserResponse();
        try {
            log.info("🌋云子用户详情列表");

            // userList 先从redis里获取，如果没有，再调用接口获取，然后存入redis
            // redis key: volcengine-all-user-list
            // redis value: userList
            // redis 过期时间 20 分钟
            List<UserEntity> userList = new ArrayList<>();
            String redisKey = "volcengine-all-user-list:" + cloudAccount.getId();

            try {
                // 先从 Redis 获取用户列表
                String cachedUserListJson = stringRedisTemplate.opsForValue().get(redisKey);
                if (cachedUserListJson != null) {
                    log.info("从Redis缓存获取到🌋云用户列表，key: {}", redisKey);
                    // 将JSON字符串转换为用户列表
                    userList = JSON.parseArray(cachedUserListJson, UserEntity.class);
                } else {
                    log.info("Redis缓存中未找到🌋云用户列表，开始调用API获取，key: {}", redisKey);
                    // Redis中没有数据，调用火山云API获取
                    ListUsersRequest request = new ListUsersRequest();
                    request.setLimit(20); // 火山云支持最大1000条记录
                    request.setOffset(0);
                    ListUsersResponse response = volcEngineCloudHttpConfig.getVolcEngineIamClient(cloudAccount.getId()).listUsers(request);

                    if (response != null && response.getUserMetadata() != null && !response.getUserMetadata().isEmpty()) {
                        for (var user : response.getUserMetadata()) {
                            UserEntity userEntity = new UserEntity();
                            userEntity.setUserId(String.valueOf(user.getId()));
                            userEntity.setUserIn(String.valueOf(user.getId())); // 火山云没有UIN概念，使用Id
                            userEntity.setUserName(safeGetString(user.getUserName()));
                            userEntity.setCreateTime(safeGetString(user.getCreateDate()));
                            userEntity.setRemark(safeGetString(user.getDescription()));
                            userEntity.setPhone(safeGetString(user.getMobilePhone()));
                            userEntity.setEmail(safeGetString(user.getEmail()));
                            userEntity.setConsoleLogin(0L); // 火山云默认设置为0，后续可通过其他API获取
                            userEntity.setAccountId(cloudAccount.getDomainId());
                            userList.add(userEntity);
                        }

                        // 将用户列表存入Redis，过期时间20分钟
                        String userListJson = JSON.toJSONString(userList);
                        stringRedisTemplate.opsForValue().set(redisKey, userListJson, 20, TimeUnit.MINUTES);
                        log.info("已将🌋云用户列表存入Redis缓存，key: {}, 用户数量: {}", redisKey, userList.size());
                    }
                }
            } catch (Exception e) {
                log.error("Redis操作异常，直接调用API获取用户列表: {}", e.getMessage(), e);
            }

            // nextMarker 有字符串转为int，如果不是数字型或空，则默认为0
            int pageIndex = 0;
            if (nextMarker != null && !nextMarker.isEmpty()) {
                try {
                    pageIndex = Integer.parseInt(nextMarker);
                    if (pageIndex < 0) {
                        pageIndex = 0; // 负数也默认为0
                    }
                } catch (NumberFormatException e) {
                    log.warn("nextMarker格式错误，无法转换为整数: {}, 使用默认值0", nextMarker);
                    pageIndex = 0;
                }
            }

            // 处理分页逻辑：将nextMarker转为int，并从userList中切片
            int size = 20;
            int startIndex = pageIndex * size;
            int endIndex = (pageIndex + 1) * size;
            List<UserEntity> pagedUserList = userList;
            if (startIndex >= userList.size()) {
                return userListRst;
            }
            if (endIndex > userList.size()) {
                endIndex = userList.size();
                userListRst.setNextMarker(Strings.EMPTY);
            } else {
                userListRst.setNextMarker(String.valueOf(pageIndex + 1));
            }
            pagedUserList = userList.subList(startIndex, endIndex);

            // 对分页后的用户列表进行详细信息补充
            attachUserInfos(pagedUserList, cloudAccount.getId());

            // 设置返回结果
            userListRst.setUserList(pagedUserList);

        } catch (Exception e) {
            log.error("查询🌋云子用户详情列表异常", e);
            throw new RuntimeException("查询🌋云子用户详情列表异常" + e.getMessage());
        }
        return userListRst;
    }

    /**
     * 安全获取字符串值，如果为null则返回空字符串
     */
    private String safeGetString(String value) {
        return value != null ? value : "";
    }

    /**
     * 补充用户详细信息
     */
    private void attachUserInfos(List<UserEntity> userList, String accountId) {
        if (userList == null || userList.isEmpty()) {
            return;
        }
        try {
            // 获取用户信息
            for (int i = 0; i < userList.size(); i++) {
                UserEntity user = userList.get(i);

                // 1. 获取用户组列表
                try {
                    ListGroupsForUserRequest listGroupsRequest = new ListGroupsForUserRequest();
                    listGroupsRequest.setUserName(user.getUserName());
                    ListGroupsForUserResponse listGroupsResponse = volcEngineCloudHttpConfig.getVolcEngineIamClient(accountId).listGroupsForUser(listGroupsRequest);

                    if (listGroupsResponse != null && listGroupsResponse.getUserGroups() != null && !listGroupsResponse.getUserGroups().isEmpty()) {
                        List<UserGroupInfo> groupList = new ArrayList<>();
                        for (UserGroupForListGroupsForUserOutput group : listGroupsResponse.getUserGroups()) {
                            UserGroupInfo userGroupInfo = new UserGroupInfo();
                            userGroupInfo.setGroupId(safeGetString(group.getUserGroupName()));
                            userGroupInfo.setGroupName(safeGetString(group.getUserGroupName()));
                            userGroupInfo.setRemark(safeGetString(group.getDescription()));
                            groupList.add(userGroupInfo);

                            // 注意：火山云的用户组权限API可能需要不同的方法名
                            // 这里先跳过权限获取，后续可以根据实际API文档进行完善
                        }
                        user.setGroupList(groupList);
                    }
                } catch (Exception e) {
                    log.error("获取用户组信息异常: {}", e.getMessage());
                }

                // 2. 初始化用户权限列表和访问密钥列表
                user.setPolicyList(new ArrayList<>());
                user.setAccessKeyList(new ArrayList<>());

                // 注意：火山云的权限和访问密钥API可能需要不同的权限或方法名
                // 这里先设置为空列表，后续可以根据实际API文档进行完善

                userList.set(i, user);
            }
        } catch (Exception e) {
            log.error("🌋云完善用户详细信息异常", e);
        }
    }

}
