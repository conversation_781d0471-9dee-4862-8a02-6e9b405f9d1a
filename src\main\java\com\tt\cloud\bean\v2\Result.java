package com.tt.cloud.bean.v2;

import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.sso.metadata.MetadataBean;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/3/5 10:24
 */
@Getter
@Setter
@ToString
@Slf4j
public class Result {

    private final MetadataBean spMetadata;
    private final String aliLoginName;
    private CloudAccountEntity cloudAccountEntity;
    private String tencentUserName;
    private String volcEngineUserName;
    private String ksCloudUserName;

    public Result(MetadataBean spMetadata, String aliLoginName, CloudAccountEntity cloudAccountEntity,
                  String tencentUserName, String volcEngineUserName, String ksCloudUserName) {
        log.info("aliLoginName:{} tencentUserName:{} ksCloudUserName:{}", aliLoginName, tencentUserName, ksCloudUserName);
        this.spMetadata = spMetadata;
        this.aliLoginName = aliLoginName;
        this.cloudAccountEntity = cloudAccountEntity;
        this.tencentUserName = tencentUserName;
        this.volcEngineUserName = volcEngineUserName;
        this.ksCloudUserName = ksCloudUserName;
    }
}
