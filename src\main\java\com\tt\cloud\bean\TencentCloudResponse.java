package com.tt.cloud.bean;

import com.tencentcloudapi.cam.v20190116.models.StrategyInfo;
import com.tencentcloudapi.clb.v20180317.models.LoadBalancer;
import com.tencentcloudapi.clb.v20180317.models.ZoneResource;
import com.tencentcloudapi.vpc.v20170312.models.SecurityGroup;
import com.tencentcloudapi.vpc.v20170312.models.Subnet;
import com.tencentcloudapi.vpc.v20170312.models.Vpc;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/18 9:48
 */
@Getter
@Setter
@ToString
public class TencentCloudResponse {
    private ResponseHeader header = new ResponseHeader();
    private String userId;
    private UserEntity userEntity;
    private List<GroupEntity> groupList;
    private List<UserEntity> userList;
    private GroupEntity groupEntity;
    private List<PermissionEntity> attachPolicyInfos;
    private  List<StrategyInfo> strategyInfoList;
    private String loginUrl;
    private PermanentAccessKeyEntity permanentAccessKeyEntity;
    private List<PermanentAccessKeyEntity> permanentAccessKeyList;
    private List<LoadBalancer> LoadBalancerSet;
    private List<RegionEntity> regionList;
    private Vpc[] vpcList;
    private Subnet[] subnets;
    private List<ProjectEntity> projectList;
    private SecurityGroup[] securityGroupSet;
    private String trackId;

    private List<ZoneResource> azList;

    private String appId;
}
