package com.tt.cloud.controller.v2;

import com.aliyun.resourcemanager20200331.models.ListResourceGroupsRequest;
import com.aliyun.resourcemanager20200331.models.ListResourceGroupsResponse;
import com.aliyun.resourcemanager20200331.models.ListResourceGroupsResponseBody;
import com.aliyun.slb20140515.models.DescribeRegionsResponseBody;
import com.huaweicloud.sdk.eps.v1.EpsClient;
import com.huaweicloud.sdk.eps.v1.model.EpDetail;
import com.huaweicloud.sdk.eps.v1.model.ListEnterpriseProjectRequest;
import com.huaweicloud.sdk.eps.v1.model.ListEnterpriseProjectResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListRegionsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListRegionsResponse;
import com.huaweicloud.sdk.iam.v3.model.Region;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.dcdb.v20180411.DcdbClient;
import com.tencentcloudapi.dcdb.v20180411.models.DescribeProjectsRequest;
import com.tencentcloudapi.dcdb.v20180411.models.DescribeProjectsResponse;
import com.tencentcloudapi.dcdb.v20180411.models.Project;
import com.tencentcloudapi.region.v20220627.models.DescribeRegionsRequest;
import com.tencentcloudapi.region.v20220627.models.DescribeRegionsResponse;
import com.tencentcloudapi.region.v20220627.models.RegionInfo;
import com.tt.cloud.bean.*;
import com.tt.cloud.bean.v2.CommonListResponse;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.config.HuaweiCloudHttpConfig;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.config.VolcEngineCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.CommonUtil;
import com.volcengine.ApiException;
import com.volcengine.ecs.model.RegionForDescribeRegionsOutput;
import com.volcengine.iam20210801.model.ListProjectsRequest;
import com.volcengine.iam20210801.model.ListProjectsResponse;
import com.volcengine.iam20210801.model.ProjectForListProjectsOutput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * LB自动化创建接口
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/1/16 10:28
 */
@Slf4j
@RestController
@RequestMapping("/rest/v2")
public class CloudCommonV2Controller {
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private HuaweiCloudHttpConfig huaweiCloudHttpConfig;
    @Resource
    private TencentCloudHttpConfig tencentCloudHttpConfig;
    @Resource
    private VolcEngineCloudHttpConfig volcEngineCloudHttpConfig;
    @Resource
    private AliCloudHttpConfig aliCloudHttpConfig;

    @GetMapping("/cloud/list")
    public CommonListResponse queryCloudList(CommonRequest commonRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            List<CloudAccountEntity> cloudList = cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
            List<Map<String, String>> list = new ArrayList<>();
            for (CloudAccountEntity cloudAccount : cloudList) {
                if (cloudAccount.isResourceManage()) {
                    Map<String, String> cloudInfo = new HashMap<>();
                    cloudInfo.put("cloud_id", cloudAccount.getDomainId());
                    cloudInfo.put("cloud_name", getShowName(cloudAccount));
                    cloudInfo.put("cloud_type", cloudAccount.getCloudType());
                    list.add(cloudInfo);
                }
            }
            commonListResponse.setTotal(list.size());
            commonListResponse.setList(CommonUtil.getPage(list, commonRequest.getPageNo(), commonRequest.getPageSize()));
        } catch (Exception e) {
            log.error("查询云账号列表异常", e);
            commonListResponse.getHeader().setErrorMsg("查询云账号列表异常：" + e.getMessage());
        }
        return commonListResponse;
    }

    @GetMapping("/cloud/region/list")
    public CommonListResponse queryRegionList(CommonRequest commonRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isNotEmpty(commonRequest.getCloud_id())) {
                CloudAccountEntity cloudAccountEntity =
                        cloudAccountDao.queryCloudAccountInfoByCloudId(commonRequest.getCloud_id());
                if (null != cloudAccountEntity) {
                    if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                        getHuaweiCloudRegionList(commonListResponse, cloudAccountEntity, commonRequest);
                    } else if (Constants.TENCENT_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                        getTencentCloudRegionList(commonListResponse, cloudAccountEntity, commonRequest);
                    } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                        getVolcEngineRegionList(commonListResponse, cloudAccountEntity, commonRequest);
                    } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                        getAliCloudRegionList(commonListResponse, cloudAccountEntity, commonRequest);
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询云商地区列表异常", e);
            commonListResponse.getHeader().setErrorMsg("查询云商地区列表异常：" + e.getMessage());
        }
        return commonListResponse;
    }

    @GetMapping("/cloud/project/list")
    public CommonListResponse queryEnterpriseProjectList(CommonRequest commonRequest) {
        CommonListResponse commonListResponse = new CommonListResponse();
        try {
            if (StringUtils.isEmpty(commonRequest.getCloud_id())
                    || StringUtils.isEmpty(commonRequest.getRegion_id())) {
                return commonListResponse;
            }
            CloudAccountEntity cloudAccountEntity =
                    cloudAccountDao.queryCloudAccountInfoByCloudId(commonRequest.getCloud_id());
            if (null == cloudAccountEntity) {
                return commonListResponse;
            }
            if (Constants.HUAWEI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getHuaweiCloudProjectList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.TENCENT_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getTencentCloudProjectList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getVolcEngineProjectList(commonRequest, commonListResponse, cloudAccountEntity);
            } else if (Constants.ALI_CLOUD.equals(cloudAccountEntity.getCloudType())) {
                getAliCloudProjectList(commonRequest, commonListResponse, cloudAccountEntity);
            }
        } catch (Exception e) {
            log.error("查询云商企业项目列表异常", e);
            commonListResponse.getHeader().setErrorMsg("查询云商企业项目列表异常：" + e.getMessage());
        }
        return commonListResponse;
    }

    private void getAliCloudRegionList(CommonListResponse commonListResponse,
                                       CloudAccountEntity cloudAccountEntity,
                                       CommonRequest commonRequest) throws Exception {
        com.aliyun.slb20140515.models.DescribeRegionsRequest describeRegionsRequest = new com.aliyun.slb20140515.models.DescribeRegionsRequest();
        com.aliyun.slb20140515.models.DescribeRegionsResponse describeRegionsResponse =
                aliCloudHttpConfig.getSLBClient(cloudAccountEntity.getId(), commonRequest.getRegion_id()).describeRegions(describeRegionsRequest);
        List<RegionEntity> regionList = getRegionEntityList(describeRegionsResponse);
        commonListResponse.setList(regionList.size());
        commonListResponse.setList(CommonUtil.getPage(regionList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }

    private static List<RegionEntity> getRegionEntityList(com.aliyun.slb20140515.models.DescribeRegionsResponse describeRegionsResponse) {
        List<RegionEntity> regionList = new ArrayList<>();
        List<DescribeRegionsResponseBody.DescribeRegionsResponseBodyRegionsRegion> regionsOutputs = describeRegionsResponse.getBody().regions.region;
        for (DescribeRegionsResponseBody.DescribeRegionsResponseBodyRegionsRegion regionsOutput : regionsOutputs) {
            RegionEntity regionEntity = new RegionEntity();
            regionEntity.setRegion_name(regionsOutput.getLocalName());
            regionEntity.setRegion_id(regionsOutput.getRegionId());
            regionList.add(regionEntity);
        }
        return regionList;
    }

    private void getAliCloudProjectList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                          CloudAccountEntity cloudAccountEntity) throws Exception {
        ListResourceGroupsRequest listResourceGroupsRequest = new ListResourceGroupsRequest();
        listResourceGroupsRequest.setStatus("OK");
        listResourceGroupsRequest.setPageSize(100);
        ListResourceGroupsResponse listResourceGroupsResponse =
                aliCloudHttpConfig.getResourceClient(cloudAccountEntity.getId())
                        .listResourceGroups(listResourceGroupsRequest);
        List<ProjectEntity> projectList = getAliProjectEntities(listResourceGroupsResponse);
        limitProject(commonRequest, commonListResponse, projectList);
    }

    private List<ProjectEntity> getAliProjectEntities(ListResourceGroupsResponse listResourceGroupsResponse) {
        List<ProjectEntity> projectList = new ArrayList<>();
        for (ListResourceGroupsResponseBody.ListResourceGroupsResponseBodyResourceGroupsResourceGroup resourceGroup
                : listResourceGroupsResponse.body.resourceGroups.resourceGroup) {
            ProjectEntity projectEntity = new ProjectEntity();
            projectEntity.setProject_id(resourceGroup.getId());
            projectEntity.setProject_name(resourceGroup.getDisplayName());
            projectList.add(projectEntity);
        }
        return projectList;
    }

    private void getVolcEngineProjectList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                          CloudAccountEntity cloudAccountEntity) throws ApiException {
        ListProjectsRequest listProjectsRequest = new ListProjectsRequest();
        listProjectsRequest.setLimit(100);
        ListProjectsResponse listProjectsResponse =
                volcEngineCloudHttpConfig.getVolcEngineIam20210801Api(cloudAccountEntity.getId(), commonRequest.getRegion_id())
                        .listProjects(listProjectsRequest);

        List<ProjectEntity> projectList = new ArrayList<>();
        for (ProjectForListProjectsOutput project : listProjectsResponse.getProjects()) {
            ProjectEntity projectEntity = new ProjectEntity();
            projectEntity.setProject_id(project.getProjectName());
            projectEntity.setProject_name(project.getDisplayName());
            projectList.add(projectEntity);
        }
        limitProject(commonRequest, commonListResponse, projectList);
    }

    private void limitProject(CommonRequest commonRequest, CommonListResponse commonListResponse, List<ProjectEntity> projectList) {
        if (StringUtils.isNotEmpty(commonRequest.getProject_name())) {
            projectList = projectList.stream().filter(projectEntity -> projectEntity.getProject_name().toLowerCase(
                    Locale.ROOT).contains(commonRequest.getProject_name().toLowerCase(Locale.ROOT))).collect(
                    Collectors.toList());
        }
        commonListResponse.setTotal(projectList.size());
        commonListResponse.setList(CommonUtil.getPage(projectList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }

    private void getTencentCloudProjectList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                            CloudAccountEntity cloudAccountEntity) throws TencentCloudSDKException {
        DcdbClient dcdbClient = tencentCloudHttpConfig
                .getTencentDcdbClient(cloudAccountEntity.getId(), commonRequest.getRegion_id());
        DescribeProjectsResponse describeProjectsResponse = dcdbClient.DescribeProjects(
                new DescribeProjectsRequest());
        List<ProjectEntity> projectList = new ArrayList<>();
        for (Project project : describeProjectsResponse.getProjects()) {
            ProjectEntity projectEntity = new ProjectEntity();
            projectEntity.setProject_id(String.valueOf(project.getProjectId()));
            projectEntity.setProject_name(project.getName());
            projectList.add(projectEntity);
        }
        limitProject(commonRequest, commonListResponse, projectList);
    }

    private void getHuaweiCloudProjectList(CommonRequest commonRequest, CommonListResponse commonListResponse,
                                           CloudAccountEntity cloudAccountEntity) {
        EpsClient epsClient = huaweiCloudHttpConfig
                .getHuaweiEpsClient(cloudAccountEntity.getId());
        ListEnterpriseProjectRequest listEnterpriseProjectRequest = new ListEnterpriseProjectRequest();
        if (StringUtils.isNotEmpty(commonRequest.getProject_name())) {
            listEnterpriseProjectRequest.setName(commonRequest.getProject_name());
        }
        ListEnterpriseProjectResponse listEnterpriseProjectResponse
                = epsClient.listEnterpriseProject(listEnterpriseProjectRequest);
        List<ProjectEntity> projectList = new ArrayList<>();
        for (EpDetail enterpriseProject : listEnterpriseProjectResponse.getEnterpriseProjects()) {
            ProjectEntity projectEntity = new ProjectEntity();
            projectEntity.setProject_id(enterpriseProject.getId());
            projectEntity.setProject_name(enterpriseProject.getName());
            projectList.add(projectEntity);
        }
        commonListResponse.setTotal(projectList.size());
        commonListResponse.setList(CommonUtil.getPage(projectList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }

    private void getTencentCloudRegionList(CommonListResponse commonListResponse,
                                           CloudAccountEntity cloudAccountEntity, CommonRequest commonRequest)
            throws TencentCloudSDKException {
        DescribeRegionsRequest describeRegionsRequest = new DescribeRegionsRequest();
        describeRegionsRequest.setProduct("clb");
        DescribeRegionsResponse describeRegionsResponse =
                tencentCloudHttpConfig.getTencentRegionClient(cloudAccountEntity.getId())
                        .DescribeRegions(describeRegionsRequest);
        List<RegionEntity> regionList = new ArrayList<>();
        for (RegionInfo regionInfo : describeRegionsResponse.getRegionSet()) {
            RegionEntity regionEntity = new RegionEntity();
            regionEntity.setRegion_id(regionInfo.getRegion());
            regionEntity.setRegion_name(regionInfo.getRegionName());
            regionList.add(regionEntity);
        }
        regionList = filterRegion(commonRequest, regionList);
        commonListResponse.setTotal(regionList.size());
        commonListResponse.setList(CommonUtil.getPage(regionList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }


    private void getVolcEngineRegionList(CommonListResponse commonListResponse,
                                         CloudAccountEntity cloudAccountEntity,
                                         CommonRequest commonRequest) throws ApiException {
        com.volcengine.ecs.model.DescribeRegionsRequest describeRegionsRequest = new com.volcengine.ecs.model.DescribeRegionsRequest();
        describeRegionsRequest.setMaxResults(100);
        com.volcengine.ecs.model.DescribeRegionsResponse describeRegionsResponse =
                volcEngineCloudHttpConfig.getVolcEngineEcsApi(cloudAccountEntity.getId(), "")
                        .describeRegions(new com.volcengine.ecs.model.DescribeRegionsRequest());
        List<RegionEntity> regionList = new ArrayList<>();
        List<com.volcengine.ecs.model.RegionForDescribeRegionsOutput> regionsOutputs = describeRegionsResponse.getRegions();
        for (RegionForDescribeRegionsOutput regionsOutput : regionsOutputs) {
            RegionEntity regionEntity = new RegionEntity();
            regionEntity.setRegion_id(regionsOutput.getRegionId());
            regionEntity.setRegion_name(regionsOutput.getRegionId());
            String regionName = Constants.VOLC_ENGINE_REGION_MAP.get(regionsOutput.getRegionId());
            if (StringUtils.isNotEmpty(regionName)) {
                regionEntity.setRegion_name(regionName);
            }
            regionList.add(regionEntity);
        }
        commonListResponse.setList(regionList.size());
        commonListResponse.setList(CommonUtil.getPage(regionList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }

    private List<RegionEntity> filterRegion(CommonRequest commonRequest,
                                            List<RegionEntity> regionList) {
        if (StringUtils.isNotEmpty(commonRequest.getRegion_name())) {
            regionList = regionList.stream()
                    .filter(regionEntity -> regionEntity.getRegion_name().toLowerCase(
                                    Locale.ROOT)
                            .contains(commonRequest.getRegion_name().toLowerCase(Locale.ROOT)))
                    .collect(
                            Collectors.toList());
        }
        return regionList;
    }

    private void getHuaweiCloudRegionList(CommonListResponse commonListResponse,
                                          CloudAccountEntity cloudAccountEntity, CommonRequest commonRequest) {
        KeystoneListRegionsResponse keystoneListRegionsResponse =
                huaweiCloudHttpConfig.getHuaweiIamClient(cloudAccountEntity.getId())
                        .keystoneListRegions(new KeystoneListRegionsRequest());
        List<RegionEntity> regionList = new ArrayList<>();
        for (Region region : keystoneListRegionsResponse.getRegions()) {
            RegionEntity regionEntity = new RegionEntity();
            regionEntity.setRegion_id(region.getId());
            regionEntity.setRegion_name(region.getLocales().getZhCn());
            regionList.add(regionEntity);
        }
        regionList = filterRegion(commonRequest, regionList);
        commonListResponse.setTotal(regionList.size());
        commonListResponse.setList(CommonUtil.getPage(regionList, commonRequest.getPageNo(), commonRequest.getPageSize()));
    }

    private String getShowName(CloudAccountEntity cloudAccount) {
        if (Constants.HUAWEI_CLOUD.equals(cloudAccount.getCloudType())) {
            return "华为云/" + cloudAccount.getName();
        }
        if (Constants.TENCENT_CLOUD.equals(cloudAccount.getCloudType())) {
            return "腾讯云/" + cloudAccount.getName();
        }
        if (Constants.ALI_CLOUD.equals(cloudAccount.getCloudType())) {
            return "阿里云/" + cloudAccount.getName();
        }
        if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccount.getCloudType())) {
            return "火山云/" + cloudAccount.getName();
        }
        return cloudAccount.getName();
    }

}
