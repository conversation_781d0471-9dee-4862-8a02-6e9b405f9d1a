package com.tt.cloud.service;

import com.tt.cloud.bean.TmpToken;
import com.tt.cloud.bean.UserInfo;
import com.tt.cloud.dao.TmpTokenDao;
import com.tt.cloud.dao.UserDao;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class UserServiceImpl {
    @Resource
    private UserDao userDao;

    @Resource
    private TmpTokenDao tmpTokenDao;

    public UserInfo queryUserInfo(String userId) {
        return userDao.queryUserInfo(userId);
    }

    public TmpToken queryTmpTokenByToken(String token) {
        return tmpTokenDao.queryTmpTokenByToken(token);
    }

    public void removeTmpTokenById(int id) {
        tmpTokenDao.removeTmpTokenById(id);
    }
}
