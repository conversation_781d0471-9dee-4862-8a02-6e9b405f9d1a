package com.tt.cloud.handler.aws;

import com.tt.cloud.bean.AwsCloudResponse;
import com.tt.cloud.bean.GroupEntity;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.config.AwsCloudHttpConfig;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.services.iam.model.AddUserToGroupRequest;
import software.amazon.awssdk.services.iam.model.CreateGroupRequest;
import software.amazon.awssdk.services.iam.model.DeleteGroupRequest;
import software.amazon.awssdk.services.iam.model.GetGroupRequest;
import software.amazon.awssdk.services.iam.model.GetGroupResponse;
import software.amazon.awssdk.services.iam.model.Group;
import software.amazon.awssdk.services.iam.model.ListGroupsForUserRequest;
import software.amazon.awssdk.services.iam.model.ListGroupsForUserResponse;
import software.amazon.awssdk.services.iam.model.ListGroupsResponse;
import software.amazon.awssdk.services.iam.model.ListUsersResponse;
import software.amazon.awssdk.services.iam.model.RemoveUserFromGroupRequest;
import software.amazon.awssdk.services.iam.model.UpdateGroupRequest;
import software.amazon.awssdk.services.iam.model.User;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/23 18:51
 */
@Slf4j
@Component
public class AwsCloudUserGroupHandler {
    @Resource
    private AwsCloudHttpConfig awsCloudHttpConfig;

    /**
     * 查询aws用户组列表
     *
     * @param groupEntity groupEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse queryGroupList(GroupEntity groupEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            ListGroupsResponse groupsResponse = awsCloudHttpConfig.getAWSIamClient(groupEntity.getAccountId()).listGroups();
            List<GroupEntity> groupList = new ArrayList<>();
            List<Group> groups = groupsResponse.groups();
            if (null != groups && !groups.isEmpty()) {
                for (Group group : groups) {
                    GroupEntity groupEntity1 = new GroupEntity();
                    groupEntity1.setGroupId(group.groupId());
                    groupEntity1.setGroupName(group.groupName());
                    groupEntity1.setCreateTime(group.createDate().toString());
                    groupList.add(groupEntity1);
                }
                if (StringUtils.isNotEmpty(groupEntity.getGroupName())) {
                    groupList = groupList.stream()
                            .filter(item -> item.getGroupName().toLowerCase(Locale.ROOT)
                                    .contains(groupEntity.getGroupName().toLowerCase(Locale.ROOT)))
                            .collect(Collectors.toList());
                }
            }
            groupList.sort((t1, t2) -> t2.getCreateTime().compareTo(t1.getCreateTime()));
            awsCloudResponse.setGroupList(groupList);
        } catch (Exception e) {
            log.error("查询aws用户组列表异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询aws用户组列表异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 查询aws用户所属的分组列表
     *
     * @param groupEntity groupEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse queryListGroupsForUser(GroupEntity groupEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            ListGroupsForUserResponse listGroupsForUserResponse =
                    awsCloudHttpConfig.getAWSIamClient(groupEntity.getAccountId()).listGroupsForUser(
                            ListGroupsForUserRequest.builder().userName(groupEntity.getUserName())
                                    .build()
                    );
            List<GroupEntity> groupList = new ArrayList<>();
            if (null != listGroupsForUserResponse.groups() && !listGroupsForUserResponse.groups().isEmpty()) {
                for (Group group : listGroupsForUserResponse.groups()) {
                    GroupEntity groupEntity1 = new GroupEntity();
                    groupEntity1.setGroupId(group.groupId());
                    groupEntity1.setGroupName(group.groupName());
                    groupEntity1.setCreateTime(group.createDate().toString());
                    groupList.add(groupEntity1);
                }
            }
            awsCloudResponse.setGroupList(groupList);
        } catch (AwsServiceException | SdkClientException e) {
            log.error("查询aws用户所属的分组列表异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询aws用户所属的分组列表异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 为aws用户绑定用户组
     *
     * @param groupEntity groupEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse addUserToGroup(GroupEntity groupEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            if (null != groupEntity.getGroupNameList() && ! groupEntity.getGroupNameList().isEmpty()) {
                for (String groupName : groupEntity.getGroupNameList()) {
                    AddUserToGroupRequest addUserToGroupRequest =
                            AddUserToGroupRequest.builder().groupName(groupName)
                                    .userName(groupEntity.getUserName()).build();
                    awsCloudHttpConfig.getAWSIamClient(groupEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                }
            }
            if (null != groupEntity.getGroupName()) {
                if (null != groupEntity.getUserNameList() && !groupEntity.getUserNameList().isEmpty()) {
                    for (String userName : groupEntity.getUserNameList()) {
                        AddUserToGroupRequest addUserToGroupRequest =
                                AddUserToGroupRequest.builder().groupName(groupEntity.getGroupName())
                                        .userName(userName).build();
                        awsCloudHttpConfig.getAWSIamClient(groupEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                    }
                }
            }
        } catch (AwsServiceException | SdkClientException e) {
            log.error("为aws用户绑定用户组异常", e);
            awsCloudResponse.getHeader().setErrorMsg("为aws用户绑定用户组异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 将aws用户从用户组移除
     *
     * @param groupEntity groupEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse removeUserFromGroup(GroupEntity groupEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            if (null != groupEntity.getGroupNameList() && null != groupEntity.getUserNameList()) {
                for (String groupName : groupEntity.getGroupNameList()) {
                    for (String userName : groupEntity.getUserNameList()) {
                        RemoveUserFromGroupRequest request =
                                RemoveUserFromGroupRequest.builder()
                                        .groupName(groupName).userName(userName).build();
                        awsCloudHttpConfig.getAWSIamClient(groupEntity.getAccountId()).removeUserFromGroup(request);
                    }
                }
            }
        } catch (Exception e) {
            log.error("将aws用户从用户组移除异常", e);
            awsCloudResponse.getHeader().setErrorMsg("将aws用户从用户组移除异常：" + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 新增aws用户组
     *
     * @param groupEntity groupEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse createGroup(GroupEntity groupEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            if (StringUtils.isNotEmpty(groupEntity.getGroupName())) {
                CreateGroupRequest createGroupRequest =
                        CreateGroupRequest.builder().groupName(groupEntity.getGroupName()).path("/")
                                .build();
                awsCloudHttpConfig.getAWSIamClient(groupEntity.getAccountId()).createGroup(createGroupRequest);
            }
        } catch (AwsServiceException | SdkClientException e) {
            log.error("新增aws用户组异常", e);
            awsCloudResponse.getHeader().setErrorMsg("新增aws用户组异常：" + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 刪除aws用户组
     *
     * @param groupEntity awsCloudResponse
     * @return AwsCloudResponse
     */
    public AwsCloudResponse deleteGroup(GroupEntity groupEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            if (StringUtils.isNotEmpty(groupEntity.getGroupName())) {
                DeleteGroupRequest deleteGroupRequest =
                        DeleteGroupRequest.builder().groupName(groupEntity.getGroupName()).build();
                awsCloudHttpConfig.getAWSIamClient(groupEntity.getAccountId()).deleteGroup(deleteGroupRequest);
            }
        } catch (AwsServiceException | SdkClientException e) {
            log.error("刪除aws用户组", e);
            awsCloudResponse.getHeader().setErrorMsg("刪除aws用户组：" + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 查询aws用户组包含用户列表
     *
     * @param userEntity userEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse queryListUsersForGroup(UserEntity userEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            ListUsersResponse listUsersResponse = awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).listUsers();
            List<UserEntity> userList = new ArrayList<>();
            for (User user : listUsersResponse.users()) {
                ListGroupsForUserRequest listGroupsForUserRequest =
                        ListGroupsForUserRequest.builder().userName(user.userName()).build();
                ListGroupsForUserResponse listGroupsForUserResponse =
                        awsCloudHttpConfig.getAWSIamClient(userEntity.getAccountId()).listGroupsForUser(listGroupsForUserRequest);
                for (Group group : listGroupsForUserResponse.groups()) {
                    if (group.groupName().equals(userEntity.getGroupName())) {
                        UserEntity userEntity1 = new UserEntity();
                        userEntity1.setUserId(user.userId());
                        userEntity1.setUserName(user.userName());
                        userList.add(userEntity1);
                    }
                }
            }
            awsCloudResponse.setUserList(userList);
        } catch (AwsServiceException | SdkClientException e) {
            log.error("查询aws用户组包含用户异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询aws用户组包含用户异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 查询aws用户组详情
     *
     * @param groupEntity groupEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse queryGroupInfo(GroupEntity groupEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            if (StringUtils.isNotEmpty(groupEntity.getGroupName())) {
                GetGroupRequest getGroupRequest =
                        GetGroupRequest.builder().groupName(groupEntity.getGroupName()).build();
                GetGroupResponse getGroupResponse =
                        awsCloudHttpConfig.getAWSIamClient(groupEntity.getAccountId()).getGroup(getGroupRequest);
                if (null != getGroupResponse.group()) {
                    GroupEntity group = new GroupEntity();
                    group.setGroupId(getGroupResponse.group().groupId());
                    group.setGroupName(getGroupResponse.group().groupName());
                    group.setCreateTime(getGroupResponse.group().createDate().toString());
                    awsCloudResponse.setGroupEntity(group);
                }
            }
        } catch (AwsServiceException | SdkClientException e) {
            log.error("查询aws用户组详情异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询aws用户组详情异常：" + e.getMessage());
        }
        return awsCloudResponse;
    }

    /**
     * 更新用户组信息
     *
     * @param groupEntity groupEntity
     * @return AwsCloudResponse
     */
    public AwsCloudResponse updateGroup(GroupEntity groupEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            UpdateGroupRequest updateGroupRequest =
                    UpdateGroupRequest.builder().newGroupName(groupEntity.getNewGroupName())
                            .groupName(groupEntity.getGroupName()).build();
            awsCloudHttpConfig.getAWSIamClient(groupEntity.getAccountId()).updateGroup(updateGroupRequest);
        } catch (AwsServiceException | SdkClientException e) {
            log.error("更新aws用户组异常" , e);
            awsCloudResponse.getHeader().setErrorMsg("更新aws用户组异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }
}
