package com.tt.cloud.sso.saml;

import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.opensaml.common.binding.BasicSAMLMessageContext;
import org.opensaml.saml2.core.AuthnRequest;
import org.opensaml.ws.transport.http.HttpServletRequestAdapter;

@Slf4j
public class AuthnRequestResolution {

    public AuthnRequest resolve(HttpServletRequest request) throws Exception {
        BasicSAMLMessageContext<AuthnRequest, ?, ?> messageContext = new BasicSAMLMessageContext<>();
        messageContext.setInboundMessageTransport(new HttpServletRequestAdapter(request));
        MyHTTPRedirectDeflateDecoder decoder = new MyHTTPRedirectDeflateDecoder();
        try {
            decoder.decode(messageContext);
        } catch (Exception e) {
            log.error("解析云商SAMLRequest失败", e);
            throw new Exception("解析云商SAMLRequest失败");
        }
        return messageContext.getInboundSAMLMessage();
    }
}

