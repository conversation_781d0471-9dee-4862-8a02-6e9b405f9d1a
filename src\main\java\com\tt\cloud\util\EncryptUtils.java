package com.tt.cloud.util;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/5/31 15:08
 */
@Slf4j
public class EncryptUtils {
    private static final String SECRET = "AES";
    private static final String CIPHER_ALGORITHM = "AES/ECB/PKCS7Padding";

    /**
     * AES解密ECB模式PKCS7Padding填充方式
     *
     * @param str 字符串
     * @return 解密字符串
     */
    public static String aes256ECBPkcs7PaddingDecrypt(String str) {
        byte[] doFinal = new byte[0];
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            byte[] keyBytes = "hard to guess me".getBytes(StandardCharsets.UTF_8);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(keyBytes, SECRET));
            doFinal = cipher.doFinal(Base64.getDecoder().decode(str));
        } catch (Exception e) {
            log.error("解密失败", e);
        }
        return new String(doFinal);
    }

    /**
     * 加密
     *
     * @param data data
     * @return String
     */
    public static String encryptPassword(String data) {
        String result = StringUtils.EMPTY;
        try {
            SecretKeySpec key = new SecretKeySpec("hard to guess me".getBytes(StandardCharsets.UTF_8), SECRET);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            result = Base64.getEncoder().encodeToString(encryptedBytes);
        }
        catch (Exception e) {
            log.error("加密失败", e);
        }
        return result;
    }

}
