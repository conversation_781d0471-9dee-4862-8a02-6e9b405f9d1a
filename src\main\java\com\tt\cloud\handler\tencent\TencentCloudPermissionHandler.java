package com.tt.cloud.handler.tencent;

import com.tencentcloudapi.cam.v20190116.models.AttachGroupPolicyRequest;
import com.tencentcloudapi.cam.v20190116.models.AttachPolicyInfo;
import com.tencentcloudapi.cam.v20190116.models.AttachUserPolicyRequest;
import com.tencentcloudapi.cam.v20190116.models.GetUserRequest;
import com.tencentcloudapi.cam.v20190116.models.GetUserResponse;
import com.tencentcloudapi.cam.v20190116.models.GroupInfo;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedGroupPoliciesRequest;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedGroupPoliciesResponse;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserPoliciesRequest;
import com.tencentcloudapi.cam.v20190116.models.ListAttachedUserPoliciesResponse;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsForUserRequest;
import com.tencentcloudapi.cam.v20190116.models.ListGroupsForUserResponse;
import com.tencentcloudapi.cam.v20190116.models.ListPoliciesRequest;
import com.tencentcloudapi.cam.v20190116.models.ListPoliciesResponse;
import com.tencentcloudapi.cam.v20190116.models.StrategyInfo;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.bean.TencentCloudResponse;
import com.tt.cloud.config.TencentCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.handler.AbstractCloudPermissionHandler;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 腾讯云子用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/18 9:31
 */
@Slf4j
@Component
public class TencentCloudPermissionHandler extends AbstractCloudPermissionHandler {

    @Resource
    private TencentCloudHttpConfig tencentCloudHttpConfig;

    public boolean supports(String cloudType) {
        return Constants.TENCENT_CLOUD.equals(cloudType);
    }

    /**
     * 查询用户权限列表
     *
     * @param permissionEntity permissionEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse queryUserPermissions(PermissionEntity permissionEntity) {
        TencentCloudResponse response = new TencentCloudResponse();
        try {
            List<PermissionEntity> policyInfoList = new ArrayList<>();
            log.info("腾讯云查询用户包含的所有分组");
            if (StringUtils.isEmpty(permissionEntity.getPermissionType())
                    || Constants.BELONG_TYPE_GROUP.equals(permissionEntity.getPermissionType())) {
                queryUserBelongGroupPermissionList(permissionEntity, policyInfoList);
            }
            log.info("腾讯云查询用户包含的权限");
            if (StringUtils.isEmpty(permissionEntity.getPermissionType())
                    || Constants.BELONG_TYPE_USER.equals(permissionEntity.getPermissionType())) {
                queryUserPermissionList(permissionEntity, policyInfoList);
            }
            response.setAttachPolicyInfos(policyInfoList);
        } catch (TencentCloudSDKException e) {
            log.info("腾讯云查询用户包含的权限异常", e);
            response.getHeader().setErrorMsg("腾讯云查询用户包含的权限异常" + e.getMessage());
        }
        return response;
    }

    private void queryUserBelongGroupPermissionList(PermissionEntity permissionEntity,
            List<PermissionEntity> policyInfoList)
            throws TencentCloudSDKException {
        ListGroupsForUserRequest listGroupsForUserRequest = new ListGroupsForUserRequest();
        listGroupsForUserRequest.setUid(Long.parseLong(permissionEntity.getUserId()));
        listGroupsForUserRequest.setPage(1L);
        listGroupsForUserRequest.setRp(200L);
        ListGroupsForUserResponse listGroupsForUserResponse =
                tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId())
                        .ListGroupsForUser(listGroupsForUserRequest);
        for (GroupInfo groupInfo : listGroupsForUserResponse.getGroupInfo()) {
            log.info("查询用户组关联的权限");
            ListAttachedGroupPoliciesRequest listAttachedGroupPoliciesRequest = new ListAttachedGroupPoliciesRequest();
            listAttachedGroupPoliciesRequest.setPage(1L);
            listAttachedGroupPoliciesRequest.setRp(200L);
            listAttachedGroupPoliciesRequest.setTargetGroupId(groupInfo.getGroupId());
            ListAttachedGroupPoliciesResponse listAttachedGroupPoliciesResponse =
                    tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId())
                            .ListAttachedGroupPolicies(listAttachedGroupPoliciesRequest);

            addPermissionInfoToList(policyInfoList, listAttachedGroupPoliciesResponse.getList(), groupInfo);

            if (listAttachedGroupPoliciesResponse.getTotalNum() > 200) {
                log.info("用户组绑定权限大于200,则继续遍历获取所有权限");
                for (int i = 2;
                        i <= Math.floor(Double.valueOf(listAttachedGroupPoliciesResponse.getTotalNum()) / 200) + (
                                listAttachedGroupPoliciesResponse.getTotalNum() % 200 > 0 ? 1 : 0);
                        i++) {
                    listAttachedGroupPoliciesRequest.setPage(Long.parseLong(String.valueOf(i)));
                    listAttachedGroupPoliciesRequest.setRp(200L);
                    listAttachedGroupPoliciesResponse =
                            tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId())
                                    .ListAttachedGroupPolicies(listAttachedGroupPoliciesRequest);
                    addPermissionInfoToList(policyInfoList, listAttachedGroupPoliciesResponse.getList(), groupInfo);
                }
            }
        }
    }

    private void addPermissionInfoToList(List<PermissionEntity> policyInfoList,
            AttachPolicyInfo[] attachPolicyInfos, GroupInfo groupInfo) {
        for (AttachPolicyInfo attachPolicyInfo : attachPolicyInfos) {
            PermissionEntity permission = new PermissionEntity();
            permission.setPolicyId(attachPolicyInfo.getPolicyId());
            permission.setPolicyName(attachPolicyInfo.getPolicyName());
            permission.setPolicyType(attachPolicyInfo.getPolicyType());
            permission.setCreateMode(attachPolicyInfo.getCreateMode());
            permission.setAddTime(attachPolicyInfo.getAddTime());
            permission.setRemark(attachPolicyInfo.getRemark());
            permission.setCreateMode(attachPolicyInfo.getCreateMode());
            if (null != groupInfo) {
                permission.setBelongName(groupInfo.getGroupName());
                permission.setBelongType(Constants.BELONG_TYPE_GROUP);
                permission.setBelongId(String.valueOf(groupInfo.getGroupId()));
            }
            policyInfoList.add(permission);
        }
    }

    private void queryUserPermissionList(PermissionEntity permissionEntity,
            List<PermissionEntity> policyInfoList)
            throws TencentCloudSDKException {
        GetUserRequest request = new GetUserRequest();
        request.setName(permissionEntity.getUserName());
        GetUserResponse userResponse =
                tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId()).GetUser(request);
        ListAttachedUserPoliciesRequest listAttachedUserPoliciesRequest = new ListAttachedUserPoliciesRequest();
        listAttachedUserPoliciesRequest.setPage(1L);
        listAttachedUserPoliciesRequest.setRp(200L);
        listAttachedUserPoliciesRequest.setTargetUin(Long.parseLong(permissionEntity.getUserIn()));
        ListAttachedUserPoliciesResponse listAttachedUserPoliciesResponse =
                tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId())
                        .ListAttachedUserPolicies(listAttachedUserPoliciesRequest);
        addPermissionInfoToList(policyInfoList, listAttachedUserPoliciesResponse.getList(), userResponse);
        if (listAttachedUserPoliciesResponse.getTotalNum() > 200) {
            for (int i = 2;
                    i <= Math.floor(Double.valueOf(listAttachedUserPoliciesResponse.getTotalNum()) / 200) + (
                            listAttachedUserPoliciesResponse.getTotalNum() % 200 > 0 ? 1 : 0);
                    i++) {
                listAttachedUserPoliciesRequest.setPage(Long.parseLong(String.valueOf(i)));
                listAttachedUserPoliciesRequest.setRp(200L);
                listAttachedUserPoliciesResponse =
                        tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId())
                                .ListAttachedUserPolicies(listAttachedUserPoliciesRequest);
                addPermissionInfoToList(policyInfoList, listAttachedUserPoliciesResponse.getList(), userResponse);
            }
        }
    }

    private void addPermissionInfoToList(List<PermissionEntity> policyInfoList,
            AttachPolicyInfo[] attachPolicyInfos, GetUserResponse getUserResponse) {
        for (AttachPolicyInfo attachPolicyInfo : attachPolicyInfos) {
            PermissionEntity permission = new PermissionEntity();
            permission.setPolicyId(attachPolicyInfo.getPolicyId());
            permission.setPolicyName(attachPolicyInfo.getPolicyName());
            permission.setPolicyType(attachPolicyInfo.getPolicyType());
            permission.setCreateMode(attachPolicyInfo.getCreateMode());
            permission.setCreateMode(attachPolicyInfo.getCreateMode());
            permission.setRemark(attachPolicyInfo.getRemark());
            permission.setAddTime(attachPolicyInfo.getAddTime());
            permission.setBelongId(String.valueOf(getUserResponse.getUin()));
            permission.setBelongName(getUserResponse.getName());
            permission.setBelongType(Constants.BELONG_TYPE_USER);
            policyInfoList.add(permission);
        }
    }

    /**
     * 查詢權限列表
     *
     * @return TencentCloudResponse
     */
    public TencentCloudResponse queryPermissionList(PermissionEntity permissionEntity) {
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            log.info("查询腾讯云全部要策略列表");
            ListPoliciesRequest listPoliciesRequest = new ListPoliciesRequest();
            listPoliciesRequest.setPage(1L);
            listPoliciesRequest.setRp(200L);
            ListPoliciesResponse listPoliciesResponse = tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId())
                    .ListPolicies(listPoliciesRequest);
            List<StrategyInfo> strategyInfoList = new ArrayList<>(
                    Arrays.asList(listPoliciesResponse.getList()));
            if (listPoliciesResponse.getTotalNum() > 200) {
                for (int i = 2; i <= Math.floor(Double.valueOf(listPoliciesResponse.getTotalNum()) / 200) + (
                        listPoliciesResponse.getTotalNum() % 200 > 0 ? 1 : 0); i++) {
                    listPoliciesRequest.setPage(Long.parseLong(String.valueOf(i)));
                    listPoliciesRequest.setRp(200L);
                    listPoliciesResponse =
                            tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId())
                                    .ListPolicies(listPoliciesRequest);
                    strategyInfoList.addAll(Arrays.asList(listPoliciesResponse.getList()));
                }
            }
            tencentCloudResponse.setStrategyInfoList(strategyInfoList);
        } catch (TencentCloudSDKException e) {
            log.info("查询腾讯云全部要策略列表异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("查询腾讯云全部要策略列表异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 向用户授权
     *
     * @param permissionEntity permissionEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse grantUserPermissions(PermissionEntity permissionEntity) {
        TencentCloudResponse tencentCloudResponse = new  TencentCloudResponse();
        try {
            log.info("向用户授权");
            AttachUserPolicyRequest attachUserPolicyRequest = new AttachUserPolicyRequest();
            attachUserPolicyRequest.setAttachUin(Long.parseLong(permissionEntity.getUserIn()));
            for (String permissionId : permissionEntity.getPermissionIdList()) {
                attachUserPolicyRequest.setPolicyId(Long.parseLong(permissionId));
                tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId()).AttachUserPolicy(attachUserPolicyRequest);
            }
            log.info("向用户授权完毕");
        } catch (TencentCloudSDKException e) {
            log.error("向用户授权异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("向用户授权异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 查询用户组权限列表
     *
     * @param permissionEntity permissionEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse queryGroupPermissionList(PermissionEntity permissionEntity) {
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            log.info("腾讯云查询用户组绑定权限列表");
            ListAttachedGroupPoliciesRequest listAttachedGroupPoliciesRequest = new ListAttachedGroupPoliciesRequest();
            listAttachedGroupPoliciesRequest.setTargetGroupId(Long.parseLong(permissionEntity.getGroupId()));
            listAttachedGroupPoliciesRequest.setPage(1L);
            listAttachedGroupPoliciesRequest.setRp(200L);
            ListAttachedGroupPoliciesResponse listAttachedGroupPoliciesResponse =
                    tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId())
                            .ListAttachedGroupPolicies(listAttachedGroupPoliciesRequest);
            List<PermissionEntity> policyInfoList = new ArrayList<>();
            addPermissionInfoToList(policyInfoList, listAttachedGroupPoliciesResponse.getList(), (GroupInfo) null);
            if (listAttachedGroupPoliciesResponse.getTotalNum() > 200) {
                for (int i = 2;
                        i <= Math.floor(Double.valueOf(listAttachedGroupPoliciesResponse.getTotalNum()) / 200) + (
                                listAttachedGroupPoliciesResponse.getTotalNum() % 200 > 0 ? 1 : 0);
                        i++) {
                    listAttachedGroupPoliciesRequest.setPage(Long.parseLong(String.valueOf(i)));
                    listAttachedGroupPoliciesRequest.setRp(200L);
                    listAttachedGroupPoliciesResponse =
                            tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId())
                                    .ListAttachedGroupPolicies(listAttachedGroupPoliciesRequest);
                    addPermissionInfoToList(policyInfoList, listAttachedGroupPoliciesResponse.getList(), (GroupInfo) null);
                }
            }
            tencentCloudResponse.setAttachPolicyInfos(policyInfoList);
            log.info("腾讯云查询用户组绑定权限列表完毕");
        } catch (TencentCloudSDKException e) {
            log.error("腾讯云查询用户组绑定权限列表异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("腾讯云查询用户组绑定权限列表异常" + e.getMessage());
        }
        return tencentCloudResponse;
    }

    /**
     * 向用户组授权
     *
     * @param permissionEntity permissionEntity
     * @return TencentCloudResponse
     */
    public TencentCloudResponse grantGroupPermissions(PermissionEntity permissionEntity) {
        TencentCloudResponse tencentCloudResponse = new TencentCloudResponse();
        try {
            log.info("腾讯云向用户组授权");
            for (Long policyId : permissionEntity.getPolicyIdList()) {
                AttachGroupPolicyRequest attachGroupPolicyRequest = new AttachGroupPolicyRequest();
                attachGroupPolicyRequest.setPolicyId(policyId);
                attachGroupPolicyRequest.setAttachGroupId(Long.parseLong(permissionEntity.getGroupId()));
                tencentCloudHttpConfig.getTencentCamClient(permissionEntity.getAccountId()).AttachGroupPolicy(attachGroupPolicyRequest);
            }
            log.info("腾讯云向用户组授权完毕");
        } catch (TencentCloudSDKException e) {
            log.error("腾讯云向用户组授权异常", e);
            tencentCloudResponse.getHeader().setErrorMsg("腾讯云向用户组授权异常：" + e.getMessage());
        }
        return tencentCloudResponse;
    }

}
