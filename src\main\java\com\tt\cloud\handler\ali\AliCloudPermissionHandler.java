package com.tt.cloud.handler.ali;

import com.aliyun.ram20150501.models.AttachPolicyToGroupRequest;
import com.aliyun.ram20150501.models.AttachPolicyToUserRequest;
import com.aliyun.ram20150501.models.ListGroupsForUserRequest;
import com.aliyun.ram20150501.models.ListGroupsForUserResponse;
import com.aliyun.ram20150501.models.ListGroupsForUserResponseBody.ListGroupsForUserResponseBodyGroupsGroup;
import com.aliyun.ram20150501.models.ListPoliciesForGroupRequest;
import com.aliyun.ram20150501.models.ListPoliciesForGroupResponse;
import com.aliyun.ram20150501.models.ListPoliciesForGroupResponseBody.ListPoliciesForGroupResponseBodyPoliciesPolicy;
import com.aliyun.ram20150501.models.ListPoliciesForUserRequest;
import com.aliyun.ram20150501.models.ListPoliciesForUserResponse;
import com.aliyun.ram20150501.models.ListPoliciesForUserResponseBody.ListPoliciesForUserResponseBodyPoliciesPolicy;
import com.aliyun.ram20150501.models.ListPoliciesRequest;
import com.aliyun.ram20150501.models.ListPoliciesResponse;
import com.aliyun.ram20150501.models.ListPoliciesResponseBody.ListPoliciesResponseBodyPoliciesPolicy;
import com.tt.cloud.bean.AliCloudResponse;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.handler.AbstractCloudPermissionHandler;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/29 16:47
 */
@Slf4j
@Component
public class AliCloudPermissionHandler extends AbstractCloudPermissionHandler {
    @Resource
    private AliCloudHttpConfig aliCloudHttpConfig;

    public boolean supports(String cloudType) {
        return Constants.ALI_CLOUD.equals(cloudType);
    }

    /**
     * 查询用户包含的權权限
     *
     * @param permissionEntity permissionEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryUserPermissions(PermissionEntity permissionEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            List<PermissionEntity> permissionList = new ArrayList<>();
            if (null == permissionEntity.getPermissionType()
                    || Constants.BELONG_TYPE_USER.equals(permissionEntity.getPermissionType())) {
                log.info("阿里云查询用户直接关联权限列表：{}", permissionEntity.getUserName());
                ListPoliciesForUserRequest listPoliciesForUserRequest = new ListPoliciesForUserRequest();
                listPoliciesForUserRequest.setUserName(permissionEntity.getUserName());
                ListPoliciesForUserResponse listPoliciesForUserResponse =
                        aliCloudHttpConfig.getAliIamClient(permissionEntity.getAccountId())
                                .listPoliciesForUser(listPoliciesForUserRequest);
                for (ListPoliciesForUserResponseBodyPoliciesPolicy
                        listPoliciesForUserResponseBodyPoliciesPolicy :
                        listPoliciesForUserResponse.getBody().getPolicies().getPolicy()) {
                    PermissionEntity permission = new PermissionEntity();
                    permission.setPolicyName(
                            listPoliciesForUserResponseBodyPoliciesPolicy.getPolicyName());
                    permission.setPolicyType(
                            listPoliciesForUserResponseBodyPoliciesPolicy.getPolicyType());
                    permission.setAddTime(
                            listPoliciesForUserResponseBodyPoliciesPolicy.getAttachDate());
                    permission.setRemark(
                            listPoliciesForUserResponseBodyPoliciesPolicy.getDescription());
                    permission.setBelongId(permissionEntity.getUserName());
                    permission.setBelongType(Constants.BELONG_TYPE_USER);
                    permissionList.add(permission);
                }
            }
            if (null == permissionEntity.getPermissionType()
                    || Constants.BELONG_TYPE_GROUP.equals(permissionEntity.getPermissionType())) {
                log.info("阿里云查询用户关联用户组所关联的权限列表：{}", permissionEntity.getUserName());
                ListGroupsForUserRequest listGroupsForUserRequest = new ListGroupsForUserRequest();
                listGroupsForUserRequest.setUserName(permissionEntity.getUserName());
                ListGroupsForUserResponse listGroupsForUserResponse
                        = aliCloudHttpConfig.getAliIamClient(permissionEntity.getAccountId())
                        .listGroupsForUser(listGroupsForUserRequest);
                for (ListGroupsForUserResponseBodyGroupsGroup listGroupsForUserResponseBodyGroupsGroup
                        : listGroupsForUserResponse.getBody().getGroups().getGroup()) {
                    ListPoliciesForGroupRequest listPoliciesForGroupRequest = new ListPoliciesForGroupRequest();
                    listPoliciesForGroupRequest.setGroupName(
                            listGroupsForUserResponseBodyGroupsGroup.getGroupName());
                    ListPoliciesForGroupResponse listPoliciesForGroupResponse =
                            aliCloudHttpConfig.getAliIamClient(permissionEntity.getAccountId())
                                    .listPoliciesForGroup(listPoliciesForGroupRequest);
                    for (ListPoliciesForGroupResponseBodyPoliciesPolicy
                            listPoliciesForUserResponseBodyPoliciesPolicy :
                            listPoliciesForGroupResponse.getBody().getPolicies().getPolicy()) {
                        PermissionEntity permission = new PermissionEntity();
                        permission.setPolicyName(
                                listPoliciesForUserResponseBodyPoliciesPolicy.getPolicyName());
                        permission.setPolicyType(
                                listPoliciesForUserResponseBodyPoliciesPolicy.getPolicyType());
                        permission.setAddTime(
                                listPoliciesForUserResponseBodyPoliciesPolicy.getAttachDate());
                        permission.setRemark(
                                listPoliciesForUserResponseBodyPoliciesPolicy.getDescription());
                        permission.setBelongId(
                                listGroupsForUserResponseBodyGroupsGroup.getGroupName());
                        permission.setBelongName(
                                listGroupsForUserResponseBodyGroupsGroup.getGroupName());
                        permission.setBelongType(Constants.BELONG_TYPE_GROUP);
                        permissionList.add(permission);
                    }
                }
            }
            aliCloudResponse.setPermissionList(permissionList);
        } catch (Exception e) {
            log.error("阿里云查询用户关联权限异常", e);
            aliCloudResponse.getHeader().setErrorMsg("阿里云查询用户关联权限异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }


    /**
     * 查询全部策略列表
     *
     * @return AliCloudResponse
     */
    public AliCloudResponse queryPermissionList(PermissionEntity permissionEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("查询阿里云全部要策略列表");
            ListPoliciesRequest listPoliciesRequest = new ListPoliciesRequest();
            listPoliciesRequest.setMaxItems(1000);
            ListPoliciesResponse listPoliciesResponse = aliCloudHttpConfig.getAliIamClient(
                            permissionEntity.getAccountId())
                    .listPolicies(listPoliciesRequest);
            aliCloudResponse.setStrategyInfoList(
                    listPoliciesResponse.getBody().getPolicies().getPolicy());
        } catch (Exception e) {
            log.info("查询阿里云全部要策略列表异常", e);
            aliCloudResponse.getHeader().setErrorMsg("查询阿里云全部要策略列表异常" + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 为阿里云用户组授权
     *
     * @param permissionEntity permissionEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse grantGroupPermissions(PermissionEntity permissionEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("为阿里云用户组授权");
            if (null != permissionEntity.getPermissionList()
                    && !permissionEntity.getPermissionList().isEmpty()) {
                for (PermissionEntity permission : permissionEntity.getPermissionList()) {
                    AttachPolicyToGroupRequest attachPolicyToGroupRequest = new AttachPolicyToGroupRequest();
                    attachPolicyToGroupRequest.setGroupName(permissionEntity.getGroupName());
                    attachPolicyToGroupRequest.setPolicyName(permission.getPolicyName());
                    attachPolicyToGroupRequest.setPolicyType(permission.getPolicyType());
                    aliCloudHttpConfig.getAliIamClient(permissionEntity.getAccountId())
                            .attachPolicyToGroup(attachPolicyToGroupRequest);
                }
            }
        } catch (Exception e) {
            log.info("为阿里云用户组授权异常", e);
            aliCloudResponse.getHeader().setErrorMsg("为阿里云用户组授权异常: " + e.getMessage());
        }
        return aliCloudResponse;
    }

    /**
     * 为阿里云用户授权
     *
     * @param permissionEntity permissionEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse grantUserPermissions(PermissionEntity permissionEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("为阿里云用户授权");
            if (null != permissionEntity.getPermissionList()
                    && !permissionEntity.getPermissionList().isEmpty()) {
                for (PermissionEntity permission : permissionEntity.getPermissionList()) {
                    AttachPolicyToUserRequest attachPolicyToUserRequest = new AttachPolicyToUserRequest();
                    attachPolicyToUserRequest.setUserName(permissionEntity.getUserName());
                    attachPolicyToUserRequest.setPolicyName(permission.getPolicyName());
                    attachPolicyToUserRequest.setPolicyType(permission.getPolicyType());
                    aliCloudHttpConfig.getAliIamClient(permissionEntity.getAccountId())
                            .attachPolicyToUser(attachPolicyToUserRequest);
                }
            }
        } catch (Exception e) {
            log.info("为阿里云用户授权异常", e);
            aliCloudResponse.getHeader().setErrorMsg("为阿里云用户授权异常: " + e.getMessage());
        }
        return aliCloudResponse;
    }

    public AliCloudResponse queryGroupPermissionList(PermissionEntity permissionEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("查询阿里云用户组权限列表:{}", permissionEntity.getGroupName());
            ListPoliciesForGroupRequest listPoliciesForGroupRequest = new ListPoliciesForGroupRequest();
            listPoliciesForGroupRequest.setGroupName(permissionEntity.getGroupName());
            ListPoliciesForGroupResponse listPoliciesForGroupResponse
                    = aliCloudHttpConfig.getAliIamClient(permissionEntity.getAccountId())
                    .listPoliciesForGroup(listPoliciesForGroupRequest);
            List<ListPoliciesResponseBodyPoliciesPolicy> strategyInfoList = new ArrayList<>();
            for (ListPoliciesForGroupResponseBodyPoliciesPolicy
                    listPoliciesForGroupResponseBodyPoliciesPolicy
                    : listPoliciesForGroupResponse.getBody()
                    .getPolicies().getPolicy()) {
                ListPoliciesResponseBodyPoliciesPolicy policy = new ListPoliciesResponseBodyPoliciesPolicy();
                policy.setPolicyName(listPoliciesForGroupResponseBodyPoliciesPolicy.policyName);
                policy.setPolicyType(listPoliciesForGroupResponseBodyPoliciesPolicy.policyType);
                policy.setDescription(
                        listPoliciesForGroupResponseBodyPoliciesPolicy.getDescription());
                policy.setCreateDate(
                        listPoliciesForGroupResponseBodyPoliciesPolicy.getAttachDate());
                strategyInfoList.add(policy);
            }
            aliCloudResponse.setStrategyInfoList(strategyInfoList);
        } catch (Exception e) {
            log.info("查询阿里云用户组权限列表异常", e);
            aliCloudResponse.getHeader().setErrorMsg("查询阿里云用户组权限列表异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

}
