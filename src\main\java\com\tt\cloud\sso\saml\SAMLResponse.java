package com.tt.cloud.sso.saml;


import com.tt.cloud.bean.v2.Result;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.sso.metadata.IDPMetadata;
import com.tt.cloud.sso.metadata.MetadataBean;
import java.util.HashMap;
import java.util.Map;
import javax.xml.namespace.QName;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.opensaml.Configuration;
import org.opensaml.common.impl.RandomIdentifierGenerator;
import org.opensaml.saml2.core.Assertion;
import org.opensaml.saml2.core.Attribute;
import org.opensaml.saml2.core.AttributeStatement;
import org.opensaml.saml2.core.AttributeValue;
import org.opensaml.saml2.core.Audience;
import org.opensaml.saml2.core.AudienceRestriction;
import org.opensaml.saml2.core.AuthenticatingAuthority;
import org.opensaml.saml2.core.AuthnContext;
import org.opensaml.saml2.core.AuthnContextClassRef;
import org.opensaml.saml2.core.AuthnRequest;
import org.opensaml.saml2.core.AuthnStatement;
import org.opensaml.saml2.core.Conditions;
import org.opensaml.saml2.core.Issuer;
import org.opensaml.saml2.core.NameID;
import org.opensaml.saml2.core.NameIDType;
import org.opensaml.saml2.core.Response;
import org.opensaml.saml2.core.Status;
import org.opensaml.saml2.core.StatusCode;
import org.opensaml.saml2.core.StatusMessage;
import org.opensaml.saml2.core.Subject;
import org.opensaml.saml2.core.SubjectConfirmation;
import org.opensaml.saml2.core.SubjectConfirmationData;
import org.opensaml.xml.XMLObjectBuilderFactory;
import org.opensaml.xml.schema.XSString;
import org.opensaml.xml.schema.impl.XSStringBuilder;


public class SAMLResponse {
    //SAMLResponse 过期时间
    private static final int DEFAULT_ASSERTION_VALID_TIME_MIN = 5;
    private static final MetadataBean idpMetadata = IDPMetadata.getInstance();

    public static MetadataBean getIdpMetadata() {
        return idpMetadata;
    }

    public static Assertion buildAssertion(AuthnRequest authnRequest, String cloud, Result result) {
        String entityId = idpMetadata.getEntityId();
        Assertion assertion = buildSAMLObject(Assertion.class);
        DateTime notBefore = DateTime.now(DateTimeZone.UTC);
        DateTime notOnOrAfter = notBefore.plusMinutes(DEFAULT_ASSERTION_VALID_TIME_MIN);

        Subject subject = buildSubject(entityId, authnRequest.getAssertionConsumerServiceURL(), authnRequest.getID(), notOnOrAfter, cloud, result);
        Issuer issuer = buildIssuer(entityId);
        Audience audience = buildSAMLObject(Audience.class);
        audience.setAudienceURI(result.getSpMetadata().getEntityId());
        AudienceRestriction audienceRestriction = buildSAMLObject(AudienceRestriction.class);
        audienceRestriction.getAudiences().add(audience);

        if (Constants.KS_CLOUD.equals(cloud)) {
              Audience audience1 = buildSAMLObject(Audience.class);
              audience1.setAudienceURI(String.format("https://signin.ksyun.com/%s/saml/SSO", result.getCloudAccountEntity().getDomainId()));
              audienceRestriction.getAudiences().add(audience1);
        }

        Conditions conditions = buildSAMLObject(Conditions.class);
        conditions.getAudienceRestrictions().add(audienceRestriction);
        conditions.setNotBefore(notBefore);
        conditions.setNotOnOrAfter(notOnOrAfter);
        AuthnStatement authnStatement = buildAuthnStatement(new DateTime(), entityId);
        assertion.setID(new RandomIdentifierGenerator().generateIdentifier());
        assertion.setIssueInstant(new DateTime());
        assertion.setSubject(subject);
        assertion.setIssuer(issuer);
        assertion.setConditions(conditions);
        assertion.getAuthnStatements().add(authnStatement);
        if (Constants.HUAWEI_CLOUD.equals(cloud)) {
            Map<String, String> attribute = new HashMap<>();
            attribute.put("IAM_SAML_Attributes_xUserId", UserContext.getCurrentUserId());
            attribute.put("IAM_SAML_Attributes_redirect_url", "https://console.huaweicloud.com/console/?iscros=true&region=cn-north-4&locale=zh-cn#/home");
            attribute.put("IAM_SAML_Attributes_domain_id", result.getCloudAccountEntity().getDomainId());
            attribute.put("IAM_SAML_Attributes_idp_id", "qw-sso");
            assertion.getAttributeStatements().add(buildAttributeStatement(attribute));
        }
        return assertion;
    }

    public static Response buildResponse(AuthnRequest authnRequest, Assertion assertion) {
        Response response = buildSAMLObject(Response.class);
        Issuer issuer = buildIssuer(idpMetadata.getEntityId());
        Status status = buildStatus();
        response.setID(new RandomIdentifierGenerator().generateIdentifier());
        response.setIssuer(issuer);
        response.setStatus(status);
        response.setIssueInstant(new DateTime());

        response.setInResponseTo(authnRequest.getID());
        response.setConsent(Response.UNSPECIFIED_CONSENT);
        response.setDestination(authnRequest.getAssertionConsumerServiceURL());
        response.getAssertions().add(assertion);
        return response;
    }

    private static Subject buildSubject(String subjectNameId, String recipient,
            String inResponseTo, DateTime notOnOrAfter, String cloud, Result result) {
        Subject subject = buildSAMLObject(Subject.class);
        NameID nameID = buildSAMLObject(NameID.class);
        nameID.setValue(subjectNameId);
        if (cloud.equals(Constants.ALI_CLOUD)) {
            nameID.setValue(result.getAliLoginName());
        }
        if (cloud.equals(Constants.TENCENT_CLOUD)) {
            nameID.setValue(result.getTencentUserName());
        }
        if (cloud.equals(Constants.VOLC_ENGINE_CLOUD)) {
            nameID.setValue(result.getVolcEngineUserName());
        }
        if (cloud.equals(Constants.KS_CLOUD)) {
            nameID.setValue(result.getKsCloudUserName());
        }
        nameID.setFormat(NameID.TRANSIENT);
        SubjectConfirmationData subjectConfirmationData = buildSAMLObject(SubjectConfirmationData.class);
        subjectConfirmationData.setRecipient(recipient);
        subjectConfirmationData.setNotOnOrAfter(notOnOrAfter);
        subjectConfirmationData.setInResponseTo(inResponseTo);
        SubjectConfirmation subjectConfirmation = buildSAMLObject(SubjectConfirmation.class);
        subjectConfirmation.setMethod(SubjectConfirmation.METHOD_BEARER);
        subjectConfirmation.setSubjectConfirmationData(subjectConfirmationData);
        subject.setNameID(nameID);
        subject.getSubjectConfirmations().add(subjectConfirmation);
        return subject;
    }

    private static AttributeStatement buildAttributeStatement(Map<String, String> attributes) {
        AttributeStatement attributeStatement = buildSAMLObject(AttributeStatement.class);
        for (Map.Entry<String, String> entry : attributes.entrySet()) {
            attributeStatement.getAttributes().add(buildAttribute(entry.getKey(), entry.getValue()));
        }
        return attributeStatement;
    }

    private static Issuer buildIssuer(String entityId) {
        Issuer issuer = buildSAMLObject(Issuer.class);
        issuer.setValue(entityId);
        issuer.setFormat(NameIDType.ENTITY);
        return issuer;
    }

    private static AuthnStatement buildAuthnStatement(DateTime authnInstant, String entityId) {
        AuthnContextClassRef authnContextClassRef = buildSAMLObject(AuthnContextClassRef.class);
        authnContextClassRef.setAuthnContextClassRef(AuthnContext.PASSWORD_AUTHN_CTX);
        AuthenticatingAuthority authenticatingAuthority = buildSAMLObject(AuthenticatingAuthority.class);
        authenticatingAuthority.setURI(entityId);
        AuthnContext authnContext = buildSAMLObject(AuthnContext.class);
        authnContext.setAuthnContextClassRef(authnContextClassRef);
        authnContext.getAuthenticatingAuthorities().add(authenticatingAuthority);
        AuthnStatement authnStatement = buildSAMLObject(AuthnStatement.class);
        authnStatement.setAuthnContext(authnContext);
        authnStatement.setAuthnInstant(authnInstant);
        return authnStatement;
    }

    private static Attribute buildAttribute(String name, String value) {
        XSStringBuilder stringBuilder = (XSStringBuilder) Configuration.getBuilderFactory().getBuilder(XSString.TYPE_NAME);
        Attribute attribute = buildSAMLObject(Attribute.class);
        attribute.setName(name);
        attribute.setNameFormat("urn:oasis:names:tc:SAML:2.0:attrname-format:uri");
        XSString stringValue = stringBuilder.buildObject(AttributeValue.DEFAULT_ELEMENT_NAME, XSString.TYPE_NAME);
        stringValue.setValue(value);
        attribute.getAttributeValues().add(stringValue);
        return attribute;
    }

    private static Status buildStatus() {
        Status status = buildSAMLObject(Status.class);
        StatusCode statusCode = buildSAMLObject(StatusCode.class);

        statusCode.setValue(StatusCode.SUCCESS_URI);
        status.setStatusCode(statusCode);

        StatusMessage statusMessage = buildSAMLObject(StatusMessage.class);
        statusMessage.setMessage(StatusCode.SUCCESS_URI);
        status.setStatusMessage(statusMessage);
        return status;
    }

    public static <T> T buildSAMLObject(final Class<T> clazz) {
        T object;
        try {
            XMLObjectBuilderFactory builderFactory = Configuration.getBuilderFactory();
            QName defaultElementName = (QName) clazz.getDeclaredField("DEFAULT_ELEMENT_NAME").get(null);
            object = (T) builderFactory.getBuilder(defaultElementName).buildObject(defaultElementName);
        } catch (IllegalAccessException | NoSuchFieldException e) {
            throw new IllegalArgumentException("Could not create SAML object");
        }
        return object;
    }
}
