package com.tt.cloud.bean.v2;

import com.tt.cloud.bean.ResponseHeader;
import java.util.ArrayList;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/1/16 10:56
 */
@Getter
@Setter
@ToString
public class CommonListResponse {
    private ResponseHeader header = new ResponseHeader();
    private Object list = new ArrayList<>();
    private long total;
}
