package com.tt.cloud.util;

import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;

public class CacheUtil {
    //默认大小
    private static final int DEFAULT_CAPACITY = 1024;
    // 最大缓存大小
    private static final int MAX_CAPACITY = 10000;
    //1000毫秒
    private static final long SECOND_TIME = 1000;
    //存储缓存的Map
    private static final ConcurrentHashMap<String, Object> map;
    private static final Timer timer;

    static {
        map = new ConcurrentHashMap<>(DEFAULT_CAPACITY);
        timer = new Timer();
    }

    //私有化构造方法
    private CacheUtil() {

    }

    static class ClearTask extends TimerTask {
        private final String key;

        public ClearTask(String key) {
            this.key = key;
        }

        @Override
        public void run() {
            CacheUtil.remove(key);
        }

    }

    public static void put(String key, Object object, int time_out) {
        if (checkCapacity()) {
            map.put(key, object);
            //默认缓存时间
            timer.schedule(new ClearTask(key), time_out * SECOND_TIME);
        }
    }

    public static boolean checkCapacity() {
        return map.size() < MAX_CAPACITY;
    }

    public static void remove(String key) {
        map.remove(key);
    }

    public static Object get(String key) {
        return map.get(key);
    }

}