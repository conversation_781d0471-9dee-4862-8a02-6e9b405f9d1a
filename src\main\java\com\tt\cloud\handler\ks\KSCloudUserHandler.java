package com.tt.cloud.handler.ks;

import com.tt.cloud.bean.*;
import com.tt.cloud.config.KSCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import ksyun.client.iam.getuser.v20151101.*;
import ksyun.client.iam.listattacheduserpolicies.v20151101.*;
import ksyun.client.iam.listgroups.v20151101.*;
import ksyun.client.iam.listgroupsforuser.v20151101.*;
import ksyun.client.iam.listusers.v20151101.*;
import ksyun.client.iam.addusertogroup.v20151101.*;
import ksyun.client.iam.createuser.v20151101.*;
import ksyun.client.iam.updateloginprofile.v20151101.*;
import ksyun.client.iam.listaccesskeys.v20151101.*;
import lombok.var;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import java.util.stream.Collectors;

@Slf4j
@Component
public class KSCloudUserHandler {
    @Resource
    private KSCloudHttpConfig ksCloudHttpConfig;

    @Resource
    private CloudAccountDao cloudAccountDao;

    @Resource
    private FlyBookService flyBookService;
    @Resource
    private ApplicationContext applicationContext;

    public VolacEngineCloudResponse queryUserExists(UserEntity userEntity) {
        VolacEngineCloudResponse volacEngineCloudResponse = new VolacEngineCloudResponse();
        try {
            UserAuthEntity userAuthEntity = getAuthEntity(userEntity);
            if (null != userAuthEntity) {
                UserEntity user = new UserEntity();
                user.setUserId(userAuthEntity.getUid());
                user.setUserName(userAuthEntity.getUname());
                volacEngineCloudResponse.setUserEntity(user);
            } else {
                volacEngineCloudResponse.setUserEntity(queryUser(userEntity));
            }
            if (null != volacEngineCloudResponse.getUserEntity()) {
                CloudAccountEntity cloudAccount
                        = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
                String loginUrl = "https://passport.ksyun.com/iam-login.html?account_id=" + cloudAccount.getName()+
                        "&email=" + volacEngineCloudResponse.getUserEntity().getUserName() +"@52tt.com";
                volacEngineCloudResponse.setLoginUrl(loginUrl);
            }
        } catch (Exception e) {
            log.error("⛰️云查询用户是否存在异常", e);
            volacEngineCloudResponse.getHeader().setErrorMsg("⛰️云查询用户是否存在异常：" + e.getMessage());
        }
        return volacEngineCloudResponse;
    }

    public UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        try {
            GetUserRequest request = new GetUserRequest();
            request.setUserName(userEntity.getUserName());
            GetUserClient client = new GetUserClient(ksCloudHttpConfig.GetCredential(userEntity.getAccountId(), StringUtils.EMPTY));

            GetUserResponse getUserResponse = client.doPost("iam.api.ksyun.com", request);      
            if (StringUtils.isNotEmpty(getUserResponse.getResult())  ) {
                userInfo = new UserEntity();
                // 创建ObjectMapper实例
            ObjectMapper mapper = new ObjectMapper();
            // 将JSON字符串解析为JsonNode树结构
            JsonNode rootNode = mapper.readTree(getUserResponse.getResult());

            // 嵌套对象访问
            JsonNode userNode = rootNode.get("GetUserResult").get("User");
            userInfo.setUserId(userNode.get("UserId").asText());
            userInfo.setUserName(userNode.get("UserName").asText());

            var  ret =  queryListGroupsForUser(new GroupEntity(){
                {
                    setAccountId(userEntity.getAccountId());
                    setUserName(userEntity.getUserName());
                }
            });

            for (var group : ret.getGroupList()) {
                if (null == userInfo.getGroupNameList()) {
                    userInfo.setGroupNameList(new ArrayList<>());
                }
                userInfo.getGroupNameList().add(group.getGroupName());

            }
        }

        } catch (Exception e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("Not Found")) {
                if (!userEntity.getUserName().contains("@52tt.com")) {
                    userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                    return queryUser(userEntity);
                }
            }
        }
        return userInfo;
    }

    private UserAuthEntity getAuthEntity(UserEntity userEntity) {
        UserAuthEntity userAuthInfo = new UserAuthEntity();
        userAuthInfo.setAccountId(userEntity.getAccountId());
        userAuthInfo.setUname(userEntity.getUserName());
        return cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
    }

    public List<UserGroupInfo> queryAllGroupList() throws Exception {
        List<UserGroupInfo> groupList = new ArrayList<>();
        List<CloudAccountEntity> accountList = cloudAccountDao.queryCloudAccountList(new CloudAccountRequest());
        Set<String> set = new HashSet<>();
        for (CloudAccountEntity cloudAccount : accountList) {
            if (Constants.KS_CLOUD.equals(cloudAccount.getCloudType())) {
                ListGroupsRequest listGroupsRequest = new ListGroupsRequest();
                listGroupsRequest.setMaxItems("1000");
                ListGroupsClient client = new ListGroupsClient(ksCloudHttpConfig.GetCredential(cloudAccount.getId(), StringUtils.EMPTY));
                ListGroupsResponse listGroupsResponse = client.doPost("iam.api.ksyun.com", listGroupsRequest);
                if (StringUtils.isNotEmpty(listGroupsResponse.getResult())) {
                    // 创建ObjectMapper实例
                    ObjectMapper mapper = new ObjectMapper();
                    // 将JSON字符串解析为JsonNode树结构
                    JsonNode rootNode = mapper.readTree(listGroupsResponse.getResult());
                    // 嵌套对象访问
                    JsonNode groupsNode = rootNode.get("ListGroupsResult").get("Groups").get("member");
                    if (groupsNode.isArray()) {
                        for (JsonNode groupNode : groupsNode) {
                            var groupName = groupNode.get("GroupName").asText();
                            if (set.contains(groupName) || StringUtils.isEmpty(groupName)) {
                                continue; // 跳过重复或空的组名
                            }
                            UserGroupInfo userGroupInfo = new UserGroupInfo();
                            userGroupInfo.setGroupId(groupName);
                            userGroupInfo.setGroupName(groupName);
                            groupList.add(userGroupInfo);
                            set.add(groupName);
                        }
                    }
                }
            }
        }
 
        return groupList;
    }

     /**
     * 查询用户组列表
     *
     * @param groupEntity groupEntity
     * @return VolacEngineCloudResponse
     */
    public VolacEngineCloudResponse queryGroupList(GroupEntity groupEntity) {
        VolacEngineCloudResponse aliCloudResponse = new VolacEngineCloudResponse();
        try {
            log.info("⛰️云查询用户组列表");
            ListGroupsRequest listGroupsRequest = new ListGroupsRequest();
            listGroupsRequest.setMaxItems("1000");
            ListGroupsClient client = new ListGroupsClient(ksCloudHttpConfig.GetCredential(groupEntity.getAccountId(), StringUtils.EMPTY));
            ListGroupsResponse listGroupsResponse = client.doPost("iam.api.ksyun.com", listGroupsRequest);
            List<GroupEntity> groupList = getGroupList(listGroupsResponse);
            if (StringUtils.isNotEmpty(groupEntity.getGroupName())) {
                groupList = groupList.stream().filter(item -> item.getGroupName().contains(groupEntity.getGroupName())).collect(
                        Collectors.toList());
            }
            aliCloudResponse.setGroupList(groupList);
        } catch (Exception e) {
            log.error("⛰️云查询用户组列表异常", e);
            aliCloudResponse.getHeader().setErrorMsg("⛰️云查询用户组列表异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }



    private List<GroupEntity> getGroupList(ListGroupsResponse listGroupsResponse) {
        List<GroupEntity> groupList = new ArrayList<>();
        try {
            if (StringUtils.isNotEmpty(listGroupsResponse.getResult())) {
                    // 创建ObjectMapper实例
                    ObjectMapper mapper = new ObjectMapper();
                    // 将JSON字符串解析为JsonNode树结构
                    JsonNode rootNode = mapper.readTree(listGroupsResponse.getResult());
                    // 嵌套对象访问
                    JsonNode groupsNode = rootNode.get("ListGroupsResult").get("Groups").get("member");
            if (groupsNode.isArray()) {
            for (JsonNode groupNode : groupsNode) {

            GroupEntity group = new GroupEntity();
            group.setGroupId(groupNode.get("GroupName").asText());
            group.setGroupName(groupNode.get("GroupName").asText());
            group.setCreateTime(groupNode.get("CreateDate").asText());
            group.setMemo(groupNode.get("Description").asText());
            groupList.add(group);
                        }
                    }
                }
        } catch (Exception e) {
            log.error("⛰️云查询用户组列表异常", e);
        }
    
        return groupList;
    }


         /**
     * 查詢用戶归属的用户组列表
     *
     * @param groupEntity groupEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryListGroupsForUser(GroupEntity groupEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("⛰️云用户所属用户组列表:{}", groupEntity.getUserName());
            ListGroupsForUserRequest listGroupsForUserRequest = new ListGroupsForUserRequest();
            listGroupsForUserRequest.setUserName(groupEntity.getUserName());
            // listGroupsForUserRequest.setUserName("wuqianhua");;
            var client = new ListGroupsForUserClient(ksCloudHttpConfig.GetCredential(groupEntity.getAccountId(), StringUtils.EMPTY));
            ListGroupsForUserResponse listGroupsForUserResponse = client.doPost("iam.api.ksyun.com", listGroupsForUserRequest);
            List<GroupEntity> groupList = new ArrayList<>();
            if (StringUtils.isEmpty(listGroupsForUserResponse.getResult())) {
                return aliCloudResponse;
            }
          
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode rootNode = mapper.readTree(listGroupsForUserResponse.getResult());
                    JsonNode groupsNode = rootNode.get("ListGroupsForUserResult").get("Groups").get("member");
            if (groupsNode.isArray()) {
                for (JsonNode groupNode : groupsNode) {
                    GroupEntity group = new GroupEntity();
                    group.setGroupId(groupNode.get("GroupName").asText());
                    group.setGroupName(groupNode.get("GroupName").asText());
                    group.setCreateTime(groupNode.get("CreateDate").asText());
                    group.setMemo(groupNode.get("Description").asText());
                    groupList.add(group);
                }
            }

            aliCloudResponse.setGroupList(groupList);
        } catch (Exception e) {
            log.error("⛰️云用户所属用户组列表异常", e);
            aliCloudResponse.getHeader().setErrorMsg("⛰️云用户所属用户组列表异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }


     /**
     * 创建用户
     *
     * @param userEntity userEntity
     * @return VolacEngineCloudResponse
     */
    public VolacEngineCloudResponse createUser(UserEntity userEntity) {
        VolacEngineCloudResponse aliCloudResponse = new VolacEngineCloudResponse();
        try {
            log.info("⛰️云新增用户：{}", userEntity);
            CreateUserRequest request = new CreateUserRequest();
            request.setUserName(userEntity.getUserName());
            request.setRealName(userEntity.getUserName());
            request.setEmail(userEntity.getEmail());
            request.setPhone(userEntity.getPhone());
            request.setRemark(userEntity.getRemark());

            var client = new CreateUserClient(ksCloudHttpConfig.GetCredential(userEntity.getAccountId(), StringUtils.EMPTY));
            var u2gclient = new AddUserToGroupClient(ksCloudHttpConfig.GetCredential(userEntity.getAccountId(), StringUtils.EMPTY));

            client.doPost("iam.api.ksyun.com", request);
            log.info("⛰️云新增用户完毕");
            if (null != userEntity.getGroupNameList() && !userEntity.getGroupNameList().isEmpty()) {
                log.info("将用户添加到用户组中：{}", userEntity.getGroupNameList());
                for (String groupName : userEntity.getGroupNameList()) {
                    AddUserToGroupRequest addUserToGroupRequest = new AddUserToGroupRequest();
                    addUserToGroupRequest.setUserName(userEntity.getUserName());
                    addUserToGroupRequest.setGroupName(groupName);
                    u2gclient.doPost("iam.api.ksyun.com", addUserToGroupRequest);
                }
            }
            if (!userEntity.isApiUser()) {
                var client3 = new UpdateLoginProfileClient(ksCloudHttpConfig.GetCredential(userEntity.getAccountId(), StringUtils.EMPTY));
                UpdateLoginProfileRequest createLoginProfileRequest = new UpdateLoginProfileRequest();
                createLoginProfileRequest.setUserName(userEntity.getUserName());
                createLoginProfileRequest.setPassword(Utils.getPassword());
                // createLoginProfileRequest.setMFABindRequired(false);
                createLoginProfileRequest.setViewAllProject(true);
                createLoginProfileRequest.setPasswordResetRequired(true);
                client3.doPost("iam.api.ksyun.com", createLoginProfileRequest);
                userEntity.setPassword(createLoginProfileRequest.getPassword());
                sendMessage(userEntity);
            }
            aliCloudResponse.setUserEntity(userEntity);
        } catch (Exception e) {
            log.error("⛰️云新增用户异常", e);
            aliCloudResponse.getHeader().setErrorMsg("⛰️云新增用户异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }


    private void sendMessage(UserEntity userEntity) throws Exception {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
        Map<String, Object> messageParam = new HashMap<>();
        messageParam.put("cloud", cloudAccount.getMemo() + "/" + cloudAccount.getName() + "/" + cloudAccount.getDomainId());
        messageParam.put("username", userEntity.getUserName());
        messageParam.put("password", userEntity.getPassword());
        messageParam.put("url", "*由于开启云商单点登录，关闭密码登录方式*\\n*请通过[运维平台](https://yw-sso.ttyuyin.com/index/dashboard)->"
                + "[云泽平台](https://yw-cloud.ttyuyin.com/tt-cloud/cloud/center)进行登录*");
        String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
        content = Utils.getParseValue(applicationContext, messageParam, content);
        flyBookService.sendMessage(userEntity.getEmail(), content);
    }


        /**
     * 用户加入用户组
     *
     * @param groupEntity groupEntity
     * @return VolacEngineCloudResponse
     */
    public VolacEngineCloudResponse addUserToGroup(GroupEntity groupEntity) {
        VolacEngineCloudResponse aliCloudResponse = new VolacEngineCloudResponse();
        try {
            log.info("⛰️云用户加入用户组:{}", groupEntity);
            AddUserToGroupRequest request = new AddUserToGroupRequest();
            var client = new AddUserToGroupClient(ksCloudHttpConfig.GetCredential(groupEntity.getAccountId(), StringUtils.EMPTY));
            if (null != groupEntity.getGroupNameList() && !groupEntity.getGroupNameList().isEmpty()) {
                for (String groupName : groupEntity.getGroupNameList()) {
                    request.setGroupName(groupName);
                    request.setUserName(groupEntity.getUserName());
                    AddUserToGroupResponse addUserToGroupResponse = client.doPost("iam.api.ksyun.com", request);
                }
            }
            if (null != groupEntity.getGroupName()) {
                if (null != groupEntity.getUserNameList() && !groupEntity.getUserNameList().isEmpty()) {
                    for (String userName : groupEntity.getUserNameList()) {
                        request.setGroupName(groupEntity.getGroupName());
                        request.setUserName(userName);
                         AddUserToGroupResponse addUserToGroupResponse = client.doPost("iam.api.ksyun.com", request);
                    }
                }
            }
        } catch (Exception e) {
            log.error("⛰️云用户加入用户组异常", e);
            aliCloudResponse.getHeader().setErrorMsg("⛰️云用户加入用户组异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    public CloudAllUserResponse queryUserListByAccount(CloudAccountEntity cloudAccount, String nextMarker) {
        CloudAllUserResponse userListRst = new CloudAllUserResponse();
        try {
            log.info("⛰️云子用户详情列表，nextMarker: {}", nextMarker);

            List<UserEntity> userList = new ArrayList<>();
            // ⛰️云支持分页获取用户列表，不需要缓存到redis，每次传入nextMarker分别获取
            var client = new ListUsersClient(ksCloudHttpConfig.GetCredential(cloudAccount.getId(), StringUtils.EMPTY));

            // 使用传入的nextMarker进行分页查询
            var response = getUserListResponse(client, nextMarker);
            if (StringUtils.isEmpty(response.getResult())) {
                log.info("⛰️云用户列表查询结果为空");
                userListRst.setUserList(userList);
                return userListRst;
            }

            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(response.getResult());
            var items = rootNode.get("ListUserResult").get("Users").get("member");

            if (items.isArray()) {
                for (JsonNode item : items) {
                    UserEntity user = new UserEntity();
                    user.setUserId(item.get("UserId").asText());
                    user.setUserName(item.get("UserName").asText());
                    user.setCreateTime(item.get("CreateDate").asText());
                    user.setUpdateTime(item.get("UpdateDate").asText());
                    user.setRemark(item.get("Remark").asText());
                    user.setAccountId(cloudAccount.getDomainId());
                    userList.add(user);
                }
            }

            // 对当前页的用户列表进行详细信息补充
            attachUserInfos(userList, cloudAccount.getId());
            // 设置返回结果
            userListRst.setUserList(userList);
            // 设置下一页的nextMarker
            if (rootNode.get("ListUserResult").get("IsTruncated").asBoolean()) {
                userListRst.setNextMarker(rootNode.get("ListUserResult").get("Marker").asText());
            }
            
        } catch (Exception e) {
            log.error("⛰️云子用户详情列表异常", e);
            throw new RuntimeException("⛰️云子用户详情列表异常：" + e.getMessage());
        }

        return userListRst;
    }

    private ListUsersResponse getUserListResponse(ListUsersClient client, String marker) {
        ListUsersRequest request = new ListUsersRequest();
        // 设置分页参数，每页20条记录
        request.setMaxItems(20);
        if (StringUtils.isNotEmpty(marker)) {
            request.setMarker(marker);
        }
        try {
            return client.doPost("iam.api.ksyun.com", request);
        } catch (Exception e) {
            log.error("⛰️云获取用户列表异常: {}", e.getMessage(), e);
            throw new RuntimeException("⛰️云获取用户列表异常：" + e.getMessage());
        }
    }


    private void attachUserInfos(List<UserEntity> userList, String accountId) {
        if (userList == null || userList.isEmpty()) {
            return;
        }
        try {
            var client = new ListGroupsForUserClient(ksCloudHttpConfig.GetCredential(accountId, StringUtils.EMPTY));
            var iamApi = new ListAttachedUserPoliciesClient(ksCloudHttpConfig.GetCredential(accountId, StringUtils.EMPTY));
            var accessKeyClient = new ListAccessKeysClient(ksCloudHttpConfig.GetCredential(accountId, StringUtils.EMPTY));

            // 获取用户信息
            for (int i = 0; i < userList.size(); i++) {
                UserEntity user = userList.get(i);

                // 用户最后登录信息

                // 用户组列表
                ListGroupsForUserRequest request = new ListGroupsForUserRequest();
                request.setUserName(user.getUserName());
                request.setMaxItems("999");
                var result = client.doPost("iam.api.ksyun.com", request);
                if (StringUtils.isNotEmpty(result.getResult())) {
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode rootNode = mapper.readTree(result.getResult());
                    var items = rootNode.get("ListGroupsForUserResult").get("Groups").get("member");
                    List<UserGroupInfo> groupList = new ArrayList<>();
                    if (items.isArray()) {
                        for (JsonNode item : items) {
                            UserGroupInfo userGroupInfo = new UserGroupInfo();
                            userGroupInfo.setGroupId(item.get("GroupName").asText());
                            userGroupInfo.setGroupName(item.get("GroupName").asText());
                            groupList.add(userGroupInfo);
                        }
                    } 
                }

                // 用户权限列表
                try {
                    ListAttachedUserPoliciesRequest request1 = new ListAttachedUserPoliciesRequest();
                    request1.setUserName(user.getUserName());
                    request1.setMaxItems("999");
                    var response = iamApi.doPost("iam.api.ksyun.com", request1);
                    if (StringUtils.isNotEmpty(response.getResult())) {
                        ObjectMapper mapper1 = new ObjectMapper();
                        JsonNode rootNode1 = mapper1.readTree(response.getResult());
                        var items1 = rootNode1.get("ListAttachedUserPoliciesResult").get("AttachedPolicies").get("member");
                        List<PermissionEntity> policyList = new ArrayList<>();
                        if (items1.isArray()) {
                            for (JsonNode item : items1) {
                                PermissionEntity permission = new PermissionEntity();
                                permission.setPolicyName(item.get("PolicyName").asText());
                                try {
                                    permission.setPolicyId(Long.valueOf(item.get("PolicyId").asText()));
                                } catch (NumberFormatException e) {
                                    permission.setPolicyId(null);
                                }
                                permission.setPolicyType(item.get("PolicyType").asText());
                                permission.setBelongId(user.getUserId());
                                permission.setBelongName(user.getUserName());
                                permission.setBelongType(Constants.BELONG_TYPE_USER);
                                policyList.add(permission);
                            }
                        }
                        user.setPolicyList(policyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户权限信息异常: {}", e.getMessage());
                }


                // 用户密钥ID
                try {
                    ListAccessKeysRequest request2 = new ListAccessKeysRequest();
                    request2.setUserName(user.getUserName());
                    var accessKeyResponse = accessKeyClient.doPost("iam.api.ksyun.com", request2);
                    if (StringUtils.isNotEmpty(accessKeyResponse.getResult())) {
                        ObjectMapper mapper2 = new ObjectMapper();
                        JsonNode rootNode2 = mapper2.readTree(accessKeyResponse.getResult());
                        var items2 = rootNode2.get("ListAccessKeyResult").get("AccessKeyMetadata").get("member");
                        List<PermanentAccessKeyEntity> accessKeyList = new ArrayList<>();
                        if (items2.isArray()) {
                            for (JsonNode item : items2) {
                                PermanentAccessKeyEntity accessKeyEntity = new PermanentAccessKeyEntity();
                                accessKeyEntity.setAccess(item.get("AccessKeyId").asText());
                                accessKeyEntity.setStatus(item.get("Status").asText());
                                accessKeyEntity.setCreateTime(item.get("CreateDate").asText());
                                accessKeyEntity.setUserId(user.getUserId());
                                accessKeyEntity.setUserName(user.getUserName());
                                accessKeyEntity.setAccountId(accountId);
                                accessKeyList.add(accessKeyEntity);
                            }
                        }
                        user.setAccessKeyList(accessKeyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户密钥信息异常: {}", e.getMessage());
                }
                
                userList.set(i, user);
            }
        } catch (Exception e) {
            log.error("Error attaching user information: {}", e.getMessage());
        }
    }
}
