package com.tt.cloud.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/21 10:56
 */
@Getter
@Setter
@ToString
public class LoginProtectEntity {
    private String userId;
    // IAM用户是否开启登录保护，开启为"true"，不开启为"false"。
    private boolean enabled;
    // IAM用户登录验证方式。手机验证为“sms”,邮箱验证为“email”,MFA验证为“vmfa”。
    private String verificationMethod;
    private String accountId;
}
