package com.tt.cloud.config;

import com.tencentcloudapi.cam.v20190116.CamClient;
import com.tencentcloudapi.clb.v20180317.ClbClient;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.dcdb.v20180411.DcdbClient;
import com.tencentcloudapi.dnspod.v20210323.DnspodClient;
import com.tencentcloudapi.region.v20220627.RegionClient;
import com.tencentcloudapi.tke.v20180525.TkeClient;
import com.tencentcloudapi.vpc.v20170312.VpcClient;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.CacheUtil;
import com.tt.cloud.util.EncryptUtils;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 15:02
 */
@Slf4j
@Component
public class TencentCloudHttpConfig {
    @Resource
    private CloudAccountDao cloudAccountDao;

    public CamClient getTencentCamClient(String accountId) {
        String cacheKey = "Tencent_CamClient_" + accountId;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (CamClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("cam.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        Credential cred = new Credential(cloudAccount.getSecretId(),
                EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        CamClient camClient = new CamClient(cred, "", clientProfile);
        CacheUtil.put(cacheKey, camClient, 24 * 60 * 60);
        return camClient;
    }

    public ClbClient getTencentClbClient(String accountId, String region) {
        String cacheKey = "Tencent_ClbClient_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (ClbClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("clb.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        Credential cred = new Credential(cloudAccount.getSecretId(),
                EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        ClbClient clbClient = new ClbClient(cred, region, clientProfile);
        CacheUtil.put(cacheKey, clbClient, 24 * 60 * 60);
        return clbClient;
    }

    public VpcClient getTencentVpcClient(String accountId, String region) {
        String cacheKey = "Tencent_VpcClient_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (VpcClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        Credential cred = new Credential(cloudAccount.getSecretId(),
                EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("vpc.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        VpcClient vpcClient = new VpcClient(cred, region, clientProfile);
        CacheUtil.put(cacheKey, vpcClient, 24 * 60 * 60);
        return vpcClient;
    }

    public TkeClient getTencentTkeClient(String accountId, String region) {
        String cacheKey = "Tencent_TkeClient_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (TkeClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        Credential cred = new Credential(cloudAccount.getSecretId(),
                EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("tke.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        TkeClient tkeClient = new TkeClient(cred, region, clientProfile);
        CacheUtil.put(cacheKey, tkeClient, 24 * 60 * 60);
        return tkeClient;
    }

    public DcdbClient getTencentDcdbClient(String accountId, String region) {
        String cacheKey = "Tencent_DcdbClient_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (DcdbClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        Credential cred = new Credential(cloudAccount.getSecretId(),
                EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("dcdb.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        DcdbClient dcdbClient = new DcdbClient(cred, region, clientProfile);
        CacheUtil.put(cacheKey, dcdbClient, 24 * 60 * 60);
        return dcdbClient;
    }

    public DnspodClient getTencentDnspodClient(String accountId) {
        String cacheKey = "Tencent_DnspodClient_" + accountId;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (DnspodClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        Credential cred = new Credential(cloudAccount.getSecretId(),
                EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("dnspod.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        DnspodClient dnspodClient = new DnspodClient(cred, "", clientProfile);
        CacheUtil.put(cacheKey, dnspodClient, 24 * 60 * 60);
        return dnspodClient;
    }

    public RegionClient getTencentRegionClient(String accountId) {
        String cacheKey = "Tencent_RegionClient_" + accountId;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (RegionClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        Credential cred = new Credential(cloudAccount.getSecretId(),
                EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("region.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        RegionClient regionClient = new RegionClient(cred, "", clientProfile); // ap-beijing
        CacheUtil.put(cacheKey, regionClient, 24 * 60 * 60);
        return regionClient;
    }

}
