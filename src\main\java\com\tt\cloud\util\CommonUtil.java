package com.tt.cloud.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.tt.cloud.bean.ResponseHeader;
import com.tt.cloud.exception.ServiceRuntimeException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2021/9/8 18:12
 */
@Slf4j
public class CommonUtil {
  private CommonUtil() {}

  /**
   * 加密生成token
   *
   * @param subject 载体信息
   * @param maxAge 有效时长
   * @param secret 服务器私钥
   * @return String
   */
  public static String createToken(String subject, long maxAge, String secret) {
    try {
      final Algorithm signer = Algorithm.HMAC256(secret); // 生成签名
      return JWT.create()
              .withIssuer("admin")
              .withSubject(subject)
              .withClaim("userId", subject)
              .withExpiresAt(new Date(System.currentTimeMillis() + maxAge))
              .sign(signer);
    } catch (Exception e) {
      log.error("生成token异常：", e);
      return null;
    }
  }

  public static void setErrorMsg(ResponseHeader header, Exception e, String s) {
    if (e instanceof ServiceRuntimeException) {
      header.setErrorMsg(((ServiceRuntimeException) e).getErrorMsg());
      log.error(header.getMsg());
    } else {
      log.error(s + "：" + e.getMessage());
      header.setErrorMsg(s);
    }
  }

  /**
   * 解析验证token
   *
   * @param token 加密后的token字符串
   * @param secret 服务器私钥
   * @return Boolean
   */
  public static String verifyToken(String token, String secret) {
    try {
      Algorithm algorithm = Algorithm.HMAC256(secret);
      JWTVerifier verifier = JWT.require(algorithm).build();
      DecodedJWT decodedJWT = verifier.verify(token);
      return decodedJWT.getClaim("userId").asString();
    } catch (IllegalArgumentException | JWTVerificationException e) {
      log.error("校验失败", e);
    }
    return StringUtils.EMPTY;
  }

  public static String parseTmpToken(String token, String secret) {
    try {
      Algorithm algorithm = Algorithm.HMAC256(secret);
      JWTVerifier verifier = JWT.require(algorithm).build();
      DecodedJWT decodedJWT = verifier.verify(token);
      return decodedJWT.getSubject();
    } catch (IllegalArgumentException | JWTVerificationException e) {
      log.error("校验失败", e);
    }
    return StringUtils.EMPTY;
  }

  public static void addCookie(
          HttpServletResponse response, String cookieName, String cookieValue) {
    Cookie authorization = new Cookie(cookieName, cookieValue);
    authorization.setPath("/");
    authorization.setHttpOnly(true);
    response.addCookie(authorization);
  }

  public static String getCookieValue(HttpServletRequest request, String cookieName) {
    Cookie[] cookies = request.getCookies();
    if (null != cookies) {
      for (Cookie cookie : cookies) {
        if (cookie.getName().equals(cookieName)) {
          return cookie.getValue();
        }
      }
    }
    return null;
  }

  public static String translateToChinese(String alphabet) {
    String[] alphabets = {"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m",
            "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"};
    for (int i = 0; i < alphabets.length; i++) {
      if (alphabet.equals(alphabets[i])){
        return "可用区" + (i + 1);
      }
    }
    return "输入错误";
  }

  public static <T> List<T> getPage(List<T> sourceList, long currentPage, long pageSize) {
    long fromIndex = currentPage <= 0 ? 0 : (currentPage - 1) * pageSize;
    if (sourceList == null || sourceList.size() < fromIndex) {
      return Collections.emptyList();
    }
    return sourceList.subList((int)fromIndex, (int) Math.min(fromIndex + pageSize, sourceList.size()));
  }

}
