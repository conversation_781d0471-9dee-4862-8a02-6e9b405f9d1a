package com.tt.cloud;

import java.security.Security;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.opensaml.DefaultBootstrap;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@ServletComponentScan(basePackages = {"com.tt.cloud.*", "com.tt.cloud.*.*"})
@EnableAsync
@SpringBootApplication
@EnableScheduling
@MapperScan(value = {"com.tt.cloud.*"})
public class TtCloudAuthManageApplication {

  static {
    try {
      DefaultBootstrap.bootstrap();
    } catch (Exception e) {
      log.error("bootstrap failed !", e);
    }
  }

  public static void main(String[] args) {
    Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
    SpringApplication.run(TtCloudAuthManageApplication.class, args);
  }

}
