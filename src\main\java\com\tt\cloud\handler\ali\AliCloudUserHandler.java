package com.tt.cloud.handler.ali;

import com.aliyun.ram20150501.models.AddUserToGroupRequest;
import com.aliyun.ram20150501.models.CreateLoginProfileRequest;
import com.aliyun.ram20150501.models.CreateUserRequest;
import com.aliyun.ram20150501.models.GetAccountAliasResponse;
import com.aliyun.ram20150501.models.GetUserRequest;
import com.aliyun.ram20150501.models.GetUserResponse;
import com.tt.cloud.bean.AliCloudResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.UserAuthEntity;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 阿里云用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/19 9:55
 */
@Slf4j
@Component
public class AliCloudUserHandler {
    @Resource
    private AliCloudHttpConfig aliCloudHttpConfig;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 创建用户
     *
     * @param userEntity userEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse createUser(UserEntity userEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("阿里云新增用户：{}", userEntity);
            CreateUserRequest request = new CreateUserRequest();
            request.setUserName(userEntity.getUserName());
            request.setDisplayName(userEntity.getUserName());
            request.setEmail(userEntity.getEmail());
            request.setMobilePhone(userEntity.getPhone());
            request.setComments(userEntity.getRemark());
            aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).createUser(request);
            log.info("阿里云新增用户完毕");
            if (null != userEntity.getGroupNameList() && !userEntity.getGroupNameList().isEmpty()) {
                log.info("将用户添加到用户组中：{}", userEntity.getGroupNameList());
                for (String groupName : userEntity.getGroupNameList()) {
                    AddUserToGroupRequest addUserToGroupRequest = new AddUserToGroupRequest();
                    addUserToGroupRequest.setUserName(userEntity.getUserName());
                    addUserToGroupRequest.setGroupName(groupName);
                    aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                }
            }
            if (!userEntity.isApiUser()) {
                CreateLoginProfileRequest createLoginProfileRequest = new CreateLoginProfileRequest();
                createLoginProfileRequest.setUserName(userEntity.getUserName());
                createLoginProfileRequest.setPassword(Utils.getPassword());
                createLoginProfileRequest.setMFABindRequired(false);
                createLoginProfileRequest.setPasswordResetRequired(true);
                aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).createLoginProfile(createLoginProfileRequest);
                userEntity.setPassword(createLoginProfileRequest.password);
                sendMessage(userEntity);
            }
            aliCloudResponse.setUserEntity(userEntity);
        } catch (Exception e) {
            log.error("阿里云新增用户异常", e);
            aliCloudResponse.getHeader().setErrorMsg("阿里云新增用户异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    private void sendMessage(UserEntity userEntity) throws Exception {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
        Map<String, Object> messageParam = new HashMap<>();
        messageParam.put("cloud", cloudAccount.getMemo() + "/" + cloudAccount.getName() + "/" + cloudAccount.getDomainId());
        messageParam.put("username", userEntity.getUserName());
        messageParam.put("password", userEntity.getPassword());
        messageParam.put("url", "*由于开启云商单点登录，关闭密码登录方式*\\n*请通过[运维平台](https://yw-sso.ttyuyin.com/index/dashboard)->"
                + "[云泽平台](https://yw-cloud.ttyuyin.com/tt-cloud/cloud/center)进行登录*");
        String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
        content = Utils.getParseValue(applicationContext, messageParam, content);
        flyBookService.sendMessage(userEntity.getEmail(), content);
    }

    /**
     * 查詢用戶是否存在
     *
     * @param userEntity userEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryUserExists(UserEntity userEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            UserAuthEntity userAuthInfo = new UserAuthEntity();
            userAuthInfo.setAccountId(userEntity.getAccountId());
            userAuthInfo.setUname(userEntity.getUserName());
            UserAuthEntity userAuthEntity = cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
            if (null != userAuthEntity) {
                UserEntity user = new UserEntity();
                user.setUserId(userAuthEntity.getUid());
                user.setUserName(userAuthEntity.getUname());
                aliCloudResponse.setUserEntity(user);
            } else {
                aliCloudResponse.setUserEntity(queryUser(userEntity));
            }
            if (null != aliCloudResponse.getUserEntity()) {
                GetAccountAliasResponse getAccountAliasResponse
                        = aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).getAccountAlias();
                CloudAccountEntity cloudAccount
                        = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
                String endpoint = "https://signin.aliyun.com/";
                if (cloudAccount.isInternational()) {
                    endpoint = "https://signin-intl.aliyun.com/";
                }
                String loginUrl = endpoint
                        + getAccountAliasResponse.getBody().getAccountAlias()
                        + ".onaliyun.com/login.htm?username=" + UserContext.getCurrentUserId() + "&callback=" + cloudAccount.getDestination();
                aliCloudResponse.setLoginUrl(loginUrl);
            }
        } catch (Exception e) {
            log.error("阿里云查询用户是否存在异常", e);
            aliCloudResponse.getHeader().setErrorMsg("阿里云查询用户是否存在异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    public UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        try {
            GetUserRequest getUserRequest = new GetUserRequest();
            getUserRequest.setUserName(userEntity.getUserName());
            GetUserResponse getUserResponse = aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).getUser(getUserRequest);
            if (null != getUserResponse.getBody().getUser()) {
                userInfo = new UserEntity();
                userInfo.setUserId(getUserResponse.getBody().getUser().getUserId());
                userInfo.setUserName(getUserResponse.getBody().getUser().getUserName());
            }
        } catch (Exception e) {
            if (e.getMessage().contains("The user does not exist")) {
                if (!userEntity.getUserName().contains("@52tt.com")) {
                    userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                    return queryUser(userEntity);
                }
            }
        }
        return userInfo;
    }

    /**
     * 查询登录名称
     *
     * @return String
     */
    public String queryUserLoginName(String accountAlias) {
        try {
            return UserContext.getCurrentUserId() + "@" + accountAlias + ".onaliyun.com";
        } catch (Exception e) {
            log.error("查询阿里云用户登录名称异常");
        }
        return StringUtils.EMPTY;
    }

    public Object queryUserListByAccount(CloudAccountEntity cloudAccount) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'queryUserListByAccount'");
    }

}
