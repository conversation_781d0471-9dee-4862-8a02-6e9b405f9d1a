package com.tt.cloud.handler.ali;

import com.alibaba.fastjson.JSON;
import com.aliyun.ram20150501.models.AddUserToGroupRequest;
import com.aliyun.ram20150501.models.CreateLoginProfileRequest;
import com.aliyun.ram20150501.models.CreateUserRequest;
import com.aliyun.ram20150501.models.GetAccountAliasResponse;
import com.aliyun.ram20150501.models.GetUserRequest;
import com.aliyun.ram20150501.models.GetUserResponse;
import com.aliyun.ram20150501.models.ListAccessKeysRequest;
import com.aliyun.ram20150501.models.ListAccessKeysResponse;
import com.aliyun.ram20150501.models.ListGroupsForUserRequest;
import com.aliyun.ram20150501.models.ListGroupsForUserResponse;
import com.aliyun.ram20150501.models.ListGroupsForUserResponseBody.ListGroupsForUserResponseBodyGroupsGroup;
import com.aliyun.ram20150501.models.ListPoliciesForUserRequest;
import com.aliyun.ram20150501.models.ListPoliciesForUserResponse;
import com.aliyun.ram20150501.models.ListPoliciesForUserResponseBody.ListPoliciesForUserResponseBodyPoliciesPolicy;
import com.aliyun.ram20150501.models.ListAccessKeysResponseBody.ListAccessKeysResponseBodyAccessKeysAccessKey;
import com.aliyun.ram20150501.models.ListUsersRequest;
import com.aliyun.ram20150501.models.ListUsersResponse;
import com.aliyun.ram20150501.models.ListUsersResponseBody.ListUsersResponseBodyUsersUser;
import com.tt.cloud.bean.AliCloudResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAllUserResponse;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.bean.PermanentAccessKeyEntity;
import com.tt.cloud.bean.UserAuthEntity;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.UserGroupInfo;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.redis.core.StringRedisTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 阿里云用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/19 9:55
 */
@Slf4j
@Component
public class AliCloudUserHandler {
    @Resource
    private AliCloudHttpConfig aliCloudHttpConfig;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 创建用户
     *
     * @param userEntity userEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse createUser(UserEntity userEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("阿里云新增用户：{}", userEntity);
            CreateUserRequest request = new CreateUserRequest();
            request.setUserName(userEntity.getUserName());
            request.setDisplayName(userEntity.getUserName());
            request.setEmail(userEntity.getEmail());
            request.setMobilePhone(userEntity.getPhone());
            request.setComments(userEntity.getRemark());
            aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).createUser(request);
            log.info("阿里云新增用户完毕");
            if (null != userEntity.getGroupNameList() && !userEntity.getGroupNameList().isEmpty()) {
                log.info("将用户添加到用户组中：{}", userEntity.getGroupNameList());
                for (String groupName : userEntity.getGroupNameList()) {
                    AddUserToGroupRequest addUserToGroupRequest = new AddUserToGroupRequest();
                    addUserToGroupRequest.setUserName(userEntity.getUserName());
                    addUserToGroupRequest.setGroupName(groupName);
                    aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                }
            }
            if (!userEntity.isApiUser()) {
                CreateLoginProfileRequest createLoginProfileRequest = new CreateLoginProfileRequest();
                createLoginProfileRequest.setUserName(userEntity.getUserName());
                createLoginProfileRequest.setPassword(Utils.getPassword());
                createLoginProfileRequest.setMFABindRequired(false);
                createLoginProfileRequest.setPasswordResetRequired(true);
                aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).createLoginProfile(createLoginProfileRequest);
                userEntity.setPassword(createLoginProfileRequest.password);
                sendMessage(userEntity);
            }
            aliCloudResponse.setUserEntity(userEntity);
        } catch (Exception e) {
            log.error("阿里云新增用户异常", e);
            aliCloudResponse.getHeader().setErrorMsg("阿里云新增用户异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    private void sendMessage(UserEntity userEntity) throws Exception {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
        Map<String, Object> messageParam = new HashMap<>();
        messageParam.put("cloud", cloudAccount.getMemo() + "/" + cloudAccount.getName() + "/" + cloudAccount.getDomainId());
        messageParam.put("username", userEntity.getUserName());
        messageParam.put("password", userEntity.getPassword());
        messageParam.put("url", "*由于开启云商单点登录，关闭密码登录方式*\\n*请通过[运维平台](https://yw-sso.ttyuyin.com/index/dashboard)->"
                + "[云泽平台](https://yw-cloud.ttyuyin.com/tt-cloud/cloud/center)进行登录*");
        String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
        content = Utils.getParseValue(applicationContext, messageParam, content);
        flyBookService.sendMessage(userEntity.getEmail(), content);
    }

    /**
     * 查詢用戶是否存在
     *
     * @param userEntity userEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryUserExists(UserEntity userEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            UserAuthEntity userAuthInfo = new UserAuthEntity();
            userAuthInfo.setAccountId(userEntity.getAccountId());
            userAuthInfo.setUname(userEntity.getUserName());
            UserAuthEntity userAuthEntity = cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
            if (null != userAuthEntity) {
                UserEntity user = new UserEntity();
                user.setUserId(userAuthEntity.getUid());
                user.setUserName(userAuthEntity.getUname());
                aliCloudResponse.setUserEntity(user);
            } else {
                aliCloudResponse.setUserEntity(queryUser(userEntity));
            }
            if (null != aliCloudResponse.getUserEntity()) {
                GetAccountAliasResponse getAccountAliasResponse
                        = aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).getAccountAlias();
                CloudAccountEntity cloudAccount
                        = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
                String endpoint = "https://signin.aliyun.com/";
                if (cloudAccount.isInternational()) {
                    endpoint = "https://signin-intl.aliyun.com/";
                }
                String loginUrl = endpoint
                        + getAccountAliasResponse.getBody().getAccountAlias()
                        + ".onaliyun.com/login.htm?username=" + UserContext.getCurrentUserId() + "&callback=" + cloudAccount.getDestination();
                aliCloudResponse.setLoginUrl(loginUrl);
            }
        } catch (Exception e) {
            log.error("阿里云查询用户是否存在异常", e);
            aliCloudResponse.getHeader().setErrorMsg("阿里云查询用户是否存在异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    public UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        try {
            GetUserRequest getUserRequest = new GetUserRequest();
            getUserRequest.setUserName(userEntity.getUserName());
            GetUserResponse getUserResponse = aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).getUser(getUserRequest);
            if (null != getUserResponse.getBody().getUser()) {
                userInfo = new UserEntity();
                userInfo.setUserId(getUserResponse.getBody().getUser().getUserId());
                userInfo.setUserName(getUserResponse.getBody().getUser().getUserName());
            }
        } catch (Exception e) {
            if (e.getMessage().contains("The user does not exist")) {
                if (!userEntity.getUserName().contains("@52tt.com")) {
                    userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                    return queryUser(userEntity);
                }
            }
        }
        return userInfo;
    }

    /**
     * 查询登录名称
     *
     * @return String
     */
    public String queryUserLoginName(String accountAlias) {
        try {
            return UserContext.getCurrentUserId() + "@" + accountAlias + ".onaliyun.com";
        } catch (Exception e) {
            log.error("查询阿里云用户登录名称异常");
        }
        return StringUtils.EMPTY;
    }

    public CloudAllUserResponse queryUserListByAccount(CloudAccountEntity cloudAccount, String nextMarker) {
        CloudAllUserResponse userListRst = new CloudAllUserResponse();
        try {
            log.info("阿里云子用户详情列表");

            // userList 先从redis里获取，如果没有，再调用接口获取，然后存入redis
            // redis key: ali-all-user-list
            // redis value: userList
            // redis 过期时间 20 分钟
            List<UserEntity> userList = new ArrayList<>();
            String redisKey = "ali-all-user-list:" + cloudAccount.getId();

            try {
                // 先从 Redis 获取用户列表
                String cachedUserListJson = stringRedisTemplate.opsForValue().get(redisKey);
                if (cachedUserListJson != null) {
                    log.info("从Redis缓存获取到阿里云用户列表，key: {}", redisKey);
                    // 将JSON字符串转换为用户列表
                    userList = JSON.parseArray(cachedUserListJson, UserEntity.class);
                } else {
                    log.info("Redis缓存中未找到阿里云用户列表，开始调用API获取，key: {}", redisKey);
                    // Redis中没有数据，调用阿里云API获取
                    ListUsersRequest request = new ListUsersRequest();
                    request.setMaxItems(1000); // 阿里云最大支持1000条
                    ListUsersResponse response = aliCloudHttpConfig.getAliIamClient(cloudAccount.getId()).listUsers(request);

                    if (response != null && response.getBody() != null && response.getBody().getUsers() != null
                        && response.getBody().getUsers().getUser() != null && !response.getBody().getUsers().getUser().isEmpty()) {
                        for (ListUsersResponseBodyUsersUser user : response.getBody().getUsers().getUser()) {
                            UserEntity userEntity = new UserEntity();
                            userEntity.setUserId(user.getUserId());
                            userEntity.setUserIn(user.getUserId()); // 阿里云没有UIN概念，使用UserId
                            userEntity.setUserName(user.getUserName());
                            userEntity.setCreateTime(user.getCreateDate());
                            userEntity.setRemark(user.getComments() != null ? user.getComments() : "");
                            userEntity.setPhone(user.getMobilePhone() != null ? user.getMobilePhone() : "");
                            userEntity.setEmail(user.getEmail() != null ? user.getEmail() : "");
                            userEntity.setConsoleLogin(1L); // 阿里云默认支持控制台登录
                            userEntity.setAccountId(cloudAccount.getDomainId());
                            userList.add(userEntity);
                        }

                        // 将用户列表存入Redis，过期时间20分钟
                        String userListJson = JSON.toJSONString(userList);
                        stringRedisTemplate.opsForValue().set(redisKey, userListJson, 20, TimeUnit.MINUTES);
                        log.info("已将阿里云用户列表存入Redis缓存，key: {}, 用户数量: {}", redisKey, userList.size());
                    }
                }
            } catch (Exception e) {
                log.error("Redis操作异常，直接调用API获取用户列表: {}", e.getMessage(), e);
            }

            // nextMarker 有字符串转为int，如果不是数字型或空，则默认为0
            int pageIndex = 0;
            if (nextMarker != null && !nextMarker.isEmpty()) {
                try {
                    pageIndex = Integer.parseInt(nextMarker);
                    if (pageIndex < 0) {
                        pageIndex = 0; // 负数也默认为0
                    }
                } catch (NumberFormatException e) {
                    log.warn("nextMarker格式错误，无法转换为整数: {}, 使用默认值0", nextMarker);
                    pageIndex = 0;
                }
            }

            // 处理分页逻辑：将nextMarker转为int，并从userList中切片
            int size = 20;
            int startIndex = pageIndex * size;
            int endIndex = (pageIndex + 1) * size;
            List<UserEntity> pagedUserList = userList;
            if (startIndex >= userList.size()) {
                return userListRst;
            }
            if (endIndex > userList.size()) {
                endIndex = userList.size();
                userListRst.setNextMarker(Strings.EMPTY);
            } else {
                userListRst.setNextMarker(String.valueOf(pageIndex + 1));
            }
            pagedUserList = userList.subList(startIndex, endIndex);

            // 对分页后的用户列表进行详细信息补充
            attachUserInfos(pagedUserList, cloudAccount.getId());

            // 设置返回结果
            userListRst.setUserList(pagedUserList);

        } catch (Exception e) {
            log.error("查询阿里云子用户详情列表异常", e);
            throw new RuntimeException("查询阿里云子用户详情列表异常" + e.getMessage());
        }
        return userListRst;
    }

    /**
     * 安全获取字符串值，如果为null则返回空字符串
     */
    private String safeGetString(String value) {
        return value != null ? value : "";
    }

    /**
     * 补充用户详细信息
     */
    private void attachUserInfos(List<UserEntity> userList, String accountId) {
        if (userList == null || userList.isEmpty()) {
            return;
        }
        try {
            // 获取用户信息
            for (int i = 0; i < userList.size(); i++) {
                UserEntity user = userList.get(i);

                // 获取最后登录时间和登录IP（阿里云可能不支持，设置默认值）
                try {
                    // 阿里云暂不支持获取最后登录信息，设置默认值
                    user.setLastLoginIp("");
                    user.setLastLoginTime("");
                } catch (Exception e) {
                    log.error("获取用户登录信息异常: {}", e.getMessage());
                }

                // 1. 获取用户组列表
                try {
                    ListGroupsForUserRequest request = new ListGroupsForUserRequest();
                    request.setUserName(user.getUserName());
                    ListGroupsForUserResponse response = aliCloudHttpConfig.getAliIamClient(accountId).listGroupsForUser(request);

                    if (response != null && response.getBody() != null && response.getBody().getGroups() != null
                        && response.getBody().getGroups().getGroup() != null) {
                        List<UserGroupInfo> groupList = new ArrayList<>();
                        for (ListGroupsForUserResponseBodyGroupsGroup group : response.getBody().getGroups().getGroup()) {
                            UserGroupInfo userGroupInfo = new UserGroupInfo();
                            userGroupInfo.setGroupId(safeGetString(group.getGroupId()));
                            userGroupInfo.setGroupName(safeGetString(group.getGroupName()));
                            userGroupInfo.setRemark(safeGetString(group.getComments()));
                            groupList.add(userGroupInfo);
                        }
                        user.setGroupList(groupList);
                    }
                } catch (Exception e) {
                    log.error("获取用户组信息异常: {}", e.getMessage());
                }

                // 2. 获取用户权限列表
                try {
                    ListPoliciesForUserRequest request1 = new ListPoliciesForUserRequest();
                    request1.setUserName(user.getUserName());
                    ListPoliciesForUserResponse response = aliCloudHttpConfig.getAliIamClient(accountId).listPoliciesForUser(request1);

                    if (response != null && response.getBody() != null && response.getBody().getPolicies() != null
                        && response.getBody().getPolicies().getPolicy() != null) {
                        List<PermissionEntity> policyList = new ArrayList<>();
                        for (ListPoliciesForUserResponseBodyPoliciesPolicy policy : response.getBody().getPolicies().getPolicy()) {
                            PermissionEntity permission = new PermissionEntity();
                            permission.setPolicyName(safeGetString(policy.getPolicyName()));
                            permission.setPolicyType(safeGetString(policy.getPolicyType()));
                            permission.setCreateMode(null); // 阿里云可能不支持此字段
                            permission.setAddTime(safeGetString(policy.getAttachDate()));
                            permission.setRemark(safeGetString(policy.getDescription()));
                            permission.setBelongId(user.getUserId());
                            permission.setBelongName(user.getUserName());
                            permission.setBelongType(Constants.BELONG_TYPE_USER);
                            policyList.add(permission);
                        }
                        user.setPolicyList(policyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户权限信息异常: {}", e.getMessage());
                }

                // 3. 获取用户密钥ID列表
                try {
                    ListAccessKeysRequest request2 = new ListAccessKeysRequest();
                    request2.setUserName(user.getUserName());
                    ListAccessKeysResponse response = aliCloudHttpConfig.getAliIamClient(accountId).listAccessKeys(request2);

                    if (response != null && response.getBody() != null && response.getBody().getAccessKeys() != null
                        && response.getBody().getAccessKeys().getAccessKey() != null) {
                        List<PermanentAccessKeyEntity> accessKeyList = new ArrayList<>();
                        for (ListAccessKeysResponseBodyAccessKeysAccessKey accessKey : response.getBody().getAccessKeys().getAccessKey()) {
                            PermanentAccessKeyEntity accessKeyEntity = new PermanentAccessKeyEntity();
                            accessKeyEntity.setAccess(safeGetString(accessKey.getAccessKeyId()));
                            accessKeyEntity.setStatus(safeGetString(accessKey.getStatus()));
                            accessKeyEntity.setCreateTime(safeGetString(accessKey.getCreateDate()));
                            accessKeyEntity.setDescription(""); // 阿里云可能不支持描述字段
                            accessKeyEntity.setUserId(user.getUserId());
                            accessKeyEntity.setUserName(user.getUserName());
                            accessKeyEntity.setAccountId(accountId);

                            // 获取密钥最后使用时间（阿里云可能不支持，设置默认值）
                            try {
                                // 阿里云暂不支持获取密钥最后使用时间，设置默认值
                                accessKeyEntity.setLastUsedDate("");
                            } catch (Exception e) {
                                log.error("获取用户密钥最后使用时间异常: {}", e.getMessage());
                            }

                            accessKeyList.add(accessKeyEntity);
                        }
                        user.setAccessKeyList(accessKeyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户密钥信息异常: {}", e.getMessage());
                }

                userList.set(i, user);
            }
        } catch (Exception e) {
            log.error("阿里云完善用户详细信息异常", e);
        }
    }

}
