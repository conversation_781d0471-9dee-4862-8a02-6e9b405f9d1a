package com.tt.cloud.handler.ali;


import com.aliyun.ram20150501.models.*;
import com.aliyun.ram20150501.models.CreateLoginProfileRequest;
import com.aliyun.ram20150501.models.CreateUserRequest;
import com.aliyun.ram20150501.models.GetAccountAliasResponse;
import com.aliyun.ram20150501.models.GetUserRequest;
import com.aliyun.ram20150501.models.GetUserResponse;
import com.aliyun.ram20150501.models.GetUserResponseBody.GetUserResponseBodyUser;
import com.aliyun.ram20150501.models.ListAccessKeysRequest;
import com.aliyun.ram20150501.models.ListAccessKeysResponse;
import com.aliyun.ram20150501.models.ListGroupsForUserRequest;
import com.aliyun.ram20150501.models.ListGroupsForUserResponse;
import com.aliyun.ram20150501.models.ListGroupsForUserResponseBody.ListGroupsForUserResponseBodyGroupsGroup;
import com.aliyun.ram20150501.models.ListPoliciesForGroupRequest;
import com.aliyun.ram20150501.models.ListPoliciesForGroupResponse;
import com.aliyun.ram20150501.models.ListPoliciesForGroupResponseBody.ListPoliciesForGroupResponseBodyPoliciesPolicy;
import com.aliyun.ram20150501.models.ListPoliciesForUserRequest;
import com.aliyun.ram20150501.models.ListPoliciesForUserResponse;
import com.aliyun.ram20150501.models.ListPoliciesForUserResponseBody.ListPoliciesForUserResponseBodyPoliciesPolicy;
import com.aliyun.ram20150501.models.ListAccessKeysResponseBody.ListAccessKeysResponseBodyAccessKeysAccessKey;
import com.aliyun.ram20150501.models.ListUsersRequest;
import com.aliyun.ram20150501.models.ListUsersResponse;
import com.aliyun.ram20150501.models.ListUsersResponseBody.ListUsersResponseBodyUsersUser;
import com.tt.cloud.bean.AliCloudResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAllUserResponse;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.bean.PermanentAccessKeyEntity;
import com.tt.cloud.bean.UserAuthEntity;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.bean.UserGroupInfo;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.config.AliCloudHttpConfig;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.Utils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import lombok.var;

/**
 * 阿里云用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/19 9:55
 */
@Slf4j
@Component
public class AliCloudUserHandler {
    @Resource
    private AliCloudHttpConfig aliCloudHttpConfig;
    @Resource
    private FlyBookService flyBookService;
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 创建用户
     *
     * @param userEntity userEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse createUser(UserEntity userEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            log.info("阿里云新增用户：{}", userEntity);
            CreateUserRequest request = new CreateUserRequest();
            request.setUserName(userEntity.getUserName());
            request.setDisplayName(userEntity.getUserName());
            request.setEmail(userEntity.getEmail());
            request.setMobilePhone(userEntity.getPhone());
            request.setComments(userEntity.getRemark());
            aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).createUser(request);
            log.info("阿里云新增用户完毕");
            if (null != userEntity.getGroupNameList() && !userEntity.getGroupNameList().isEmpty()) {
                log.info("将用户添加到用户组中：{}", userEntity.getGroupNameList());
                for (String groupName : userEntity.getGroupNameList()) {
                    AddUserToGroupRequest addUserToGroupRequest = new AddUserToGroupRequest();
                    addUserToGroupRequest.setUserName(userEntity.getUserName());
                    addUserToGroupRequest.setGroupName(groupName);
                    aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).addUserToGroup(addUserToGroupRequest);
                }
            }
            if (!userEntity.isApiUser()) {
                CreateLoginProfileRequest createLoginProfileRequest = new CreateLoginProfileRequest();
                createLoginProfileRequest.setUserName(userEntity.getUserName());
                createLoginProfileRequest.setPassword(Utils.getPassword());
                createLoginProfileRequest.setMFABindRequired(false);
                createLoginProfileRequest.setPasswordResetRequired(true);
                aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).createLoginProfile(createLoginProfileRequest);
                userEntity.setPassword(createLoginProfileRequest.password);
                sendMessage(userEntity);
            }
            aliCloudResponse.setUserEntity(userEntity);
        } catch (Exception e) {
            log.error("阿里云新增用户异常", e);
            aliCloudResponse.getHeader().setErrorMsg("阿里云新增用户异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    private void sendMessage(UserEntity userEntity) throws Exception {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
        Map<String, Object> messageParam = new HashMap<>();
        messageParam.put("cloud", cloudAccount.getMemo() + "/" + cloudAccount.getName() + "/" + cloudAccount.getDomainId());
        messageParam.put("username", userEntity.getUserName());
        messageParam.put("password", userEntity.getPassword());
        messageParam.put("url", "*由于开启云商单点登录，关闭密码登录方式*\\n*请通过[运维平台](https://yw-sso.ttyuyin.com/index/dashboard)->"
                + "[云泽平台](https://yw-cloud.ttyuyin.com/tt-cloud/cloud/center)进行登录*");
        String content = Utils.readTxtFile("/fly_book_template/welcome_user.txt");
        content = Utils.getParseValue(applicationContext, messageParam, content);
        flyBookService.sendMessage(userEntity.getEmail(), content);
    }

    /**
     * 查詢用戶是否存在
     *
     * @param userEntity userEntity
     * @return AliCloudResponse
     */
    public AliCloudResponse queryUserExists(UserEntity userEntity) {
        AliCloudResponse aliCloudResponse = new AliCloudResponse();
        try {
            UserAuthEntity userAuthInfo = new UserAuthEntity();
            userAuthInfo.setAccountId(userEntity.getAccountId());
            userAuthInfo.setUname(userEntity.getUserName());
            UserAuthEntity userAuthEntity = cloudAccountDao.queryUserAuthInfoByName(userAuthInfo);
            if (null != userAuthEntity) {
                UserEntity user = new UserEntity();
                user.setUserId(userAuthEntity.getUid());
                user.setUserName(userAuthEntity.getUname());
                aliCloudResponse.setUserEntity(user);
            } else {
                aliCloudResponse.setUserEntity(queryUser(userEntity));
            }
            if (null != aliCloudResponse.getUserEntity()) {
                GetAccountAliasResponse getAccountAliasResponse
                        = aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).getAccountAlias();
                CloudAccountEntity cloudAccount
                        = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
                String endpoint = "https://signin.aliyun.com/";
                if (cloudAccount.isInternational()) {
                    endpoint = "https://signin-intl.aliyun.com/";
                }
                String loginUrl = endpoint
                        + getAccountAliasResponse.getBody().getAccountAlias()
                        + ".onaliyun.com/login.htm?username=" + UserContext.getCurrentUserId() + "&callback=" + cloudAccount.getDestination();
                aliCloudResponse.setLoginUrl(loginUrl);
            }
        } catch (Exception e) {
            log.error("阿里云查询用户是否存在异常", e);
            aliCloudResponse.getHeader().setErrorMsg("阿里云查询用户是否存在异常：" + e.getMessage());
        }
        return aliCloudResponse;
    }

    public UserEntity queryUser(UserEntity userEntity) {
        UserEntity userInfo = null;
        try {
            GetUserRequest getUserRequest = new GetUserRequest();
            getUserRequest.setUserName(userEntity.getUserName());
            GetUserResponse getUserResponse = aliCloudHttpConfig.getAliIamClient(userEntity.getAccountId()).getUser(getUserRequest);
            if (null != getUserResponse.getBody().getUser()) {
                userInfo = new UserEntity();
                userInfo.setUserId(getUserResponse.getBody().getUser().getUserId());
                userInfo.setUserName(getUserResponse.getBody().getUser().getUserName());
            }
        } catch (Exception e) {
            if (e.getMessage().contains("The user does not exist")) {
                if (!userEntity.getUserName().contains("@52tt.com")) {
                    userEntity.setUserName(userEntity.getUserName() + "@52tt.com");
                    return queryUser(userEntity);
                }
            }
        }
        return userInfo;
    }

    /**
     * 查询登录名称
     *
     * @return String
     */
    public String queryUserLoginName(String accountAlias) {
        try {
            return UserContext.getCurrentUserId() + "@" + accountAlias + ".onaliyun.com";
        } catch (Exception e) {
            log.error("查询阿里云用户登录名称异常");
        }
        return StringUtils.EMPTY;
    }

    public CloudAllUserResponse queryUserListByAccount(CloudAccountEntity cloudAccount, String nextMarker) {
        CloudAllUserResponse userListRst = new CloudAllUserResponse();
        List<UserEntity> userList = new ArrayList<>();

        try {
            log.info("阿里云子用户详情列表，nextMarker: {}", nextMarker);

            // 阿里云支持分页获取用户列表，不需要缓存到redis，每次传入nextMarker分别获取
            ListUsersRequest request = new ListUsersRequest();
            request.setMaxItems(20); // 每页20条记录
            if (StringUtils.isNotEmpty(nextMarker)) {
                request.setMarker(nextMarker);
            }

            ListUsersResponse response = aliCloudHttpConfig.getAliIamClient(cloudAccount.getId()).listUsers(request);
            if (response == null || response.getBody() == null) {
                log.info("阿里云用户列表查询结果为空");
                userListRst.setUserList(userList);
                userListRst.setNextMarker(null);
                return userListRst;
            }

            if (response.getBody().getUsers() != null && response.getBody().getUsers().getUser() != null
                && !response.getBody().getUsers().getUser().isEmpty()) {
                for (ListUsersResponseBodyUsersUser user : response.getBody().getUsers().getUser()) {
                    UserEntity userEntity = new UserEntity();
                    userEntity.setUserId(safeGetString(user.getUserId()));
                    userEntity.setUserIn(safeGetString(user.getUserId())); // 阿里云没有UIN概念，使用UserId
                    userEntity.setUserName(safeGetString(user.getUserName()));
                    userEntity.setCreateTime(safeGetString(user.getCreateDate()));
                    userEntity.setRemark(safeGetString(user.getComments()));
                    userEntity.setPhone(safeGetString(user.getMobilePhone()));
                    userEntity.setEmail(safeGetString(user.getEmail()));
                    userEntity.setAccountId(cloudAccount.getDomainId());
                    userList.add(userEntity);
                }
            }

            // 对当前页的用户列表进行详细信息补充
            attachUserInfos(userList, cloudAccount.getId());

            // 设置返回结果
            userListRst.setUserList(userList);
             // 设置下一页的nextMarker
            if (response.getBody().getIsTruncated() != null && response.getBody().getIsTruncated()) {
              userListRst.setNextMarker(response.getBody().getMarker());
            }
            

        } catch (Exception e) {
            log.error("阿里云子用户详情列表异常", e);
            throw new RuntimeException("阿里云子用户详情列表异常：" + e.getMessage());
        }

        return userListRst;
    }

    /**
     * 安全获取字符串值，如果为null则返回空字符串
     */
    private String safeGetString(String value) {
        return value != null ? value : "";
    }

    /**
     * 补充用户详细信息
     */
    private void attachUserInfos(List<UserEntity> userList, String accountId) {
        if (userList == null || userList.isEmpty()) {
            return;
        }
        try {
             var iamClinet=aliCloudHttpConfig.getAliIamClient(accountId);
            // 获取用户信息
            for (int i = 0; i < userList.size(); i++) {
                UserEntity user = userList.get(i);
                if (user.getPolicyList() == null) {
                    user.setPolicyList(new ArrayList<>());
                }

                // 获取最后登录时间和登录IP            
                try {
                    GetUserRequest getUserRequest = new GetUserRequest();
                    getUserRequest.setUserName(user.getUserName());
                    GetUserResponse getUserResponse = iamClinet.getUser(getUserRequest);

                    if (getUserResponse != null && getUserResponse.getBody() != null && getUserResponse.getBody().getUser() != null) {
                        GetUserResponseBodyUser userInfo = getUserResponse.getBody().getUser();
                        user.setLastLoginTime(safeGetString(userInfo.getLastLoginDate()));
                        // user.setLastLoginIp(""); // 阿里云GetUser API不返回登录IP信息
                        // user.setConsoleLogin(StringUtils.isNotEmpty(userInfo.getLastLoginDate()) ? 1L : 0L);
                    }
                } catch (Exception e) {
                    log.error("获取用户登录信息异常: {}", e.getMessage());
                }

                // 1. 获取用户组列表
                try {
                    ListGroupsForUserRequest request = new ListGroupsForUserRequest();
                    request.setUserName(user.getUserName());
                    ListGroupsForUserResponse response = iamClinet.listGroupsForUser(request);

                    if (response != null && response.getBody() != null && response.getBody().getGroups() != null
                        && response.getBody().getGroups().getGroup() != null) {
                        List<UserGroupInfo> groupList = new ArrayList<>();
                        for (ListGroupsForUserResponseBodyGroupsGroup group : response.getBody().getGroups().getGroup()) {
                            UserGroupInfo userGroupInfo = new UserGroupInfo();
                            userGroupInfo.setGroupId(safeGetString(group.getGroupId()));
                            userGroupInfo.setGroupName(safeGetString(group.getGroupName()));
                            userGroupInfo.setRemark(safeGetString(group.getComments()));
                            groupList.add(userGroupInfo);


                            // 2.1 获取用户组权限列表
                            try {
                                ListPoliciesForGroupRequest listPoliciesForGroupRequest = new ListPoliciesForGroupRequest();
                                listPoliciesForGroupRequest.setGroupName(group.getGroupName());
                                ListPoliciesForGroupResponse listPoliciesForGroupResponse =
                                        iamClinet.listPoliciesForGroup(listPoliciesForGroupRequest);
                                if (listPoliciesForGroupResponse != null && listPoliciesForGroupResponse.getBody() != null
                                    && listPoliciesForGroupResponse.getBody().getPolicies() != null
                                    && listPoliciesForGroupResponse.getBody().getPolicies().getPolicy() != null)    
                                    {
                                    List<PermissionEntity> policyList = new ArrayList<>();
                                    for (ListPoliciesForGroupResponseBodyPoliciesPolicy policy : listPoliciesForGroupResponse.getBody().getPolicies().getPolicy()) {
                                        PermissionEntity permission = new PermissionEntity();
                                        permission.setPolicyName(safeGetString(policy.getPolicyName()));
                                        permission.setPolicyType(safeGetString(policy.getPolicyType()));
                                        // permission.setCreateMode(null); // 阿里云可能不支持此字段
                                        permission.setAddTime(safeGetString(policy.getAttachDate()));
                                        permission.setRemark(safeGetString(policy.getDescription()));
                                        permission.setBelongId(group.getGroupId());
                                        permission.setBelongName(group.getGroupName());
                                        permission.setBelongType(Constants.BELONG_TYPE_GROUP);
                                        policyList.add(permission);
                                    }
                                    user.getPolicyList().addAll(policyList);
                                }
                            } catch (Exception e) {
                                log.error("获取用户组权限信息异常: {}", e.getMessage());
                            }
                        }
                        user.setGroupList(groupList);
                    }
                } catch (Exception e) {
                    log.error("获取用户组信息异常: {}", e.getMessage());
                }

                // 2. 获取用户权限列表
                try {
                    ListPoliciesForUserRequest request1 = new ListPoliciesForUserRequest();
                    request1.setUserName(user.getUserName());
                    ListPoliciesForUserResponse response = iamClinet.listPoliciesForUser(request1);

                    if (response != null && response.getBody() != null && response.getBody().getPolicies() != null
                        && response.getBody().getPolicies().getPolicy() != null) {
                        List<PermissionEntity> policyList = new ArrayList<>();
                        for (ListPoliciesForUserResponseBodyPoliciesPolicy policy : response.getBody().getPolicies().getPolicy()) {
                            PermissionEntity permission = new PermissionEntity();
                            permission.setPolicyName(safeGetString(policy.getPolicyName()));
                            permission.setPolicyType(safeGetString(policy.getPolicyType()));
                            // permission.setCreateMode(null); // 阿里云可能不支持此字段
                            permission.setAddTime(safeGetString(policy.getAttachDate()));
                            permission.setRemark(safeGetString(policy.getDescription()));
                            permission.setBelongType(Constants.BELONG_TYPE_USER);
                            policyList.add(permission);
                        }
                        user.getPolicyList().addAll(policyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户权限信息异常: {}", e.getMessage());
                }

                // 3. 获取用户密钥ID列表
                try {
                    ListAccessKeysRequest request2 = new ListAccessKeysRequest();
                    request2.setUserName(user.getUserName());
                    ListAccessKeysResponse response = iamClinet.listAccessKeys(request2);

                    if (response != null && response.getBody() != null && response.getBody().getAccessKeys() != null
                        && response.getBody().getAccessKeys().getAccessKey() != null) {
                        List<PermanentAccessKeyEntity> accessKeyList = new ArrayList<>();
                        for (ListAccessKeysResponseBodyAccessKeysAccessKey accessKey : response.getBody().getAccessKeys().getAccessKey()) {
                            PermanentAccessKeyEntity accessKeyEntity = new PermanentAccessKeyEntity();
                            accessKeyEntity.setAccess(safeGetString(accessKey.getAccessKeyId()));
                            accessKeyEntity.setStatus(safeGetString(accessKey.getStatus()));
                            accessKeyEntity.setCreateTime(safeGetString(accessKey.getCreateDate()));
                            accessKeyEntity.setDescription(""); // 阿里云可能不支持描述字段
                            accessKeyEntity.setAccountId(accountId);
                            try {
                                GetAccessKeyLastUsedRequest getAccessKeyLastUsedRequest = new GetAccessKeyLastUsedRequest();
                                getAccessKeyLastUsedRequest.setUserAccessKeyId(accessKey.getAccessKeyId());
                                GetAccessKeyLastUsedResponse getAccessKeyLastUsedResponse = iamClinet.getAccessKeyLastUsed(getAccessKeyLastUsedRequest);

                                if (getAccessKeyLastUsedResponse != null && getAccessKeyLastUsedResponse.getBody() != null
                                    && getAccessKeyLastUsedResponse.getBody().getAccessKeyLastUsed() != null) {
                                    accessKeyEntity.setLastUsedDate(safeGetString(getAccessKeyLastUsedResponse.getBody().getAccessKeyLastUsed().getLastUsedDate()));
                                }
                            } catch (Exception e) {
                                log.error("获取用户密钥最后使用时间异常: {}", e.getMessage());
                                accessKeyEntity.setLastUsedDate("");
                            }
                            accessKeyList.add(accessKeyEntity);
                        }
                        user.setAccessKeyList(accessKeyList);
                    }
                } catch (Exception e) {
                    log.error("获取用户密钥信息异常: {}", e.getMessage());
                }

                userList.set(i, user);
            }
        } catch (Exception e) {
            log.error("阿里云完善用户详细信息异常", e);
        }
    }

}
