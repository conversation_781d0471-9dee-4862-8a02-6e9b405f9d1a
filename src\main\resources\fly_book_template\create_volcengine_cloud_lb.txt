{
  "config": {
    "wide_screen_mode": true,
    "enable_forward": false,
    "update_multi": true
  },
  "header": {
    "template": "#{#color}",
    "title": {
      "tag": "plain_text",
      "content": "【#{#type}】创建#{#result}"
    }
  },
  "elements": [
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**🗳云商名称：**\n#{#cloud}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**📝云商类型：**\n火山云"
          }
        }
      ]
    },
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**🗳所属地区：**\n#{#region}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**📝企业项目：**\n#{#project}"
          }
        }
      ]
    },
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**🗳LB名称：**\n#{#lb_name}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**📝LB类型：**\n#{#lb_type}"
          }
        }
      ]
    },
    #{#zone_flavor}
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**🗳网络类型：**\n#{#net_type}"
          }
        }
        #{#band_width}
      ]
    },
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**🗳所属VPC：**\n#{#vpc}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**📝所属子网：**\n#{#subnet}"
          }
        }
      ]
    },
    #{#cluster_istio}
    {
      "tag": "div",
      "fields": [
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**🗳实例ID：**\n#{#lb_id}"
          }
        },
        {
          "is_short": true,
          "text": {
            "tag": "lark_md",
            "content": "**📝创建时间：**\n#{#createTime}"
          }
        }
      ]
    },
    {
          "tag": "div",
          "fields": [
            {
              "is_short": true,
              "text": {
                "tag": "lark_md",
                "content": "**🗳需求提出人：**\n#{#username}"
              }
            }
          ]
        }
    #{#error}
  ]
}
