package com.tt.cloud.config;

import com.huaweicloud.sdk.cce.v3.CceClient;
import com.huaweicloud.sdk.cce.v3.region.CceRegion;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.GlobalCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.core.http.HttpConfig;
import com.huaweicloud.sdk.eip.v3.EipClient;
import com.huaweicloud.sdk.elb.v3.ElbClient;
import com.huaweicloud.sdk.elb.v3.region.ElbRegion;
import com.huaweicloud.sdk.eps.v1.EpsClient;
import com.huaweicloud.sdk.eps.v1.region.EpsRegion;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.vpc.v2.VpcClient;
import com.huaweicloud.sdk.vpc.v2.region.VpcRegion;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.CacheUtil;
import com.tt.cloud.util.EncryptUtils;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 15:02
 */
@Slf4j
@Component
public class HuaweiCloudHttpConfig {
    @Resource
    private CloudAccountDao cloudAccountDao;

    public VpcClient getHuaweiVpcClient(String accountId, String region) {
        String cacheKey = "Huawei_VpcClient_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (VpcClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        ICredential auth = new BasicCredentials()
                .withAk(cloudAccount.getSecretId())
                .withSk(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        VpcClient vpcClient = VpcClient.newBuilder()
                .withCredential(auth)
                .withRegion(VpcRegion.valueOf(region))
                .build();
        CacheUtil.put(cacheKey, vpcClient, 24 * 60 * 60);
        return vpcClient;
    }

    public CceClient getHuaweiCceClient(String accountId, String region) {
        String cacheKey = "Huawei_CceClient_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (CceClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        ICredential auth = new BasicCredentials()
                .withAk(cloudAccount.getSecretId())
                .withSk(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        CceClient cceClient =  CceClient.newBuilder()
                .withCredential(auth)
                .withRegion(CceRegion.valueOf(region))
                .build();
        CacheUtil.put(cacheKey, cceClient, 24 * 60 * 60);
        return cceClient;
    }

    public EipClient getHuaweiEipV3Client(String accountId, String region) {
        String cacheKey = "Huawei_EipV3Client_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (EipClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        ICredential auth = new BasicCredentials()
                .withAk(cloudAccount.getSecretId())
                .withSk(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        EipClient eipClient = EipClient.newBuilder()
                .withCredential(auth)
                .withRegion(VpcRegion.valueOf(region))
                .build();
        CacheUtil.put(cacheKey, eipClient, 24 * 60 * 60);
        return eipClient;
    }

    public com.huaweicloud.sdk.eip.v2.EipClient getHuaweiEipV2Client(String accountId, String region) {
        String cacheKey = "Huawei_EipV2Client_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (com.huaweicloud.sdk.eip.v2.EipClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        ICredential auth = new BasicCredentials()
                .withAk(cloudAccount.getSecretId())
                .withSk(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        com.huaweicloud.sdk.eip.v2.EipClient eipClient = com.huaweicloud.sdk.eip.v2.EipClient.newBuilder()
                .withCredential(auth)
                .withRegion(VpcRegion.valueOf(region))
                .build();
        CacheUtil.put(cacheKey, eipClient, 24 * 60 * 60);
        return eipClient;
    }

    public EpsClient getHuaweiEpsClient(String accountId) {
        String cacheKey = "Huawei_EpsClient_" + accountId;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (EpsClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        ICredential auth = new GlobalCredentials()
                .withAk(cloudAccount.getSecretId())
                .withSk(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        EpsClient epsClient = EpsClient.newBuilder()
                .withCredential(auth)
                .withRegion(EpsRegion.valueOf("cn-north-4"))
                .build();
        CacheUtil.put(cacheKey, epsClient, 365 * 24 * 60 * 60);
        return epsClient;
    }

    public IamClient getHuaweiIamClient(String accountId) {
        String cacheKey = "Huawei_IamClient_" + accountId;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (IamClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        HttpConfig config = HttpConfig.getDefaultHttpConfig();
        config.withIgnoreSSLVerification(true);
        ICredential auth = new GlobalCredentials()
                .withAk(cloudAccount.getSecretId())
                .withSk(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()))
                .withDomainId(cloudAccount.getDomainId());
        IamClient iamClient = IamClient.newBuilder()
                .withCredential(auth)
                .withEndpoint("https://iam.myhuaweicloud.com")
                .withHttpConfig(config).build();
        CacheUtil.put(cacheKey, iamClient, 24 * 60 * 60);
        return iamClient;
    }

    public ElbClient getHuaweiElbV3Client(String accountId, String region) {
        String cacheKey = "Huawei_ElbV3Client_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (ElbClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        ICredential auth = new BasicCredentials()
                .withAk(cloudAccount.getSecretId())
                .withSk(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        ElbClient elbClient = ElbClient.newBuilder()
                .withCredential(auth)
                .withRegion(ElbRegion.valueOf(region))
                .build();
        CacheUtil.put(cacheKey, elbClient, 24 * 60 * 60);
        return elbClient;
    }

    public com.huaweicloud.sdk.elb.v2.ElbClient getHuaweiElbV2Client(String accountId, String region) {
        String cacheKey = "Huawei_ElbV2Client_" + accountId + "_" + region;
        Object object = CacheUtil.get(cacheKey);
        if (null != object) {
            return (com.huaweicloud.sdk.elb.v2.ElbClient) object;
        }
        CloudAccountEntity cloudAccount
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        ICredential auth = new BasicCredentials()
                .withAk(cloudAccount.getSecretId())
                .withSk(EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccount.getSecretKey()));
        com.huaweicloud.sdk.elb.v2.ElbClient elbClient = com.huaweicloud.sdk.elb.v2.ElbClient.newBuilder()
                .withCredential(auth)
                .withRegion(ElbRegion.valueOf(region))
                .build();
        CacheUtil.put(cacheKey, elbClient, 24 * 60 * 60);
        return elbClient;
    }

}
