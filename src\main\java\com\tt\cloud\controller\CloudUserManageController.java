package com.tt.cloud.controller;

import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.CloudAccountRequest;
import com.tt.cloud.bean.UserEntity;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.handler.ali.AliCloudUserHandler;
import com.tt.cloud.handler.aws.AwsCloudUserHandler;
import com.tt.cloud.handler.huawei.HuaweiCloudUserHandler;
import com.tt.cloud.handler.tencent.TencentCloudUserHandler;
import java.util.Optional;
import javax.annotation.Resource;

import com.tt.cloud.handler.volcengine.VolcEngineCloudUserHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公有云用户管理
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 11:04
 */
@Slf4j
@RestController
@RequestMapping("/rest/v1")
public class CloudUserManageController {
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private HuaweiCloudUserHandler huaweiCloudUserHandler;
    @Resource
    private TencentCloudUserHandler tencentCloudUserHandler;
    @Resource
    private AliCloudUserHandler aliCloudUserHandler;
    @Resource
    private AwsCloudUserHandler awsCloudUserHandler;
    @Resource
    private VolcEngineCloudUserHandler volcEngineCloudUserHandler;
    @Resource
    private com.tt.cloud.handler.ks.KSCloudUserHandler ksCloudUserHandler;

    /**
     * 查询用户是否在对应云存在
     *
     * @param userEntity userEntity
     * @return Object
     */
    @GetMapping("/user/exists")
    public Object queryUserExists(UserEntity userEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(userEntity.getAccountId());
        userEntity.setUserName(UserContext.getUserInfoThreadLocal().getUserId());
        if (Constants.HUAWEI_CLOUD.equals(cloudAccount.getCloudType())) {
            return huaweiCloudUserHandler.queryUserExists(userEntity);
        } else if (Constants.TENCENT_CLOUD.equals(cloudAccount.getCloudType())) {
            return tencentCloudUserHandler.queryUserExists(userEntity);
        } else if (Constants.ALI_CLOUD.equals(cloudAccount.getCloudType())) {
            return aliCloudUserHandler.queryUserExists(userEntity);
        } else if (Constants.AWS_CLOUD.equals(cloudAccount.getCloudType())) {
            return awsCloudUserHandler.queryUserExists(userEntity);
        } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccount.getCloudType())) {
            return volcEngineCloudUserHandler.queryUserExists(userEntity);
        } else if (Constants.KS_CLOUD.equals(cloudAccount.getCloudType())) {
            return ksCloudUserHandler.queryUserExists(userEntity);
        }
        return Optional.empty();
    }


    /**
     *  查询所有子用户详情信息
     *
     * @param cloudAccountRequest cloudAccountRequest
     * @return Object
     */
    @GetMapping("/user/listByAccountId")
    public Object queryUserListByAccountId(CloudAccountRequest req) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(req.getId());
        if (Constants.HUAWEI_CLOUD.equals(cloudAccount.getCloudType())) {
            return huaweiCloudUserHandler.queryUserListByAccount(cloudAccount);
        } else if (Constants.TENCENT_CLOUD.equals(cloudAccount.getCloudType())) {
            return tencentCloudUserHandler.queryUserListByAccount(cloudAccount);
        } else if (Constants.ALI_CLOUD.equals(cloudAccount.getCloudType())) {
            return aliCloudUserHandler.queryUserListByAccount(cloudAccount);
        } else if (Constants.AWS_CLOUD.equals(cloudAccount.getCloudType())) {
            return awsCloudUserHandler.queryUserListByAccount(cloudAccount);
        } else if (Constants.VOLC_ENGINE_CLOUD.equals(cloudAccount.getCloudType())) {
            return volcEngineCloudUserHandler.queryUserListByAccount(cloudAccount);
        } else if (Constants.KS_CLOUD.equals(cloudAccount.getCloudType())) {
            return ksCloudUserHandler.queryUserListByAccount(cloudAccount);
        }
        return Optional.empty();
    }

}
