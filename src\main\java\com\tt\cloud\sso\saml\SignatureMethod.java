package com.tt.cloud.sso.saml;

import lombok.extern.slf4j.Slf4j;
import org.opensaml.Configuration;
import org.opensaml.xml.security.credential.Credential;
import org.opensaml.xml.signature.SignableXMLObject;
import org.opensaml.xml.signature.SignatureConstants;
import org.opensaml.xml.signature.Signer;

@Slf4j
public class SignatureMethod {

    public static void signObject(SignableXMLObject signableXMLObject, Credential credential)
            throws Exception {
        org.opensaml.xml.signature.Signature signature = SAMLResponse.buildSAMLObject(org.opensaml.xml.signature.Signature.class);
        signature.setSigningCredential(credential);
        signature.setSignatureAlgorithm(Configuration.getGlobalSecurityConfiguration().getSignatureAlgorithmURI(credential));
        signature.setCanonicalizationAlgorithm(SignatureConstants.ALGO_ID_C14N_EXCL_OMIT_COMMENTS);
        signableXMLObject.setSignature(signature);
        Configuration.getMarshallerFactory().getMarshaller(signableXMLObject).marshall(signableXMLObject);
        Signer.signObject(signature);
    }
}
