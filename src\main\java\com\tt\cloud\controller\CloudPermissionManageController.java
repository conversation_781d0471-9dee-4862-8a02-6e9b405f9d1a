package com.tt.cloud.controller;

import software.amazon.awssdk.services.sts.model.PolicyDescriptorType;
import com.tt.cloud.bean.AwsCloudResponse;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.PermissionEntity;
import com.tt.cloud.bean.UserAuthEntity;
import com.tt.cloud.context.UserContext;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.handler.AbstractCloudPermissionHandler;
import com.tt.cloud.operatelog.OperateLog;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 云权限管理控制層
 *
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/20 18:20
 */
@Slf4j
@RestController
@RequestMapping("/rest/v1/permission")
public class CloudPermissionManageController {
    @Resource
    private CloudAccountDao cloudAccountDao;
    @Resource
    private SsoController ssoController;
    @Resource
    private List<AbstractCloudPermissionHandler> cloudPermissionHandler;

    /**
     * 查询当前登录用户关联用户组权限列表
     *
     * @param permissionEntity permissionEntity
     * @return Object
     */
    @GetMapping("/current/user/list")
    public Object queryCurrentUserPermissions(PermissionEntity permissionEntity) {
        UserAuthEntity userAuthEntity = new UserAuthEntity();
        userAuthEntity.setAccountId(permissionEntity.getAccountId());
        userAuthEntity.setUname(UserContext.getCurrentUserId());
        return cloudAccountDao.queryUserAuthNameList(userAuthEntity);
    }

    /**
     * 查询用户关联用户组权限列表
     *
     * @param permissionEntity permissionEntity
     * @return Object
     */
    @GetMapping("/user/list")
    public Object queryUserPermissions(PermissionEntity permissionEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
        return cloudPermissionHandler.stream()
                .filter(handler -> handler.supports(cloudAccount.getCloudType()))
                .findFirst().map(handler -> handler.queryUserPermissions(permissionEntity));
    }

    /**
     * 查询aws用户权限个数
     *
     * @param permissionEntity permissionEntity
     * @return Object
     */
    @GetMapping("/aws/user/permission/count")
    public Object queryAwsUserPermissionCount(PermissionEntity permissionEntity) {
        AwsCloudResponse awsCloudResponse = new AwsCloudResponse();
        try {
            List<UserAuthEntity> userAuthList = getUserAuthEntities(permissionEntity, awsCloudResponse);
            if (userAuthList.size() <= 10) {
                log.info("aws用户权限小于10，直接拼接登录URL");
                List<PolicyDescriptorType> policyArns = new ArrayList<>();
                for (UserAuthEntity userAuth : userAuthList) {
                    PolicyDescriptorType policyDescriptorType = PolicyDescriptorType.builder()
                            .arn(userAuth.getAuthId())
                            .build();
                    policyArns.add(policyDescriptorType);
                }
                permissionEntity.setPolicyArns(policyArns);
                String loginPath = ssoController.queryAwsLoginPath(permissionEntity).getLogInPath();
                awsCloudResponse.setLogInPath(loginPath);
                return awsCloudResponse;
            }
        } catch (Exception e) {
            log.error("查询aws用户权限个数异常", e);
            awsCloudResponse.getHeader().setErrorMsg("查询aws用户权限个数异常: " + e.getMessage());
        }
        return awsCloudResponse;
    }

    private List<UserAuthEntity> getUserAuthEntities(PermissionEntity permissionEntity,
            AwsCloudResponse awsCloudResponse) {
        UserAuthEntity userAuthEntity = new UserAuthEntity();
        userAuthEntity.setAccountId(permissionEntity.getAccountId());
        userAuthEntity.setUname(UserContext.getCurrentUserId());
        List<UserAuthEntity> userAuthList = cloudAccountDao.queryUserAuthList(userAuthEntity);
        List<PermissionEntity> permissionList = new ArrayList<>();
        for (UserAuthEntity userAuth : userAuthList) {
            PermissionEntity permission = new PermissionEntity();
            permission.setPolicyName(userAuth.getAuth());
            permission.setArn(userAuth.getAuthId());
            permissionList.add(permission);
        }
        awsCloudResponse.setPermissionList(permissionList);
        return userAuthList;
    }

    /**
     * 查询权限详情
     *
     * @param cloud cloud
     * @param permissionEntity permissionEntity
     * @return Object
     */
    @GetMapping("/version/{cloud}")
    public Object queryPermissionVersion(@PathVariable(name = "cloud") String cloud,
            PermissionEntity permissionEntity) {
        return cloudPermissionHandler.stream()
                .filter(handler -> handler.supports(cloud))
                .findFirst().map(handler -> handler.queryPermissionVersion(permissionEntity));
    }


    /**
     * 查询全部权限列表
     *
     * @param permissionEntity permissionEntity
     * @return Object
     */
    @GetMapping("/list")
    public Object queryPermissionList(PermissionEntity permissionEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
        return cloudPermissionHandler.stream()
                .filter(handler -> handler.supports(cloudAccount.getCloudType()))
                .findFirst().map(handler -> handler.queryPermissionList(permissionEntity));
    }


    /**
     * 给用户组授权
     *
     * @param permissionEntity permissionEntity
     * @return Object
     */
    @PostMapping("/group/permissions")
    @OperateLog(operateDesc = "云商权限", businessName = "给用户组授权")
    public Object grantGroupPermissions(@RequestBody PermissionEntity permissionEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
        return cloudPermissionHandler.stream()
                .filter(handler -> handler.supports(cloudAccount.getCloudType()))
                .findFirst().map(handler -> handler.grantGroupPermissions(permissionEntity));
    }

    /**
     * 给用户授权
     *
     * @param permissionEntity permissionEntity
     * @return Object
     */
    @PostMapping("/user/permissions")
    @OperateLog(operateDesc = "云商权限", businessName = "给用户授权")
    public Object grantUserPermissions(@RequestBody PermissionEntity permissionEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
        return cloudPermissionHandler.stream()
                .filter(handler -> handler.supports(cloudAccount.getCloudType()))
                .findFirst().map(handler -> handler.grantUserPermissions(permissionEntity));
    }

    /**
     * 查询用户组所有项目服务权限列表
     *
     * @param permissionEntity permissionEntity
     * @return Object
     */
    @GetMapping("/group/permissions")
    public Object queryGroupPermissionList(PermissionEntity permissionEntity) {
        CloudAccountEntity cloudAccount = cloudAccountDao.queryCloudAccountInfoById(permissionEntity.getAccountId());
        return cloudPermissionHandler.stream()
                .filter(handler -> handler.supports(cloudAccount.getCloudType()))
                .findFirst().map(handler -> handler.queryGroupPermissionList(permissionEntity));
    }

}
