package com.tt.cloud.bean;

import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2021/9/14 14:52
 */
@Getter
@Setter
public class UserInfo {
    private String userId;
    private String userName;
    private String isSuperUser;
    private String isSystemUser;
    private String email;
    private String userGroup;
    private Date createTime;
    private String state;
    private List<String> authCodeList;
    private String authorization;

    @Override
    public String toString() {
        return "UserEntity{" +
                "userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", isSuperUser='" + isSuperUser + '\'' +
                ", isSystemUser='" + isSystemUser + '\'' +
                ", email='" + email + '\'' +
                ", userGroup='" + userGroup + '\'' +
                ", state='" + state + '\'' +
                '}';
    }
}
