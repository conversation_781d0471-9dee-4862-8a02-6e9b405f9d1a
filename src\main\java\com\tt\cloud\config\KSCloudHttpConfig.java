package com.tt.cloud.config;

import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.dao.CloudAccountDao;
import com.tt.cloud.util.EncryptUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import common.Credential;

import javax.annotation.Resource;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2022/4/15 15:02
 */
@Slf4j
@Component
public class KSCloudHttpConfig {
    @Resource
    private CloudAccountDao cloudAccountDao;

       public Credential GetCredential(String accountId, String region) {
        if (StringUtils.isEmpty(region)) {
            region = "cn-shanghai-2";
        }
        return getCredential(accountId, region);
    }

    private Credential getCredential(String accountId, String region) {
        CloudAccountEntity cloudAccountEntity
                = cloudAccountDao.queryCloudAccountInfoById(accountId);
        return new Credential(cloudAccountEntity.getSecretId(),
                EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccountEntity.getSecretKey()),region);
    }


    // public Iam20210801Api getVolcEngineIam20210801Api(String accountId, String region) {
    //     String cacheKey = "Volc_Engine_Iam20210801Api_" + accountId + "_" + region;
    //     Object object = CacheUtil.get(cacheKey);
    //     if (null != object) {
    //         return (Iam20210801Api) object;
    //     }
    //     if (StringUtils.isEmpty(region)) {
    //         region = "cn-beijing";
    //     }
    //     ApiClient apiClient = getApiClient(accountId, region);
    //     Iam20210801Api api = new Iam20210801Api(apiClient);
    //     CacheUtil.put(cacheKey, api, 24 * 60 * 60);
    //     return api;
    // }

    // private ApiClient getApiClient(String accountId, String region) {
    //     CloudAccountEntity cloudAccountEntity
    //             = cloudAccountDao.queryCloudAccountInfoById(accountId);
    //     return new ApiClient()
    //             .setCredentials(Credentials.getCredentials(cloudAccountEntity.getSecretId(),
    //                     EncryptUtils.aes256ECBPkcs7PaddingDecrypt(cloudAccountEntity.getSecretKey())))
    //             .setRegion(region);
    // }

 



}
