package com.tt.cloud.thread;

import com.huaweicloud.sdk.cce.v3.model.Cluster;
import com.huaweicloud.sdk.cce.v3.model.ListClustersRequest;
import com.huaweicloud.sdk.cce.v3.model.ListClustersResponse;
import com.huaweicloud.sdk.elb.v3.ElbClient;
import com.huaweicloud.sdk.elb.v3.model.Flavor;
import com.huaweicloud.sdk.elb.v3.model.ListFlavorsRequest;
import com.huaweicloud.sdk.elb.v3.model.ListFlavorsResponse;
import com.huaweicloud.sdk.eps.v1.EpsClient;
import com.huaweicloud.sdk.eps.v1.model.EpDetail;
import com.huaweicloud.sdk.eps.v1.model.ListEnterpriseProjectRequest;
import com.huaweicloud.sdk.eps.v1.model.ListEnterpriseProjectResponse;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListRegionsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListRegionsResponse;
import com.huaweicloud.sdk.iam.v3.model.Region;
import com.huaweicloud.sdk.vpc.v2.VpcClient;
import com.huaweicloud.sdk.vpc.v2.model.ListSubnetsRequest;
import com.huaweicloud.sdk.vpc.v2.model.ListSubnetsResponse;
import com.huaweicloud.sdk.vpc.v2.model.ListVpcsRequest;
import com.huaweicloud.sdk.vpc.v2.model.ListVpcsResponse;
import com.huaweicloud.sdk.vpc.v2.model.Subnet;
import com.huaweicloud.sdk.vpc.v2.model.Vpc;
import com.tt.cloud.bean.CloudAccountEntity;
import com.tt.cloud.bean.v2.LBItem;
import com.tt.cloud.bean.v2.LbTypeEnum;
import com.tt.cloud.bean.v2.NetTypeEnum;
import com.tt.cloud.bean.v2.ResourceTypeEnum;
import com.tt.cloud.config.HuaweiCloudHttpConfig;
import com.tt.cloud.constant.Constants;
import com.tt.cloud.notice.FlyBookService;
import com.tt.cloud.util.CommonUtil;
import com.tt.cloud.util.Utils;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2024/2/28 11:19
 */
@Slf4j
public class HuaweiLBCreateNoticeThread implements Runnable {
    private final LBItem lbItem;
    private final CloudAccountEntity cloudAccountEntity;
    private final FlyBookService flyBookService;
    private final ApplicationContext applicationContext;
    private final HuaweiCloudHttpConfig huaweiCloudHttpConfig;

    public HuaweiLBCreateNoticeThread(LBItem lbItem, CloudAccountEntity cloudAccountEntity,
            FlyBookService flyBookService, ApplicationContext applicationContext,
            HuaweiCloudHttpConfig huaweiCloudHttpConfig) {
        this.lbItem = lbItem;
        this.cloudAccountEntity = cloudAccountEntity;
        this.flyBookService = flyBookService;
        this.applicationContext = applicationContext;
        this.huaweiCloudHttpConfig = huaweiCloudHttpConfig;
    }

    @Override
    public void run() {
        try {
            String content = Utils.readTxtFile("/fly_book_template/create_huawei_cloud_lb.txt");
            Map<String, Object> messageParam = new HashMap<>();
            fillTitle(messageParam);
            messageParam.put("cloud", cloudAccountEntity.getDomainId() + "/" + cloudAccountEntity.getName());
            fillRegion(messageParam);
            fillProject(messageParam);
            messageParam.put("lb_name", lbItem.getLb_name());
            if (ResourceTypeEnum.HOST_LB.getResourceType().equals(lbItem.getResource_type())) {
                messageParam.put("type", "主机LB");
                messageParam.put("cluster_istio", StringUtils.EMPTY);
            } else {
                messageParam.put("type", "容器LB");
                fillCluster(messageParam);
            }
            dealLBType(messageParam);
            dealNetType(messageParam);
            fill_VPC_Subnet(messageParam);
            messageParam.put("lb_id", StringUtils.isNotEmpty(lbItem.getLb_id()) ? lbItem.getLb_id() : "-");
            messageParam.put("createTime", new SimpleDateFormat(Constants.DATA_FORMAT).format(new Date()));
            messageParam.put("username", lbItem.getUsername());
            content = Utils.getParseValue(applicationContext, messageParam, content);
            flyBookService.sendMessage(lbItem.getEmail(), content);
        } catch (Exception e) {
            log.error("发送华为云LB创建成功消息异常", e);
        }
    }

    private void fillTitle(Map<String, Object> messageParam) {
        if (Constants.SUCCESS.equals(lbItem.getResultCode())) {
            messageParam.put("color", "green");
            messageParam.put("result", "成功");
            messageParam.put("error", StringUtils.EMPTY);
        } else {
            messageParam.put("color", "red");
            messageParam.put("result", "失败");
            String error = ",\n"
                    + "{\n"
                    + "\"tag\": \"div\",\n"
                    + "\"fields\": [\n"
                    + "{\n"
                    + "\"is_short\": true,\n"
                    + "\"text\": {\n"
                    + "\"tag\": \"lark_md\",\n"
                    + "\"content\": \"**\uD83D\uDDF3错误信息：**\\n" + lbItem.getResultMsg() + "\"\n"
                    + "}\n"
                    + "}\n"
                    + "]\n"
                    + "}";
            messageParam.put("error", error);
        }
    }

    private void fillCluster(Map<String, Object> messageParam) {
        messageParam.put("cluster_istio", "{\n"
                + "      \"tag\": \"div\",\n"
                + "      \"fields\": [\n"
                + "        {\n"
                + "          \"is_short\": true,\n"
                + "          \"text\": {\n"
                + "            \"tag\": \"lark_md\",\n"
                + "            \"content\": \"**\uD83D\uDDF3集群名称：**\\n" + getCluster_name() + "\"\n"
                + "          }\n"
                + "        },\n"
                + "        {\n"
                + "          \"is_short\": true,\n"
                + "          \"text\": {\n"
                + "            \"tag\": \"lark_md\",\n"
                + "            \"content\": \"**\uD83D\uDCDD是否istio网关：**\\n" + ("1".equals(lbItem.getIstio_net()) ? "是": "否") + "\"\n"
                + "          }\n"
                + "        }\n"
                + "      ]\n"
                + "    },");
    }

    private String getCluster_name() {
        String cluster_name = StringUtils.EMPTY;
        ListClustersResponse listClustersResponse =
                huaweiCloudHttpConfig.getHuaweiCceClient(cloudAccountEntity.getId(),
                        lbItem.getRegion_id()).listClusters(new ListClustersRequest());
        Optional<Cluster> clusterOptional = listClustersResponse.getItems().stream()
                .filter(cluster -> cluster.getMetadata().getUid().equals(lbItem.getCluster_id())).findFirst();
        if (clusterOptional.isPresent()) {
            cluster_name = clusterOptional.get().getMetadata().getName();
        }
        return cluster_name;
    }

    private void fill_VPC_Subnet(Map<String, Object> messageParam) {
        VpcClient vpcClient = huaweiCloudHttpConfig.getHuaweiVpcClient(
                cloudAccountEntity.getId(),
                lbItem.getRegion_id());
        ListVpcsResponse listVpcsResponse = vpcClient.listVpcs(new ListVpcsRequest());
        Optional<Vpc> vpcOptional = listVpcsResponse.getVpcs().stream().filter(vpc -> vpc.getId().equals(lbItem.getVpc_id())).findFirst();
        vpcOptional.ifPresent(
                vpc -> messageParam.put("vpc", vpc.getName() + "【" + vpc.getCidr() + "】"));
        ListSubnetsRequest listSubnetsRequest = new ListSubnetsRequest();
        listSubnetsRequest.setVpcId(lbItem.getVpc_id());
        ListSubnetsResponse listSubnetsResponse = vpcClient.listSubnets(listSubnetsRequest);
        Optional<Subnet> subnetOptional = listSubnetsResponse.getSubnets().stream()
                .filter(subnet -> subnet.getNeutronSubnetId().equals(lbItem.getSubnet_id())).findFirst();
        subnetOptional.ifPresent(subnet -> messageParam.put("subnet",
                subnet.getName() + "【" + subnet.getCidr() + "】"));
    }

    private void fillProject(Map<String, Object> messageParam) {
        EpsClient epsClient = huaweiCloudHttpConfig
                .getHuaweiEpsClient(cloudAccountEntity.getId());
        ListEnterpriseProjectRequest listEnterpriseProjectRequest = new ListEnterpriseProjectRequest();
        ListEnterpriseProjectResponse listEnterpriseProjectResponse
                = epsClient.listEnterpriseProject(listEnterpriseProjectRequest);
        Optional<EpDetail> projectOptional = listEnterpriseProjectResponse.getEnterpriseProjects()
                .stream().filter(project -> project.getId().equals(lbItem.getProject_id())).findFirst();
        projectOptional.ifPresent(
                epDetail -> messageParam.put("project", epDetail.getName()));
    }

    private void fillRegion(Map<String, Object> messageParam) {
        KeystoneListRegionsResponse keystoneListRegionsResponse =
                huaweiCloudHttpConfig.getHuaweiIamClient(cloudAccountEntity.getId())
                        .keystoneListRegions(new KeystoneListRegionsRequest());
        Optional<Region> regionOption = keystoneListRegionsResponse.getRegions().stream()
                .filter(region -> region.getId().equals(lbItem.getRegion_id())).findFirst();
        regionOption.ifPresent(region -> messageParam.put("region", region.getLocales().getZhCn()));
    }

    private void dealLBType(Map<String, Object> messageParam) {
        if (LbTypeEnum.EXCLUSIVE.getLBType().equals(lbItem.getLb_type())) {
            String flavor_name = getFlavor_name();
            messageParam.put("lb_type", "独享型");
            List<String> zoneNames = new ArrayList<>();
            for (String zone_id : lbItem.getZone_ids()) {
                zoneNames.add(CommonUtil.translateToChinese(zone_id.substring(lbItem.getRegion_id().length())));
            }
            String zone_flavor = "\n"
                    + "    {\n"
                    + "      \"tag\": \"div\",\n"
                    + "      \"fields\": [\n"
                    + "        {\n"
                    + "          \"is_short\": true,\n"
                    + "          \"text\": {\n"
                    + "            \"tag\": \"lark_md\",\n"
                    + "            \"content\": \"**\uD83D\uDDF3可用区：**\\n" + StringUtils.join(zoneNames, ",") + "\"\n"
                    + "          }\n"
                    + "        },\n"
                    + "        {\n"
                    + "          \"is_short\": true,\n"
                    + "          \"text\": {\n"
                    + "            \"tag\": \"lark_md\",\n"
                    + "            \"content\": \"**\uD83D\uDCDD机型：**\\n" + flavor_name + "\"\n"
                    + "          }\n"
                    + "        }\n"
                    + "      ]\n"
                    + "    },";
            messageParam.put("zone_flavor", zone_flavor);
        } else {
            messageParam.put("lb_type", "共享型");
            messageParam.put("zone_flavor", StringUtils.EMPTY);
        }
    }

    private String getFlavor_name() {
        String flavor_name = StringUtils.EMPTY;
        ElbClient elbClient = huaweiCloudHttpConfig.getHuaweiElbV3Client(
                cloudAccountEntity.getId(), lbItem.getRegion_id());
        ListFlavorsResponse listFlavorsResponse = elbClient.listFlavors(new ListFlavorsRequest());
        if (StringUtils.isNotEmpty(lbItem.getL7_flavor_id())) {
            Optional<Flavor> flavorOptional = listFlavorsResponse.getFlavors().stream()
                    .filter(flavor -> flavor.getId().equals(lbItem.getL7_flavor_id())).findFirst();
            if (flavorOptional.isPresent()) {
                flavor_name += "应用型：" + Utils.getFlavorName(flavorOptional.get());
            }
        }
        if (StringUtils.isNotEmpty(lbItem.getL4_flavor_id())) {
            Optional<Flavor> flavorOptional = listFlavorsResponse.getFlavors().stream()
                    .filter(flavor -> flavor.getId().equals(lbItem.getL4_flavor_id())).findFirst();
            if (flavorOptional.isPresent()) {
                flavor_name += "网络型：" + Utils.getFlavorName(flavorOptional.get());
            }
        }
        return flavor_name;
    }

    private void dealNetType(Map<String, Object> messageParam) {
        if (NetTypeEnum.OPEN.getNetType().equals(lbItem.getNet_type())) {
            messageParam.put("net_type", "公网");
            String band_width = ",\n"
                    + "        {\n"
                    + "          \"is_short\": true,\n"
                    + "          \"text\": {\n"
                    + "            \"tag\": \"lark_md\",\n"
                    + "            \"content\": \"**\uD83D\uDCDD网络带宽：**\\n" + lbItem.getBandwidth_size() + "\"\n"
                    + "          }\n"
                    + "        }";
            messageParam.put("band_width", band_width);
        } else {
            messageParam.put("net_type", "内网");
            messageParam.put("band_width", StringUtils.EMPTY);
        }
    }
}
