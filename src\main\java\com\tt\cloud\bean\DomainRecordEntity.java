package com.tt.cloud.bean;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> T2717
 * @version 1.0
 * @date 2023/4/23 15:34
 */
@Getter
@Setter
@ToString
public class DomainRecordEntity {
    private String recordId;
    private String recordName;
    private String dnsId;
    private String dnsName;
    private String remark;
    private String lineId;
    private long ttl;
    private long mx;
    private long weight;
    private String type;
    private String value;
    private Date createTime;
    private Date updateTime;
    private String status;
    private String lbId;
    private String lbName;
    private String lbType;
}
